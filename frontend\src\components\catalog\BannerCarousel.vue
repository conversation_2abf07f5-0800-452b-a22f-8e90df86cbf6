<template>
  <div class="banner-carousel">
    <div class="carousel-container" ref="carouselContainer">
      <div
        v-for="(slide, index) in slides"
        :key="index"
        class="carousel-slide"
        :class="{ active: currentSlide === index }"
        :style="{ transform: `translateX(${100 * (index - currentSlide)}%)` }"
        @click="handleSlideClick(slide)"
        style="cursor: pointer;"
      >
        <div class="slide-content" :style="{ backgroundImage: `url(${slide.imageUrl})` }">
          <div class="slide-overlay">
            <h2 class="slide-title">{{ slide.title }}</h2>
            <p class="slide-description">{{ slide.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="carousel-indicators">
      <button
        v-for="(slide, index) in slides"
        :key="index"
        class="indicator-dot"
        :class="{ active: currentSlide === index }"
        @click="goToSlide(index)"
      ></button>
    </div>

    <button v-if="showNavigationButtons" class="carousel-control prev" @click="prevSlide">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="15 18 9 12 15 6"></polyline>
      </svg>
    </button>

    <button v-if="showNavigationButtons" class="carousel-control next" @click="nextSlide">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="9 18 15 12 9 6"></polyline>
      </svg>
    </button>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { MovieCategory } from '@/services/vibixService';
import { useMoviesStore } from '@/store/movies';
import { isMobilePlatform } from '@/utils/platform';

interface CarouselSlide {
  title: string;
  description: string;
  imageUrl: string;
  link?: string;
  isAd?: boolean;
}

export default defineComponent({
  name: 'BannerCarousel',
  setup() {
    // Функция для выбора n случайных элементов из массива
    function getRandomItems<T>(arr: T[], n: number): T[] {
      const result: T[] = [];
      const used = new Set<number>();
      while (result.length < n && used.size < arr.length) {
        const idx = Math.floor(Math.random() * arr.length);
        if (!used.has(idx)) {
          result.push(arr[idx]);
          used.add(idx);
        }
      }
      return result;
    }

    const slides = ref<CarouselSlide[]>([]);
    const currentSlide = ref(0);
    const carouselContainer = ref<HTMLElement | null>(null);
    const autoplayInterval = ref<number | null>(null);
    const loading = ref(true);
    const error = ref<string | null>(null);
    const moviesStore = useMoviesStore();
    const router = useRouter();

    // Проверяем платформу и скрываем кнопки навигации для мобильных устройств
    const showNavigationButtons = ref(!isMobilePlatform());

    // Load featured movies for carousel
    const loadFeaturedMovies = async () => {
      loading.value = true;
      error.value = null;

      try {
        // Получаем популярные фильмы из хранилища (или загружаем, если их нет)
        const movies = await moviesStore.getMoviesByCategory(MovieCategory.POPULAR, 1, 20);

        // Фильтруем только фильмы с картинкой
        const filtered = (movies || []).filter(movie => movie.poster_urls?.original || movie.poster_urls?.w500);

        // Берём 3 случайных фильма
        const randomMovies = getRandomItems(filtered, 3);

        // Формируем слайды
        const movieSlides: CarouselSlide[] = randomMovies.map(movie => ({
          title: movie.name_rus || movie.name_original || '',
          description: movie.description_short || `${movie.type === 'serial' ? 'Сериал' : 'Фильм'} ${movie.year}`,
          imageUrl: movie.poster_urls?.original || movie.poster_urls?.w500 || '',
          link: `/movie/${movie.kp_id ? 'kp' : 'imdb'}/${movie.kp_id || movie.imdb_id}`
        }));

        // Add advertisement slide
        const adSlide: CarouselSlide = {
          title: '@Appss',
          description: '@Appss - ваш глобальный каталог для поиска Telegram Mini Apps, ботов и каналов.',
          imageUrl: 'https://i.ibb.co/XfGj3vpF/5881841765391124399.jpg',
          link: 'https://t.me/appss',
          isAd: true
        };

        // Вставляем рекламу на вторую позицию (index 1)
        if (movieSlides.length >= 2) {
          movieSlides.splice(1, 0, adSlide);
        } else {
          movieSlides.push(adSlide);
        }

        slides.value = movieSlides;
      } catch (err) {
        console.error('Error loading featured movies for carousel:', err);
        error.value = 'Не удалось загрузить фильмы для карусели';

        // Use placeholder slides if API fails
        slides.value = [
          {
            title: 'Популярные фильмы',
            description: 'Смотрите лучшие фильмы и сериалы',
            imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkmM9QDwADgQF/Vry1FAAAAABJRU5ErkJggg=='
          },
          {
            title: 'Новинки',
            description: 'Самые свежие релизы',
            imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkWM9QDwADCAGEFYi4zQAAAABJRU5ErkJggg=='
          },
          {
            title: '@Appss',
            description: '@Appss - ваш глобальный каталог для поиска Telegram Mini Apps, ботов и каналов.',
            imageUrl: 'https://i.ibb.co/XfGj3vpF/5881841765391124399.jpg',
            link: 'https://t.me/appss',
            isAd: true
          },
          {
            title: 'Эксклюзивы',
            description: 'Только у нас',
            imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPivDwADCQGAjPbM3QAAAABJRU5ErkJggg=='
          }
        ];
      } finally {
        loading.value = false;
      }
    };

    // Navigation functions
    const nextSlide = () => {
      currentSlide.value = (currentSlide.value + 1) % slides.value.length;
      resetAutoplay();
    };

    const prevSlide = () => {
      currentSlide.value = (currentSlide.value - 1 + slides.value.length) % slides.value.length;
      resetAutoplay();
    };

    const goToSlide = (index: number) => {
      currentSlide.value = index;
      resetAutoplay();
    };

    // Autoplay functions
    const startAutoplay = () => {
      stopAutoplay();
      autoplayInterval.value = window.setInterval(() => {
        nextSlide();
      }, 5000);
    };

    const stopAutoplay = () => {
      if (autoplayInterval.value !== null) {
        clearInterval(autoplayInterval.value);
        autoplayInterval.value = null;
      }
    };

    const resetAutoplay = () => {
      stopAutoplay();
      startAutoplay();
    };

    // Touch handling
    let touchStartX = 0;
    let touchEndX = 0;

    const handleTouchStart = (e: TouchEvent) => {
      touchStartX = e.changedTouches[0].screenX;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      touchEndX = e.changedTouches[0].screenX;
      handleSwipe();
    };

    const handleSwipe = () => {
      const swipeThreshold = 50;
      if (touchEndX < touchStartX - swipeThreshold) {
        nextSlide(); // Swipe left
      } else if (touchEndX > touchStartX + swipeThreshold) {
        prevSlide(); // Swipe right
      }
    };

    // Переход по клику на слайд
    const handleSlideClick = (slide: CarouselSlide) => {
      if (slide.link) {
        if (slide.isAd) {
          // Для рекламы используем правильный метод открытия ссылок
          const tg = (window as any).Telegram?.WebApp;
          if (tg) {
            // Для t.me ссылок используем openTelegramLink
            if (slide.link.includes('t.me/') || slide.link.includes('telegram.me/')) {
              if (tg.openTelegramLink) {
                tg.openTelegramLink(slide.link);
              } else {
                tg.openLink(slide.link);
              }
            } else {
              // Для внешних ссылок используем openLink
              tg.openLink(slide.link);
            }
          } else {
            // Fallback для не-Telegram окружений
            window.open(slide.link, '_blank');
          }
        } else {
          // Для фильмов используем роутер
          router.push(slide.link);
        }
      }
    };

    onMounted(() => {
      loadFeaturedMovies();
      startAutoplay();

      // Add touch event listeners
      if (carouselContainer.value) {
        carouselContainer.value.addEventListener('touchstart', handleTouchStart, { passive: true });
        carouselContainer.value.addEventListener('touchend', handleTouchEnd, { passive: true });
      }
    });

    onBeforeUnmount(() => {
      stopAutoplay();

      // Remove touch event listeners
      if (carouselContainer.value) {
        carouselContainer.value.removeEventListener('touchstart', handleTouchStart);
        carouselContainer.value.removeEventListener('touchend', handleTouchEnd);
      }
    });

    return {
      slides,
      currentSlide,
      carouselContainer,
      showNavigationButtons,
      nextSlide,
      prevSlide,
      goToSlide,
      handleSlideClick
    };
  }
});
</script>

<style scoped>
.banner-carousel {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
  margin-bottom: 20px;
  border-radius: 12px;
}

.carousel-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.slide-content {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
}

.slide-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
  color: white;
}

.slide-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.slide-description {
  font-size: 14px;
  margin: 0;
  opacity: 0.8;
}

.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
  cursor: pointer;
}

.indicator-dot.active {
  background-color: white;
}

.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
}

.carousel-control.prev {
  left: 10px;
}

.carousel-control.next {
  right: 10px;
}

.carousel-control svg {
  width: 20px;
  height: 20px;
}
</style>
