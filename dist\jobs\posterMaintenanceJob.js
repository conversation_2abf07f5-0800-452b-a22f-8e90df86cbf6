"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cron = __importStar(require("node-cron"));
const MoviePoster_1 = __importDefault(require("../models/MoviePoster"));
const posterCacheService_1 = __importDefault(require("../services/posterCacheService"));
const vibixService_1 = __importStar(require("../services/vibixService"));
class PosterMaintenanceJob {
    // Запуск всех cron задач
    static scheduleJobs() {
        console.log('[POSTER_CACHE] Scheduling poster maintenance jobs...');
        // Запускаем предзагрузку сразу при старте (с небольшой задержкой)
        setTimeout(() => {
            console.log('[POSTER_CACHE] Starting initial category posters preload...');
            this.preloadCategoryPosters().catch(error => {
                console.error('[POSTER_CACHE] Error in initial category preload:', error.message);
            });
        }, 5000); // 5 секунд задержки для инициализации MongoDB
        // Предзагрузка категорий каждые 6 часов
        cron.schedule('0 */6 * * *', () => {
            console.log('[POSTER_CACHE] Starting scheduled category posters preload...');
            this.preloadCategoryPosters().catch(error => {
                console.error('[POSTER_CACHE] Error in scheduled category preload:', error.message);
            });
        });
        // Повтор неудачных каждые 6 часов (со смещением на 3 часа)
        cron.schedule('0 3,9,15,21 * * *', () => {
            console.log('[POSTER_CACHE] Starting scheduled retry of failed posters...');
            this.retryFailedPosters().catch(error => {
                console.error('[POSTER_CACHE] Error in scheduled retry:', error.message);
            });
        });
        // Быстрая очистка просроченного кеша каждые 6 часов (со смещением на 1 час)
        cron.schedule('0 1,7,13,19 * * *', () => {
            console.log('[POSTER_CACHE] Starting scheduled expired cache cleanup...');
            posterCacheService_1.default.cleanupExpiredCache().catch(error => {
                console.error('[POSTER_CACHE] Error in scheduled expired cleanup:', error.message);
            });
        });
        // Полная очистка старого кеша раз в неделю в воскресенье в 2:00
        cron.schedule('0 2 * * 0', () => {
            console.log('[POSTER_CACHE] Starting scheduled full cache cleanup...');
            this.cleanupOldCache().catch(error => {
                console.error('[POSTER_CACHE] Error in scheduled cleanup:', error.message);
            });
        });
        console.log('[POSTER_CACHE] Poster maintenance jobs scheduled successfully');
        console.log('[POSTER_CACHE] Initial preload will start in 5 seconds...');
    }
    // Последовательная предзагрузка по категориям
    static async preloadCategoryPosters() {
        console.log('[POSTER_CACHE] Starting category posters preload...');
        const categories = [
            vibixService_1.MovieCategory.POPULAR,
            vibixService_1.MovieCategory.NEW,
            vibixService_1.MovieCategory.THRILLER,
            vibixService_1.MovieCategory.DRAMA,
            vibixService_1.MovieCategory.ACTION,
            vibixService_1.MovieCategory.SCIFI,
            vibixService_1.MovieCategory.DETECTIVE
        ];
        let totalProcessed = 0;
        let totalCached = 0;
        // Обрабатываем категории ПОСЛЕДОВАТЕЛЬНО
        for (const category of categories) {
            try {
                const categoryStart = Date.now();
                console.log(`[POSTER_CACHE] Preloading posters for category: ${category}`);
                // Получаем 60 фильмов из категории
                const response = await vibixService_1.default.getMoviesByCategory(category, 1, 60);
                if (!response.data || response.data.length === 0) {
                    console.log(`[POSTER_CACHE] No movies found for category ${category}`);
                    continue;
                }
                // Фильтруем фильмы без кеша
                const moviesNeedingPosters = [];
                for (const movie of response.data) {
                    const cached = await MoviePoster_1.default.findOne({
                        vibix_id: movie.id,
                        cache_status: 'found',
                        expires_at: { $gt: new Date() }
                    });
                    if (!cached) {
                        moviesNeedingPosters.push(movie);
                    }
                }
                console.log(`[POSTER_CACHE] Found ${moviesNeedingPosters.length} movies needing posters in ${category}`);
                totalProcessed += moviesNeedingPosters.length;
                // Обрабатываем по одному (задержка уже есть в TMDBService - 300ms)
                for (const movie of moviesNeedingPosters) {
                    try {
                        await posterCacheService_1.default.fetchFromTMDB(movie);
                        totalCached++;
                    }
                    catch (error) {
                        console.error(`[POSTER_CACHE] Error preloading poster for movie ${movie.id}: ${error.message}`);
                    }
                }
                // Короткая пауза между категориями
                const categoryDuration = Date.now() - categoryStart;
                console.log(`[POSTER_CACHE] Completed category ${category}. Processed: ${moviesNeedingPosters.length} movies in ${Math.round(categoryDuration / 1000)}s`);
                await this.sleep(1000);
            }
            catch (error) {
                console.error(`[POSTER_CACHE] Error preloading category ${category}: ${error.message}`);
                // Пауза при ошибке категории
                await this.sleep(5000);
            }
        }
        console.log(`[POSTER_CACHE] Category posters preload completed. Total processed: ${totalProcessed}, cached: ${totalCached}`);
    }
    // Повтор неудачных попыток
    static async retryFailedPosters() {
        console.log('[POSTER_CACHE] Starting retry of failed posters...');
        try {
            // Находим записи с ошибками, которые можно повторить
            const failedPosters = await MoviePoster_1.default.find({
                cache_status: 'error',
                retry_count: { $lt: 3 },
                updatedAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } // старше суток
            }).limit(50);
            console.log(`[POSTER_CACHE] Found ${failedPosters.length} failed posters to retry`);
            let retried = 0;
            for (const poster of failedPosters) {
                try {
                    await posterCacheService_1.default.retryPoster(poster);
                    retried++;
                    // Минимальная задержка между повторами
                    await this.sleep(200);
                }
                catch (error) {
                    console.error(`[POSTER_CACHE] Error retrying poster ${poster.vibix_id}: ${error.message}`);
                }
            }
            console.log(`[POSTER_CACHE] Retry completed. Retried: ${retried} posters`);
        }
        catch (error) {
            console.error('[POSTER_CACHE] Error in retryFailedPosters:', error.message);
        }
    }
    // Очистка старого кеша
    static async cleanupOldCache() {
        console.log('[POSTER_CACHE] Starting cache cleanup...');
        try {
            // Используем новый метод из posterCacheService
            await posterCacheService_1.default.cleanupExpiredCache();
            // Дополнительно удаляем записи с превышенным количеством попыток (старше недели)
            const failedResult = await MoviePoster_1.default.deleteMany({
                cache_status: 'error',
                retry_count: { $gte: 3 },
                updatedAt: { $lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // старше недели
            });
            if (failedResult.deletedCount > 0) {
                console.log(`[POSTER_CACHE] Removed ${failedResult.deletedCount} permanently failed cache entries`);
            }
            // Статистика кеша
            const stats = await this.getCacheStats();
            console.log('[POSTER_CACHE] Cache statistics:', stats);
        }
        catch (error) {
            console.error('[POSTER_CACHE] Error in cleanupOldCache:', error.message);
        }
    }
    // Получение статистики кеша
    static async getCacheStats() {
        try {
            const total = await MoviePoster_1.default.countDocuments();
            const found = await MoviePoster_1.default.countDocuments({ cache_status: 'found' });
            const notFound = await MoviePoster_1.default.countDocuments({ cache_status: 'not_found' });
            const error = await MoviePoster_1.default.countDocuments({ cache_status: 'error' });
            const pending = await MoviePoster_1.default.countDocuments({ cache_status: 'pending' });
            return {
                total,
                found,
                notFound,
                error,
                pending,
                hitRate: total > 0 ? Math.round((found / total) * 100) : 0
            };
        }
        catch (error) {
            console.error('Error getting cache stats:', error.message);
            return null;
        }
    }
    // Утилита для задержки
    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    // Ручной запуск предзагрузки (для тестирования)
    static async manualPreload() {
        console.log('[POSTER_CACHE] Manual preload started...');
        await this.preloadCategoryPosters();
    }
    // Ручной запуск повтора неудачных
    static async manualRetry() {
        console.log('[POSTER_CACHE] Manual retry started...');
        await this.retryFailedPosters();
    }
}
exports.default = PosterMaintenanceJob;
