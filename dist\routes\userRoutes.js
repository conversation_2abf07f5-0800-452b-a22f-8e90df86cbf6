"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const userController_1 = require("../controllers/userController");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const router = express_1.default.Router();
// Apply auth middleware to all routes
router.use(authMiddleware_1.authMiddleware);
// Get current user info
router.get('/me', userController_1.getCurrentUser);
// Get user's referrals
router.get('/referrals', userController_1.getReferrals);
exports.default = router;
