"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupBot = exports.setAdminOnlyMode = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const User_1 = __importDefault(require("../models/User"));
const Task_1 = __importDefault(require("../models/Task"));
// Состояния для добавления заданий
const addTaskStates = new Map();
const userService_1 = require("../services/userService");
// Helper function to safely send replies with error handling
const safeReply = async (ctx, message) => {
    try {
        await await safeReply(ctx, message);
    }
    catch (error) {
        console.error('Error sending reply:', error);
        // Don't throw the error to prevent unhandled rejections
    }
};
// Функция для проверки валидности ObjectId
const isValidObjectId = (id) => {
    return mongoose_1.default.Types.ObjectId.isValid(id);
};
// Глобальная переменная для режима "только для администраторов"
let ADMIN_ONLY_MODE = false;
// Функция для установки режима "только для администраторов"
const setAdminOnlyMode = (mode) => {
    ADMIN_ONLY_MODE = mode;
    console.log(`Admin-only mode set to: ${mode}`);
    // Добавляем дополнительное логирование для отладки
    console.log(`ADMIN_ONLY_MODE global variable is now: ${ADMIN_ONLY_MODE}`);
};
exports.setAdminOnlyMode = setAdminOnlyMode;
// Setup bot commands and handlers
const setupBot = (bot) => {
    // Check if user is admin
    const isAdmin = async (ctx) => {
        if (!ctx.from) {
            return false;
        }
        const chatId = ctx.from.id;
        const user = await User_1.default.findOne({ chat_id: chatId });
        return user?.role === 'admin';
    };
    // Middleware для проверки режима "только для администраторов"
    // ВАЖНО: регистрируем middleware ДО обработчиков команд
    bot.use(async (ctx, next) => {
        // Пропускаем обновления, которые не являются сообщениями
        if (!ctx.message) {
            return next();
        }
        // Если режим выключен, пропускаем все сообщения
        if (!ADMIN_ONLY_MODE) {
            return next();
        }
        // Проверяем, соответствует ли chat_id пользователя значению ADMIN_CHAT_ID
        const isAdminFromEnv = process.env.ADMIN_CHAT_ID && Number(process.env.ADMIN_CHAT_ID) === ctx.from?.id;
        // Если пользователь указан как администратор в переменной окружения, пропускаем его сообщения
        if (isAdminFromEnv) {
            return next();
        }
        // Если режим включен, проверяем, является ли пользователь администратором в базе данных
        const isUserAdmin = await isAdmin(ctx);
        if (isUserAdmin) {
            return next();
        }
        // Если это команда /start, обрабатываем её для регистрации пользователя,
        // но блокируем ответ
        if ('text' in ctx.message && ctx.message.text.startsWith('/start')) {
            // Сохраняем оригинальный метод reply
            const originalReply = ctx.reply;
            // Переопределяем метод reply, чтобы он ничего не делал
            ctx.reply = () => Promise.resolve({});
            // Вызываем next() для обработки команды /start
            await next();
            // Восстанавливаем оригинальный метод reply
            ctx.reply = originalReply;
            // Логируем регистрацию пользователя в режиме "только для администраторов"
            console.log(`User registered in admin-only mode: ${ctx.from?.id} (${ctx.from?.username})`);
            return;
        }
        // Если пользователь не администратор и режим включен, игнорируем сообщение
        // Не отправляем никакого ответа, чтобы не спамить пользователей
        return;
    });
    // Start command
    bot.start(async (ctx) => {
        try {
            const chatId = ctx.from.id;
            const username = ctx.from.username;
            const firstname = ctx.from.first_name;
            const lastname = ctx.from.last_name;
            // Check if user exists
            let user = await User_1.default.findOne({ chat_id: chatId });
            if (!user) {
                // Проверяем, является ли пользователь администратором из переменной окружения
                const isAdminFromEnv = process.env.ADMIN_CHAT_ID && Number(process.env.ADMIN_CHAT_ID) === chatId;
                // Check if this is a referral
                const startParam = ctx.payload;
                let referrerId;
                if (startParam && !isNaN(Number(startParam))) {
                    const potentialReferrerId = Number(startParam);
                    const referrer = await User_1.default.findOne({ chat_id: potentialReferrerId });
                    if (referrer && potentialReferrerId !== chatId) {
                        referrerId = potentialReferrerId;
                    }
                }
                // Create new user using user service
                user = await userService_1.userService.createUser({
                    chat_id: chatId,
                    username,
                    firstname,
                    lastname,
                    role: isAdminFromEnv ? 'admin' : 'user',
                    referrer: referrerId
                });
                // Логируем информацию о создании администратора
                if (isAdminFromEnv) {
                    console.log(`User with chat_id ${chatId} has been created as admin from environment variable`);
                }
                await safeReply(ctx, `Привет, ${firstname}! Добро пожаловать в КиноБот.`);
            }
            else {
                // Update user's last activity
                user.last_activity = new Date();
                if (user.username !== username || user.firstname !== firstname || user.lastname !== lastname) {
                    user.username = username;
                    user.firstname = firstname;
                    user.lastname = lastname;
                }
                // Проверяем, должен ли пользователь быть администратором
                const isAdminFromEnv = process.env.ADMIN_CHAT_ID && Number(process.env.ADMIN_CHAT_ID) === chatId;
                if (isAdminFromEnv && user.role !== 'admin') {
                    user.role = 'admin';
                    console.log(`Existing user with chat_id ${chatId} has been updated to admin from environment variable`);
                }
                await user.save();
                await safeReply(ctx, `С возвращением, ${firstname}!`);
            }
        }
        catch (error) {
            console.error('Error in start command:', error);
            await safeReply(ctx, 'Произошла ошибка при обработке команды.');
        }
    });
    // Help command
    bot.help(async (ctx) => {
        await safeReply(ctx, 'Доступные команды:\n' +
            '/start - Начать работу с ботом\n' +
            '/help - Показать справку\n' +
            '/referral - Получить реферальную ссылку');
    });
    // Admin help command - only for admins
    bot.command('admin', async (ctx) => {
        if (!await isAdmin(ctx)) {
            return; // Silently ignore for non-admins
        }
        const modeStatus = ADMIN_ONLY_MODE ? '✅ Включен' : '❌ Выключен';
        await safeReply(ctx, '🔧 Команды администратора:\n\n' +
            '- Задания:\n' +
            '💡 /addtask - Добавить задание\n' +
            '📋 /listtasks - Список заданий\n' +
            '✅ /activatetask [ID] - Активировать задание\n' +
            '❌ /deactivatetask [ID] - Деактивировать задание\n' +
            '🗑 /deletetask [ID] - Удалить задание\n\n' +
            '- Пользователи:\n' +
            '👑 /makeadmin [ID] - Сделать юзера администратором\n' +
            '👤 /makeuser [ID] - Сделать юзера обычным пользователем\n\n' +
            '- Режим бота:\n' +
            `🔒 Режим бота "Только для администраторов": ${modeStatus}\n` +
            '🔐 /adminonly on - Включить режим "Только для администраторов"\n' +
            '🔓 /adminonly off - Выключить режим "Только для администраторов"\n\n' +
        );
    });
    // Referral command
    bot.command('referral', async (ctx) => {
        try {
            const chatId = ctx.from.id;
            const botUsername = process.env.TELEGRAM_BOT_USERNAME || (await bot.telegram.getMe()).username;
            const referralLink = `https://t.me/${botUsername}?start=${chatId}`;
            await safeReply(ctx, 'Ваша реферальная ссылка:\n\n' +
                `${referralLink}\n\n` +
                'Поделитесь ею с друзьями, чтобы они могли присоединиться к боту через вас.');
        }
        catch (error) {
            console.error('Error in referral command:', error);
            await safeReply(ctx, 'Произошла ошибка при создании реферальной ссылки.');
        }
    });
    // Admin commands
    // Команда для отмены добавления задания
    bot.command('cancel', async (ctx) => {
        const userId = ctx.from.id;
        if (addTaskStates.has(userId)) {
            addTaskStates.delete(userId);
            return await safeReply(ctx, '❌ Добавление задания отменено.');
        }
        return await safeReply(ctx, 'Нет активных операций для отмены.');
    });
    // Add task command
    bot.command('addtask', async (ctx) => {
        if (!await isAdmin(ctx)) {
            return; // Silently ignore for non-admins
        }
        const messageText = ctx.message.text;
        const content = messageText.replace('/addtask', '').trim();
        // Если нет содержимого, показываем инструкцию и устанавливаем состояние ожидания
        if (!content) {
            const userId = ctx.from.id;
            addTaskStates.set(userId, { waitingForData: true });
            return await safeReply(ctx, '📝 Добавление нового задания\n\n' +
                'Отправьте данные в формате списка:\n\n' +
                '1. Название задания\n' +
                '2. URL логотипа\n' +
                '3. URL задания\n' +
                '4. Тип (telegram или link)\n' +
                '5. Награда в $CEFIcoin\n\n' +
                '💡 Пример:\n' +
                '1. Подписаться на канал\n' +
                '2. https://example.com/logo.png\n' +
                '3. https://t.me/channel\n' +
                '4. telegram\n' +
                '5. 10\n\n' +
                '❌ Для отмены отправьте /cancel');
        }
        try {
            let title, logoUrl, link, type, points;
            // Проверяем формат списка (должен начинаться с "1.")
            if (!content.trim().startsWith('1.')) {
                return await safeReply(ctx, '❌ Неверный формат. Используйте формат списка:\n\n' +
                    '1. Название задания\n' +
                    '2. URL логотипа\n' +
                    '3. URL задания\n' +
                    '4. Тип (telegram или link)\n' +
                    '5. Награда в $CEFIcoin\n\n' +
                    '❌ Для отмены отправьте /cancel');
            }
            const lines = content.split('\n').map(line => line.trim()).filter(line => line);
            if (lines.length !== 5) {
                return await safeReply(ctx, '❌ Неверное количество данных. Нужно ровно 5 строк:\n' +
                    '1. Название задания\n' +
                    '2. URL логотипа\n' +
                    '3. URL задания\n' +
                    '4. Тип (telegram или link)\n' +
                    '5. Награда в $CEFIcoin\n\n' +
                    '❌ Для отмены отправьте /cancel');
            }
            // Парсим строки списка
            title = lines[0].replace(/^1\.\s*/, '').trim();
            logoUrl = lines[1].replace(/^2\.\s*/, '').trim();
            link = lines[2].replace(/^3\.\s*/, '').trim();
            type = lines[3].replace(/^4\.\s*/, '').trim();
            const pointsStr = lines[4].replace(/^5\.\s*/, '').trim();
            points = parseFloat(pointsStr);
            // Валидация данных
            if (!title) {
                return await safeReply(ctx, '❌ Название задания не может быть пустым.');
            }
            if (!logoUrl) {
                return await safeReply(ctx, '❌ URL логотипа не может быть пустым.');
            }
            if (!link) {
                return await safeReply(ctx, '❌ URL задания не может быть пустым.');
            }
            if (!type) {
                return await safeReply(ctx, '❌ Тип задания не может быть пустым.');
            }
            if (type !== 'telegram' && type !== 'link') {
                return await safeReply(ctx, '❌ Тип задания должен быть "telegram" или "link".');
            }
            if (!pointsStr) {
                return await safeReply(ctx, '❌ Поле награда не может быть пустым.');
            }
            if (isNaN(points) || points < 0.001) {
                return await safeReply(ctx, '❌ Награда должна быть числом не менее 0.001.');
            }
            const task = new Task_1.default({
                title,
                logo_url: logoUrl,
                link,
                type,
                is_active: true,
                points
            });
            await task.save();
            // Очищаем состояние ожидания, если оно было установлено
            const userId = ctx.from.id;
            addTaskStates.delete(userId);
            const typeLabel = type === 'telegram' ? 'Telegram канал' : 'Переход по ссылке';
            await safeReply(ctx, '✅ Задание успешно добавлено!\n\n' +
                `Название: ${title}\n` +
                `Логотип: ${logoUrl}\n` +
                `Ссылка: ${link}\n` +
                `Тип: ${typeLabel}\n` +
                `Награда: ${points} $CEFIcoin\n` +
                `ID: ${task._id}`);
        }
        catch (error) {
            console.error('Error in addtask command:', error);
            // Очищаем состояние ожидания при ошибке
            const userId = ctx.from.id;
            addTaskStates.delete(userId);
            await safeReply(ctx, '❌ Произошла ошибка при добавлении задания.');
        }
    });
    // List tasks command
    bot.command('listtasks', async (ctx) => {
        if (!await isAdmin(ctx)) {
            return; // Silently ignore for non-admins
        }
        try {
            const tasks = await Task_1.default.find().sort({ created_at: -1 });
            if (tasks.length === 0) {
                return await safeReply(ctx, '📋 Список заданий пуст.');
            }
            // Разделяем задания на активные и неактивные
            const activeTasks = tasks.filter(task => task.is_active);
            const inactiveTasks = tasks.filter(task => !task.is_active);
            let message = '📋 Список заданий:\n\n';
            // Показываем активные задания
            if (activeTasks.length > 0) {
                message += '🟢 АКТИВНЫЕ ЗАДАНИЯ:\n\n';
                activeTasks.forEach((task, index) => {
                    const typeLabel = task.type === 'telegram' ? 'Telegram канал' : 'Переход по ссылке';
                    message += `${index + 1}. ${task.title}\n` +
                        `ID: ${task._id}\n` +
                        `Тип: ${typeLabel}\n` +
                        `Награда: ${task.points} $CEFIcoin\n` +
                        `Ссылка: ${task.link}\n\n`;
                });
            }
            // Показываем неактивные задания
            if (inactiveTasks.length > 0) {
                message += '🔴 НЕАКТИВНЫЕ ЗАДАНИЯ:\n\n';
                inactiveTasks.forEach((task, index) => {
                    const typeLabel = task.type === 'telegram' ? 'Telegram канал' : 'Переход по ссылке';
                    message += `${index + 1}. ${task.title}\n` +
                        `ID: ${task._id}\n` +
                        `Тип: ${typeLabel}\n` +
                        `Награда: ${task.points} $CEFIcoin\n` +
                        `Ссылка: ${task.link}\n\n`;
                });
            }
            if (activeTasks.length === 0 && inactiveTasks.length === 0) {
                message += 'Заданий не найдено.';
            }
            await safeReply(ctx, message);
        }
        catch (error) {
            console.error('Error in listtasks command:', error);
            await safeReply(ctx, '❌ Произошла ошибка при получении списка заданий.');
        }
    });
    // Activate task command
    bot.command('activatetask', async (ctx) => {
        if (!await isAdmin(ctx)) {
            return; // Silently ignore for non-admins
        }
        const taskId = ctx.message.text.split(' ')[1];
        if (!taskId) {
            return await safeReply(ctx, 'Укажите ID задания: /activatetask [ID]');
        }
        if (!isValidObjectId(taskId)) {
            return await safeReply(ctx, '❌ Неверный формат ID задания. ID должен быть корректным ObjectId.');
        }
        try {
            const task = await Task_1.default.findById(taskId);
            if (!task) {
                return await safeReply(ctx, 'Задание не найдено.');
            }
            task.is_active = true;
            await task.save();
            await safeReply(ctx, `Задание "${task.title}" успешно активировано.`);
        }
        catch (error) {
            console.error('Error in activatetask command:', error);
            await safeReply(ctx, 'Произошла ошибка при активации задания.');
        }
    });
    // Deactivate task command
    bot.command('deactivatetask', async (ctx) => {
        if (!await isAdmin(ctx)) {
            return; // Silently ignore for non-admins
        }
        const taskId = ctx.message.text.split(' ')[1];
        if (!taskId) {
            return await safeReply(ctx, 'Укажите ID задания: /deactivatetask [ID]');
        }
        if (!isValidObjectId(taskId)) {
            return await safeReply(ctx, '❌ Неверный формат ID задания. ID должен быть корректным ObjectId.');
        }
        try {
            const task = await Task_1.default.findById(taskId);
            if (!task) {
                return await safeReply(ctx, 'Задание не найдено.');
            }
            task.is_active = false;
            await task.save();
            await safeReply(ctx, `Задание "${task.title}" успешно деактивировано.`);
        }
        catch (error) {
            console.error('Error in deactivatetask command:', error);
            await safeReply(ctx, 'Произошла ошибка при деактивации задания.');
        }
    });
    // Delete task command
    bot.command('deletetask', async (ctx) => {
        if (!await isAdmin(ctx)) {
            return; // Silently ignore for non-admins
        }
        const taskId = ctx.message.text.split(' ')[1];
        if (!taskId) {
            return await safeReply(ctx, 'Укажите ID задания: /deletetask [ID]');
        }
        if (!isValidObjectId(taskId)) {
            return await safeReply(ctx, '❌ Неверный формат ID задания. ID должен быть корректным ObjectId.');
        }
        try {
            const task = await Task_1.default.findById(taskId);
            if (!task) {
                return await safeReply(ctx, 'Задание не найдено.');
            }
            await Task_1.default.deleteOne({ _id: taskId });
            await safeReply(ctx, `Задание "${task.title}" успешно удалено.`);
        }
        catch (error) {
            console.error('Error in deletetask command:', error);
            await safeReply(ctx, 'Произошла ошибка при удалении задания.');
        }
    });
    // Make admin command
    bot.command('makeadmin', async (ctx) => {
        if (!await isAdmin(ctx)) {
            return; // Silently ignore for non-admins
        }
        const userId = ctx.message.text.split(' ')[1];
        if (!userId || isNaN(Number(userId))) {
            return await safeReply(ctx, 'Укажите ID пользователя: /makeadmin [ID]');
        }
        try {
            const user = await User_1.default.findOne({ chat_id: Number(userId) });
            if (!user) {
                return await safeReply(ctx, 'Пользователь не найден.');
            }
            user.role = 'admin';
            await user.save();
            await safeReply(ctx, `Пользователь ${user.firstname} ${user.lastname} (${user.username}) теперь администратор.`);
        }
        catch (error) {
            console.error('Error in makeadmin command:', error);
            await safeReply(ctx, 'Произошла ошибка при назначении администратора.');
        }
    });
    // Make admin command
    bot.command('makeuser', async (ctx) => {
        if (!await isAdmin(ctx)) {
            return; // Silently ignore for non-admins
        }
        const userId = ctx.message.text.split(' ')[1];
        if (!userId || isNaN(Number(userId))) {
            return await safeReply(ctx, 'Укажите ID пользователя: /makeuser [ID]');
        }
        try {
            const user = await User_1.default.findOne({ chat_id: Number(userId) });
            if (!user) {
                return await safeReply(ctx, 'Пользователь не найден.');
            }
            user.role = 'user';
            await user.save();
            await safeReply(ctx, `Пользователь ${user.firstname} ${user.lastname} (${user.username}) теперь не администратор.`);
        }
        catch (error) {
            console.error('Error in makeadmin command:', error);
            await safeReply(ctx, 'Произошла ошибка при назначении прав пользователя.');
        }
    });
    // Admin only mode command
    bot.command('adminonly', async (ctx) => {
        const isUserAdmin = await isAdmin(ctx);
        if (!isUserAdmin) {
            return; // Silently ignore for non-admins
        }
        const param = ctx.message.text.split(' ')[1]?.toLowerCase();
        if (!param || (param !== 'on' && param !== 'off')) {
            return await safeReply(ctx, 'Укажите параметр: /adminonly on или /adminonly off');
        }
        try {
            const newMode = param === 'on';
            ADMIN_ONLY_MODE = newMode;
            await safeReply(ctx, ADMIN_ONLY_MODE
                ? '✅ Режим "Только для администраторов" включен. Бот будет отвечать только администраторам.'
                : '❌ Режим "Только для администраторов" выключен. Бот будет отвечать всем пользователям.');
        }
        catch (error) {
            console.error('Error in adminonly command:', error);
            await safeReply(ctx, 'Произошла ошибка при изменении режима работы бота.');
        }
    });
    // Обработчик текстовых сообщений для добавления заданий
    bot.on('text', async (ctx) => {
        const userId = ctx.from.id;
        const userState = addTaskStates.get(userId);
        // Проверяем, ожидает ли пользователь ввода данных для задания
        if (userState && userState.waitingForData) {
            // Проверяем, является ли пользователь админом
            if (!await isAdmin(ctx)) {
                addTaskStates.delete(userId);
                return await safeReply(ctx, 'Эта операция доступна только администраторам.');
            }
            const content = ctx.message.text.trim();
            // Игнорируем команды
            if (content.startsWith('/')) {
                return;
            }
            try {
                let title, logoUrl, link, type, points;
                // Проверяем формат списка (должен начинаться с "1.")
                if (!content.trim().startsWith('1.')) {
                    return await safeReply(ctx, '❌ Неверный формат. Используйте формат списка:\n\n' +
                        '1. Название задания\n' +
                        '2. URL логотипа\n' +
                        '3. URL задания\n' +
                        '4. Тип (telegram или link)\n' +
                        '5. Награда в $CEFIcoin\n\n' +
                        '❌ Для отмены отправьте /cancel');
                }
                const lines = content.split('\n').map(line => line.trim()).filter(line => line);
                if (lines.length !== 5) {
                    return await safeReply(ctx, '❌ Неверное количество данных. Нужно ровно 5 строк:\n' +
                        '1. Название задания\n' +
                        '2. URL логотипа\n' +
                        '3. URL задания\n' +
                        '4. Тип (telegram или link)\n' +
                        '5. Награда в $CEFIcoin\n\n' +
                        '❌ Для отмены отправьте /cancel');
                }
                // Парсим строки списка
                title = lines[0].replace(/^1\.\s*/, '').trim();
                logoUrl = lines[1].replace(/^2\.\s*/, '').trim();
                link = lines[2].replace(/^3\.\s*/, '').trim();
                type = lines[3].replace(/^4\.\s*/, '').trim();
                const pointsStr = lines[4].replace(/^5\.\s*/, '').trim();
                points = parseFloat(pointsStr);
                // Валидация данных
                if (!title) {
                    return await safeReply(ctx, '❌ Название задания не может быть пустым.\n\n❌ Для отмены отправьте /cancel');
                }
                if (!logoUrl) {
                    return await safeReply(ctx, '❌ URL логотипа не может быть пустым.\n\n❌ Для отмены отправьте /cancel');
                }
                if (!link) {
                    return await safeReply(ctx, '❌ URL задания не может быть пустым.\n\n❌ Для отмены отправьте /cancel');
                }
                if (!type) {
                    return await safeReply(ctx, '❌ Тип задания не может быть пустым.\n\n❌ Для отмены отправьте /cancel');
                }
                if (type !== 'telegram' && type !== 'link') {
                    return await safeReply(ctx, '❌ Тип задания должен быть "telegram" или "link".\n\n❌ Для отмены отправьте /cancel');
                }
                if (!pointsStr) {
                    return await safeReply(ctx, '❌ Награда не может быть пустым полем.\n\n❌ Для отмены отправьте /cancel');
                }
                if (isNaN(points) || points < 0.001) {
                    return await safeReply(ctx, '❌ Награда должна быть числом не менее 0.001.\n\n❌ Для отмены отправьте /cancel');
                }
                const task = new Task_1.default({
                    title,
                    logo_url: logoUrl,
                    link,
                    type,
                    is_active: true,
                    points
                });
                await task.save();
                // Удаляем состояние ожидания
                addTaskStates.delete(userId);
                const typeLabel = type === 'telegram' ? 'Telegram канал' : 'Переход по ссылке';
                await safeReply(ctx, '✅ Задание успешно добавлено!\n\n' +
                    `Название: ${title}\n` +
                    `Логотип: ${logoUrl}\n` +
                    `Ссылка: ${link}\n` +
                    `Тип: ${typeLabel}\n` +
                    `Награда: ${points} $CEFIcoin\n` +
                    `ID: ${task._id}`);
            }
            catch (error) {
                console.error('Error processing task data:', error);
                addTaskStates.delete(userId);
                await safeReply(ctx, '❌ Произошла ошибка при добавлении задания.');
            }
        }
    });
};
exports.setupBot = setupBot;
