<template>
  <div class="movie-view">
    <div class="container">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner large"></div>
        <p>Загрузка фильма...</p>
      </div>

      <div v-else-if="error" class="error-container">
        <p>{{ error }}</p>
        <button @click="fetchMovie" class="retry-button">Повторить</button>
        <router-link to="/catalog" class="back-button">Вернуться в каталог</router-link>
      </div>

      <div v-else-if="movie" class="movie-details">
        <div class="movie-header">
          <button class="back-button" @click="goBack">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
            Назад
          </button>

          <button class="bookmark-button" @click="toggleBookmark" :disabled="bookmarkLoading">
            <div v-if="bookmarkLoading" class="bookmark-loading"></div>
            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" :fill="isBookmarked ? 'currentColor' : 'none'" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
            </svg>
          </button>
        </div>

        <h1 class="movie-title">{{ movie.name_rus }}</h1>
        <p class="movie-original-title">{{ movie.name_original }}</p>

        <div class="movie-meta">
          <div class="meta-item">
            <span class="meta-label">Год:</span>
            <span class="meta-value">{{ movie.year }}</span>
          </div>

          <div v-if="movie.kp_rating || movie.imdb_rating" class="meta-item">
            <span class="meta-label">Рейтинг:</span>
            <span class="meta-value rating">
              {{ movie.kp_rating || movie.imdb_rating }}
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" stroke="none" class="star-icon">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
            </span>
          </div>

          <div v-if="false" class="meta-item">
            <span class="meta-label">Тип:</span>
            <span class="meta-value">{{ formatType(movie.type) }}</span>
          </div>
        </div>

        <p v-if="movie.description_short" class="movie-description">
          {{ movie.description_short }}
        </p>

        <div class="iframe-container">
          <div v-if="iframeLoading" class="iframe-loading">
            <div class="loading-spinner"></div>
            <p>Загрузка плеера...</p>
          </div>
          <iframe
            v-if="movie.iframe_url"
            :src="movie.iframe_url"
            frameborder="0"
            allowfullscreen
            @load="iframeLoaded"
          ></iframe>
        </div>

        <div class="movie-actions">
          <button class="watch-button" @click="openInTelegram">
            Открыть в браузере
          </button>

          <button class="share-button" @click="shareMovie">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="18" cy="5" r="3"></circle>
              <circle cx="6" cy="12" r="3"></circle>
              <circle cx="18" cy="19" r="3"></circle>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
            </svg>
            Поделиться
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMoviesStore } from '@/store/movies';
import { useBookmarksStore } from '@/store/bookmarks';
import { formatMovieType } from '@/utils/movieUtils';

export default defineComponent({
  name: 'MovieView',
  setup() {
    const route = useRoute();
    const router = useRouter();
    const moviesStore = useMoviesStore();
    const bookmarksStore = useBookmarksStore();
    const iframeLoading = ref(true);
    const bookmarkLoading = ref(false);

    // Get source and ID from route params
    const source = computed(() => route.params.source as string);
    const id = computed(() => route.params.id as string);

    // Computed properties
    const movie = computed(() => moviesStore.currentMovie);
    const loading = computed(() => moviesStore.loading);
    const error = computed(() => moviesStore.error);

    // Check if movie is bookmarked
    const isBookmarked = computed(() => {
      if (!movie.value) return false;

      // Используем ID из объекта фильма, а не из параметров маршрута
      const movieId = source.value === 'kp' ? movie.value.kp_id : movie.value.imdb_id;

      if (!source.value || !movieId) return false;
      return bookmarksStore.isBookmarked(movieId, source.value as 'kp' | 'imdb');
    });

    // Fetch movie details
    const fetchMovie = async () => {
      if (source.value && id.value) {
        await moviesStore.fetchMovieById(source.value, id.value);
      }
    };



    // Toggle bookmark
    const toggleBookmark = async () => {
      if (!movie.value || !source.value) return;

      // Используем ID из объекта фильма, а не из параметров маршрута
      const movieId = source.value === 'kp' ? movie.value.kp_id : movie.value.imdb_id;

      if (!movieId) return;

      bookmarkLoading.value = true;

      try {
        if (isBookmarked.value) {
          const bookmarkId = bookmarksStore.getBookmarkId(movieId, source.value as 'kp' | 'imdb');
          if (bookmarkId) {
            await bookmarksStore.deleteBookmark(bookmarkId);
          }
        } else {
          await bookmarksStore.addBookmark(movieId, source.value as 'kp' | 'imdb');
        }
      } catch (error) {
        console.error('Error toggling bookmark:', error);
      } finally {
        bookmarkLoading.value = false;
      }
    };

    // Open movie in Telegram
    const openInTelegram = () => {
      if (!movie.value?.iframe_url) return;

      // Open link using Telegram Web App
      if (window.Telegram?.WebApp?.openLink) {
        window.Telegram.WebApp.openLink(movie.value.iframe_url);
      } else {
        window.open(movie.value.iframe_url, '_blank');
      }
    };

    // Share movie
    const shareMovie = () => {
      if (!movie.value) return;

      const botUsername = process.env.VUE_APP_TELEGRAM_BOT_USERNAME || 'your_bot';
      const shareUrl = `https://t.me/${botUsername}?startapp=see_${source.value}_${id.value}`;
      const shareText = `Смотри бесплатно!\n${movie.value.name_rus}`;

      const telegramShareUrl = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`;

      // Open share URL
      if (window.Telegram?.WebApp?.openLink) {
        window.Telegram.WebApp.openLink(telegramShareUrl);
      } else {
        window.open(telegramShareUrl, '_blank');
      }
    };

    // Go back
    const goBack = () => {
      router.back();
    };

    // Iframe loaded
    const iframeLoaded = () => {
      iframeLoading.value = false;
    };

    onMounted(async () => {
      // Fetch bookmarks first to know if movie is bookmarked
      // Всегда загружаем закладки для актуальности данных
      await bookmarksStore.fetchBookmarks();

      // Then fetch movie details
      await fetchMovie();
    });

    return {
      movie,
      loading,
      error,
      isBookmarked,
      iframeLoading,
      bookmarkLoading,
      fetchMovie,
      formatType: formatMovieType,
      toggleBookmark,
      openInTelegram,
      shareMovie,
      goBack,
      iframeLoaded
    };
  }
});
</script>

<style scoped>
.movie-view {
  padding-bottom: 80px;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;
  color: var(--tg-theme-hint-color);
}

.loading-spinner {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--tg-theme-button-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: var(--spacing-md);
}

.loading-spinner.large {
  width: 48px;
  height: 48px;
  border-width: 3px;
}

.retry-button, .back-button {
  margin-top: var(--spacing-md);
  background-color: var(--tg-theme-button-color);
  color: var(--tg-theme-button-text-color);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-md);
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
}

.back-button {
  margin-right: var(--spacing-md);
}

.movie-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.movie-header .back-button {
  display: flex;
  align-items: center;
  background: none;
  color: var(--tg-theme-text-color);
  padding: var(--spacing-xs);
  margin: 0;
}

.movie-header .back-button svg {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-xs);
}

.bookmark-button {
  background: none;
  border: none;
  color: var(--tg-theme-button-color);
  width: 32px;
  height: 32px;
  padding: 0;
  cursor: pointer;
}

.bookmark-button svg {
  width: 100%;
  height: 100%;
}

.bookmark-loading {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--tg-theme-button-color);
  animation: spin 1s ease-in-out infinite;
  margin: auto;
}

.movie-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
}

.movie-original-title {
  font-size: var(--font-size-md);
  color: var(--tg-theme-hint-color);
  margin-bottom: var(--spacing-md);
}

.movie-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-md);
}

.meta-item {
  margin-right: var(--spacing-md);
  margin-bottom: var(--spacing-xs);
}

.meta-label {
  color: var(--tg-theme-hint-color);
  margin-right: var(--spacing-xs);
}

.meta-value {
  font-weight: bold;
}

.meta-value.rating {
  display: inline-flex;
  align-items: center;
  color: #ffc107;
}

.star-icon {
  width: 16px;
  height: 16px;
  margin-left: 2px;
}

.movie-description {
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
}

.iframe-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
  margin-bottom: var(--spacing-md);
  background-color: #000;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.iframe-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.iframe-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.movie-actions {
  display: flex;
  justify-content: space-between;
}

.watch-button, .share-button {
  flex: 1;
  padding: var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-md);
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.watch-button {
  background-color: var(--tg-theme-button-color);
  color: var(--tg-theme-button-text-color);
  margin-right: var(--spacing-sm);
}

.share-button {
  background-color: var(--tg-theme-secondary-bg-color);
  color: var(--tg-theme-text-color);
  margin-left: var(--spacing-sm);
}

.share-button svg {
  width: 18px;
  height: 18px;
  margin-right: var(--spacing-xs);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
