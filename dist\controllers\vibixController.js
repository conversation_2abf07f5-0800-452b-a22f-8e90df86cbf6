"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchByUrl = exports.getMovie = exports.getMoviesByCategory = void 0;
const vibixService_1 = __importStar(require("../services/vibixService"));
const posterCacheService_1 = __importDefault(require("../services/posterCacheService"));
// Get movies by category
const getMoviesByCategory = async (req, res) => {
    try {
        const { category } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        // Validate category
        if (!Object.values(vibixService_1.MovieCategory).includes(category)) {
            return res.status(400).json({
                success: false,
                message: `Invalid category: ${category}`
            });
        }
        // Получаем данные из Vibix
        const vibixResponse = await vibixService_1.default.getMoviesByCategory(category, page, limit);
        // Обогащаем постерами из кеша
        const enrichedMovies = await posterCacheService_1.default.enrichMoviesWithPosters(vibixResponse.data);
        res.json({
            ...vibixResponse,
            data: enrichedMovies
        });
    }
    catch (error) {
        console.error(`Error in getMoviesByCategory controller:`, error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch movies by category',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getMoviesByCategory = getMoviesByCategory;
// Get movie by ID (KP or IMDB)
const getMovie = async (req, res) => {
    try {
        const { id } = req.params;
        const { source } = req.query;
        let movie;
        try {
            if (source === 'kp') {
                movie = await vibixService_1.default.getMovieByKpId(id);
            }
            else if (source === 'imdb') {
                movie = await vibixService_1.default.getMovieByImdbId(id);
            }
            else {
                // Try to determine source from ID format
                if (id.startsWith('tt')) {
                    movie = await vibixService_1.default.getMovieByImdbId(id);
                }
                else {
                    movie = await vibixService_1.default.getMovieByKpId(id);
                }
            }
        }
        catch (serviceError) {
            // Если это ошибка "фильм не найден"
            if (serviceError.isMovieNotFound) {
                return res.status(404).json({
                    success: false,
                    message: serviceError.message
                });
            }
            // Для других ошибок пробрасываем дальше
            throw serviceError;
        }
        if (!movie) {
            return res.status(404).json({
                success: false,
                message: `Movie with ID ${id} not found`
            });
        }
        // Возвращаем фильм без постеров (постеры не отображаются на странице просмотра)
        res.json(movie);
    }
    catch (error) {
        console.error(`Error in getMovie controller:`, error);
        // Определяем статус код ошибки
        const statusCode = error.statusCode || error.response?.status || 500;
        res.status(statusCode).json({
            success: false,
            message: error.message || 'Failed to fetch movie',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getMovie = getMovie;
// Search movie by URL
const searchByUrl = async (req, res) => {
    try {
        const { url } = req.query;
        if (!url || typeof url !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'URL parameter is required'
            });
        }
        const { source, id } = vibixService_1.default.extractMovieId(url);
        if (!source || !id) {
            return res.status(400).json({
                success: false,
                message: 'Could not extract movie ID from URL'
            });
        }
        let movie;
        try {
            if (source === 'kp') {
                movie = await vibixService_1.default.getMovieByKpId(id);
            }
            else {
                movie = await vibixService_1.default.getMovieByImdbId(id);
            }
        }
        catch (serviceError) {
            // Если это ошибка "фильм не найден"
            if (serviceError.isMovieNotFound) {
                return res.status(404).json({
                    success: false,
                    message: serviceError.message
                });
            }
            // Для других ошибок пробрасываем дальше
            throw serviceError;
        }
        if (!movie) {
            return res.status(404).json({
                success: false,
                message: `Фильм не найден по ссылке: ${url}`
            });
        }
        // Возвращаем фильм без постеров (постеры не отображаются на странице просмотра)
        res.json({
            success: true,
            data: movie
        });
    }
    catch (error) {
        console.error(`Error in searchByUrl controller:`, error);
        // Определяем статус код ошибки
        const statusCode = error.statusCode || error.response?.status || 500;
        res.status(statusCode).json({
            success: false,
            message: error.message || 'Не удалось найти фильм по указанной ссылке',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.searchByUrl = searchByUrl;
