"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[973],{522:(e,a,r)=>{function o(e){return e?e.kp_id?"kp":e.imdb_id?"imdb":null:null}function t(e){if(!e)return null;const a=o(e);return"kp"===a?e.kp_id||null:"imdb"===a&&e.imdb_id||null}function i(e){const a={movie:"Фильм",tv:"Сериал",anime:"Аниме","tv-series":"Сериал","anime-series":"Аниме-сериал"};return a[e]||e}function s(e){return e?e.poster_urls?.w500?e.poster_urls.w500:e.poster_url||null:null}r.d(a,{GO:()=>t,Ib:()=>o,nR:()=>s,uF:()=>i})},699:(e,a,r)=>{r.d(a,{A:()=>s,B:()=>o});var o,t=r(526);(function(e){e["POPULAR"]="popular",e["NEW"]="new",e["THRILLER"]="thriller",e["DRAMA"]="drama",e["ACTION"]="action",e["SCIFI"]="scifi",e["DETECTIVE"]="detective"})(o||(o={}));class i{baseUrl;constructor(){this.baseUrl="https://v2test.appkinobot.com/api/vibix"}async getMoviesByCategory(e,a=1,r=20){const o=await t.A.get(`${this.baseUrl}/movies/category/${e}`,{params:{limit:r,page:a}});return o.data}async getMovie(e){const a=await t.A.get(`${this.baseUrl}/movies/${e}`);return a.data}async searchByUrl(e){const a=await t.A.get(`${this.baseUrl}/search`,{params:{url:e}});return a.data}}const s=new i},874:(e,a,r)=>{r.d(a,{s:()=>s});var o=r(657),t=r(526),i=r(699);const s=(0,o.nY)("movies",{state:()=>({currentMovie:null,loading:!1,error:null,categoryMovies:{},categoryLoading:{},categoryError:{},pendingRequests:{}}),actions:{async fetchMovieById(e,a){if(this.loading=!0,this.error=null,this.currentMovie=null,!window.Telegram||!window.Telegram.WebApp)return this.error="Telegram WebApp is not available",void(this.loading=!1);try{const r=await t.A.get(`/vibix/movies/${a}`,{params:{source:e}});r.data?this.currentMovie=r.data:this.error="Failed to load movie"}catch(r){this.error=r.response?.data?.message||r.message||"Unknown error"}finally{this.loading=!1}},async searchMovie(e){if(this.loading=!0,this.error=null,this.currentMovie=null,!window.Telegram||!window.Telegram.WebApp)return this.error="Telegram WebApp is not available",this.loading=!1,null;try{const a=await t.A.get("/vibix/search",{params:{url:e}});return a.data.success?(this.currentMovie=a.data.data,a.data.data):(this.error=a.data.message||"Failed to search movie",null)}catch(a){return this.error=a.response?.data?.message||a.message||"Unknown error",null}finally{this.loading=!1}},setSearchQuery(e){console.log("Search query set:",e)},clearCurrentMovie(){this.currentMovie=null},async fetchMoviesByCategory(e,a=1,r=20){const o=`${e}_${a}_${r}`;this.categoryLoading={...this.categoryLoading,[e]:!0},this.categoryError={...this.categoryError,[e]:null};try{const o=await i.A.getMoviesByCategory(e,a,r);return o.data?.length>0?(1===a&&(this.categoryMovies[e]=o.data),o.data):(this.categoryError[e]="Не удалось загрузить фильмы для категории",[])}catch(t){return this.categoryError[e]=t.message||"Ошибка при загрузке фильмов",[]}finally{this.categoryLoading[e]=!1,delete this.pendingRequests[o]}},async getMoviesByCategory(e,a=1,r=20,o=!0){const t=`${e}_${a}_${r}`;if(o&&1===a&&this.categoryMovies[e]?.length>0)return this.categoryMovies[e];if(this.pendingRequests[t])return await this.pendingRequests[t];const i=this.fetchMoviesByCategory(e,a,r);return this.pendingRequests[t]=i,await i}}})},938:(e,a,r)=>{r.d(a,{a:()=>i});var o=r(657),t=r(526);const i=(0,o.nY)("bookmarks",{state:()=>({bookmarks:[],loading:!1,error:null,lastFetchTime:0}),actions:{async fetchBookmarks(e=!1){const a=Date.now(),r=3e5;if(!(!e&&this.bookmarks.length>0&&a-this.lastFetchTime<r)){this.loading=!0,this.error=null;try{const e=await t.A.get("/bookmarks");e.data.success?(this.bookmarks=e.data.data,this.lastFetchTime=a):this.error=e.data.message||"Failed to load bookmarks"}catch(o){this.error=o.response?.data?.message||o.message||"Unknown error",console.error("Error fetching bookmarks:",o)}finally{this.loading=!1}}},async addBookmark(e,a){this.loading=!0,this.error=null;try{const o=await t.A.post("/bookmarks",{movie_id:e,source:a});if(o.data.success){let i;try{if("kp"===a){const a=await t.A.get(`/vibix/movies/${e}?source=kp`);i=a.data}else{const a=await t.A.get(`/vibix/movies/${e}?source=imdb`);i=a.data}this.bookmarks.unshift({_id:o.data.data._id,source:a,details:i})}catch(r){console.error("Error fetching movie details:",r),this.bookmarks.unshift({_id:o.data.data._id,source:a,details:null})}return!0}return this.error=o.data.message||"Failed to add bookmark",!1}catch(o){return this.error=o.response?.data?.message||o.message||"Unknown error",console.error("Error adding bookmark:",o),!1}finally{this.loading=!1}},async deleteBookmark(e){this.loading=!0,this.error=null;try{const a=await t.A.delete(`/bookmarks/${e}`);return a.data.success?(this.bookmarks=this.bookmarks.filter((a=>a._id!==e)),!0):(this.error=a.data.message||"Failed to delete bookmark",!1)}catch(a){return this.error=a.response?.data?.message||a.message||"Unknown error",console.error("Error deleting bookmark:",a),!1}finally{this.loading=!1}},isBookmarked(e,a){return this.bookmarks.some((r=>!!r.details&&("kp"===a?r.details.kp_id===e&&r.source===a:"imdb"===a&&(r.details.imdb_id===e&&r.source===a))))},getBookmarkId(e,a){const r=this.bookmarks.find((r=>!!r.details&&("kp"===a?r.details.kp_id===e&&r.source===a:"imdb"===a&&(r.details.imdb_id===e&&r.source===a))));return r?r._id:null}}})},973:(e,a,r)=>{r.r(a),r.d(a,{default:()=>X});var o=r(768),t=r(232);const i={class:"movie-view"},s={class:"container"},n={key:0,class:"loading-container"},l={key:1,class:"error-container"},c={key:2,class:"movie-details"},d={class:"movie-header"},u=["disabled"],k={key:0,class:"bookmark-loading"},m=["fill"],v={class:"movie-title"},g={class:"movie-original-title"},h={class:"movie-meta"},p={class:"meta-item"},b={class:"meta-value"},y={key:0,class:"meta-item"},f={class:"meta-value rating"},w={key:1,class:"meta-item"},_={class:"meta-value"},L={key:0,class:"movie-description"},C={class:"iframe-container"},E={key:0,class:"iframe-loading"},B=["src"],M={class:"movie-actions"};function A(e,a,r,A,T,x){const $=(0,o.g2)("router-link");return(0,o.uX)(),(0,o.CE)("div",i,[(0,o.Lk)("div",s,[e.loading?((0,o.uX)(),(0,o.CE)("div",n,a[6]||(a[6]=[(0,o.Lk)("div",{class:"loading-spinner large"},null,-1),(0,o.Lk)("p",null,"Загрузка фильма...",-1)]))):e.error?((0,o.uX)(),(0,o.CE)("div",l,[(0,o.Lk)("p",null,(0,t.v_)(e.error),1),(0,o.Lk)("button",{onClick:a[0]||(a[0]=(...a)=>e.fetchMovie&&e.fetchMovie(...a)),class:"retry-button"},"Повторить"),(0,o.bF)($,{to:"/catalog",class:"back-button"},{default:(0,o.k6)((()=>a[7]||(a[7]=[(0,o.eW)("Вернуться в каталог")]))),_:1})])):e.movie?((0,o.uX)(),(0,o.CE)("div",c,[(0,o.Lk)("div",d,[(0,o.Lk)("button",{class:"back-button",onClick:a[1]||(a[1]=(...a)=>e.goBack&&e.goBack(...a))},a[8]||(a[8]=[(0,o.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,o.Lk)("polyline",{points:"15 18 9 12 15 6"})],-1),(0,o.eW)(" Назад ")])),(0,o.Lk)("button",{class:"bookmark-button",onClick:a[2]||(a[2]=(...a)=>e.toggleBookmark&&e.toggleBookmark(...a)),disabled:e.bookmarkLoading},[e.bookmarkLoading?((0,o.uX)(),(0,o.CE)("div",k)):((0,o.uX)(),(0,o.CE)("svg",{key:1,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:e.isBookmarked?"currentColor":"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},a[9]||(a[9]=[(0,o.Lk)("path",{d:"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"},null,-1)]),8,m))],8,u)]),(0,o.Lk)("h1",v,(0,t.v_)(e.movie.name_rus),1),(0,o.Lk)("p",g,(0,t.v_)(e.movie.name_original),1),(0,o.Lk)("div",h,[(0,o.Lk)("div",p,[a[10]||(a[10]=(0,o.Lk)("span",{class:"meta-label"},"Год:",-1)),(0,o.Lk)("span",b,(0,t.v_)(e.movie.year),1)]),e.movie.kp_rating||e.movie.imdb_rating?((0,o.uX)(),(0,o.CE)("div",y,[a[12]||(a[12]=(0,o.Lk)("span",{class:"meta-label"},"Рейтинг:",-1)),(0,o.Lk)("span",f,[(0,o.eW)((0,t.v_)(e.movie.kp_rating||e.movie.imdb_rating)+" ",1),a[11]||(a[11]=(0,o.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",stroke:"none",class:"star-icon"},[(0,o.Lk)("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})],-1))])])):(0,o.Q3)("",!0),e.movie.type?((0,o.uX)(),(0,o.CE)("div",w,[a[13]||(a[13]=(0,o.Lk)("span",{class:"meta-label"},"Тип:",-1)),(0,o.Lk)("span",_,(0,t.v_)(e.formatType(e.movie.type)),1)])):(0,o.Q3)("",!0)]),e.movie.description_short?((0,o.uX)(),(0,o.CE)("p",L,(0,t.v_)(e.movie.description_short),1)):(0,o.Q3)("",!0),(0,o.Lk)("div",C,[e.iframeLoading?((0,o.uX)(),(0,o.CE)("div",E,a[14]||(a[14]=[(0,o.Lk)("div",{class:"loading-spinner"},null,-1),(0,o.Lk)("p",null,"Загрузка плеера...",-1)]))):(0,o.Q3)("",!0),e.movie.iframe_url?((0,o.uX)(),(0,o.CE)("iframe",{key:1,src:e.movie.iframe_url,frameborder:"0",allowfullscreen:"",onLoad:a[3]||(a[3]=(...a)=>e.iframeLoaded&&e.iframeLoaded(...a))},null,40,B)):(0,o.Q3)("",!0)]),(0,o.Lk)("div",M,[(0,o.Lk)("button",{class:"watch-button",onClick:a[4]||(a[4]=(...a)=>e.openInTelegram&&e.openInTelegram(...a))}," Открыть в браузере "),(0,o.Lk)("button",{class:"share-button",onClick:a[5]||(a[5]=(...a)=>e.shareMovie&&e.shareMovie(...a))},a[15]||(a[15]=[(0,o.Fv)('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-c1e389f2><circle cx="18" cy="5" r="3" data-v-c1e389f2></circle><circle cx="6" cy="12" r="3" data-v-c1e389f2></circle><circle cx="18" cy="19" r="3" data-v-c1e389f2></circle><line x1="8.59" y1="13.51" x2="15.42" y2="17.49" data-v-c1e389f2></line><line x1="15.41" y1="6.51" x2="8.59" y2="10.49" data-v-c1e389f2></line></svg> Поделиться ',2)]))])])):(0,o.Q3)("",!0)])])}var T=r(144),x=r(387),$=r(874),W=r(938),I=r(522);const R=(0,o.pM)({name:"MovieView",setup(){const e=(0,x.lq)(),a=(0,x.rd)(),r=(0,$.s)(),t=(0,W.a)(),i=(0,T.KR)(!0),s=(0,T.KR)(!1),n=(0,o.EW)((()=>e.params.source)),l=(0,o.EW)((()=>e.params.id)),c=(0,o.EW)((()=>r.currentMovie)),d=(0,o.EW)((()=>r.loading)),u=(0,o.EW)((()=>r.error)),k=(0,o.EW)((()=>{if(!c.value)return!1;const e="kp"===n.value?c.value.kp_id:c.value.imdb_id;return!(!n.value||!e)&&t.isBookmarked(e,n.value)})),m=async()=>{n.value&&l.value&&await r.fetchMovieById(n.value,l.value)},v=async()=>{if(!c.value||!n.value)return;const e="kp"===n.value?c.value.kp_id:c.value.imdb_id;if(e){s.value=!0;try{if(k.value){const a=t.getBookmarkId(e,n.value);a&&await t.deleteBookmark(a)}else await t.addBookmark(e,n.value)}catch(u){console.error("Error toggling bookmark:",u)}finally{s.value=!1}}},g=()=>{c.value?.iframe_url&&(window.Telegram?.WebApp?.openLink?window.Telegram.WebApp.openLink(c.value.iframe_url):window.open(c.value.iframe_url,"_blank"))},h=()=>{if(!c.value)return;const e="tstcinemav2bot",a=`https://t.me/${e}?startapp=see_${n.value}_${l.value}`,r=`Смотри бесплатно!\n${c.value.name_rus}`,o=`https://t.me/share/url?url=${encodeURIComponent(a)}&text=${encodeURIComponent(r)}`;window.Telegram?.WebApp?.openLink?window.Telegram.WebApp.openLink(o):window.open(o,"_blank")},p=()=>{a.back()},b=()=>{i.value=!1};return(0,o.sV)((async()=>{await t.fetchBookmarks(),await m()})),{movie:c,loading:d,error:u,isBookmarked:k,iframeLoading:i,bookmarkLoading:s,fetchMovie:m,formatType:I.uF,toggleBookmark:v,openInTelegram:g,shareMovie:h,goBack:p,iframeLoaded:b}}});var U=r(241);const F=(0,U.A)(R,[["render",A],["__scopeId","data-v-c1e389f2"]]),X=F}}]);
//# sourceMappingURL=973.51db4865.js.map