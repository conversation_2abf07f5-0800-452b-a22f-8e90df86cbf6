[{"D:\\dev\\CinemaBotV2\\frontend\\src\\main.ts": "1", "D:\\dev\\CinemaBotV2\\frontend\\src\\App.vue": "2", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\BottomMenu.vue": "3", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\TelegramError.vue": "4", "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\user.ts": "5", "D:\\dev\\CinemaBotV2\\frontend\\src\\services\\api.ts": "6", "D:\\dev\\CinemaBotV2\\frontend\\src\\router\\index.ts": "7", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\ProfileView.vue": "8", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\MovieView.vue": "9", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\TasksView.vue": "10", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\BookmarksView.vue": "11", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\CatalogView.vue": "12", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\ReferralsView.vue": "13", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\MovieCard.vue": "14", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\TaskCard.vue": "15", "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\tasks.ts": "16", "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\bookmarks.ts": "17", "D:\\dev\\CinemaBotV2\\frontend\\src\\utils\\movieUtils.ts": "18", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\SearchBar.vue": "19", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\MovieRow.vue": "20", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\BannerCarousel.vue": "21", "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\movies.ts": "22", "D:\\dev\\CinemaBotV2\\frontend\\src\\services\\vibixService.ts": "23", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\ErrorMessage.vue": "24", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\MovieTile.vue": "25"}, {"size": 350, "mtime": 1747145654864, "results": "26", "hashOfConfig": "27"}, {"size": 6866, "mtime": 1748538011364, "results": "28", "hashOfConfig": "27"}, {"size": 3753, "mtime": 1746903413356, "results": "29", "hashOfConfig": "27"}, {"size": 2804, "mtime": 1747146761116, "results": "30", "hashOfConfig": "27"}, {"size": 2517, "mtime": 1748216626650, "results": "31", "hashOfConfig": "27"}, {"size": 1138, "mtime": 1748218547778, "results": "32", "hashOfConfig": "27"}, {"size": 1017, "mtime": 1748218345441, "results": "33", "hashOfConfig": "27"}, {"size": 2886, "mtime": 1748549257520, "results": "34", "hashOfConfig": "27"}, {"size": 12251, "mtime": 1748550331173, "results": "35", "hashOfConfig": "27"}, {"size": 3591, "mtime": 1748544998521, "results": "36", "hashOfConfig": "27"}, {"size": 4606, "mtime": 1748214978816, "results": "37", "hashOfConfig": "27"}, {"size": 5048, "mtime": 1748106992464, "results": "38", "hashOfConfig": "27"}, {"size": 9301, "mtime": 1748216638699, "results": "39", "hashOfConfig": "27"}, {"size": 6266, "mtime": 1748215001763, "results": "40", "hashOfConfig": "27"}, {"size": 4383, "mtime": 1748544957164, "results": "41", "hashOfConfig": "27"}, {"size": 2372, "mtime": 1748544362156, "results": "42", "hashOfConfig": "27"}, {"size": 5418, "mtime": 1747676164176, "results": "43", "hashOfConfig": "27"}, {"size": 2077, "mtime": 1748176568129, "results": "44", "hashOfConfig": "27"}, {"size": 6826, "mtime": 1748538749297, "results": "45", "hashOfConfig": "27"}, {"size": 12246, "mtime": 1748107061859, "results": "46", "hashOfConfig": "27"}, {"size": 11393, "mtime": 1748544377045, "results": "47", "hashOfConfig": "27"}, {"size": 5924, "mtime": 1748218562500, "results": "48", "hashOfConfig": "27"}, {"size": 2227, "mtime": 1748210100713, "results": "49", "hashOfConfig": "27"}, {"size": 5603, "mtime": 1748218331689, "results": "50", "hashOfConfig": "27"}, {"size": 6095, "mtime": 1748176456553, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f2e78o", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\dev\\CinemaBotV2\\frontend\\src\\main.ts", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\App.vue", ["127", "128", "129", "130"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\BottomMenu.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\TelegramError.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\user.ts", ["131", "132"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\services\\api.ts", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\router\\index.ts", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\ProfileView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\MovieView.vue", ["133"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\TasksView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\BookmarksView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\CatalogView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\ReferralsView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\MovieCard.vue", ["134"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\TaskCard.vue", ["135"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\tasks.ts", ["136", "137"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\bookmarks.ts", ["138", "139", "140", "141"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\utils\\movieUtils.ts", [], ["142"], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\SearchBar.vue", ["143"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\MovieRow.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\BannerCarousel.vue", ["144"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\movies.ts", ["145"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\services\\vibixService.ts", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\ErrorMessage.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\MovieTile.vue", ["146"], [], {"ruleId": "147", "severity": 1, "message": "148", "line": 65, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 65, "endColumn": 22, "suggestions": "151"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 79, "column": 11, "nodeType": "149", "messageId": "150", "endLine": 79, "endColumn": 22, "suggestions": "152"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 86, "column": 7, "nodeType": "149", "messageId": "150", "endLine": 86, "endColumn": 18, "suggestions": "153"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 110, "column": 11, "nodeType": "149", "messageId": "150", "endLine": 110, "endColumn": 22, "suggestions": "154"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 65, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 65, "endColumn": 22, "suggestions": "155"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 92, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 92, "endColumn": 22, "suggestions": "156"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 163, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 163, "endColumn": 22, "suggestions": "157"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 108, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 108, "endColumn": 22, "suggestions": "158"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 65, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 65, "endColumn": 22, "suggestions": "159"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 36, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 36, "endColumn": 22, "suggestions": "160"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 59, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 59, "endColumn": 22, "suggestions": "161"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 59, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 59, "endColumn": 22, "suggestions": "162"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 92, "column": 13, "nodeType": "149", "messageId": "150", "endLine": 92, "endColumn": 26, "suggestions": "163"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 108, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 108, "endColumn": 22, "suggestions": "164"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 132, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 132, "endColumn": 22, "suggestions": "165"}, {"ruleId": "166", "severity": 2, "message": "167", "line": 3, "column": 10, "nodeType": null, "messageId": "168", "endLine": 3, "endColumn": 15, "suppressions": "169"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 124, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 124, "endColumn": 22, "suggestions": "170"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 127, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 127, "endColumn": 22, "suggestions": "171"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 98, "column": 7, "nodeType": "149", "messageId": "150", "endLine": 98, "endColumn": 18, "suggestions": "172"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 109, "column": 9, "nodeType": "149", "messageId": "150", "endLine": 109, "endColumn": 22, "suggestions": "173"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["174"], ["175"], ["176"], ["177"], ["178"], ["179"], ["180"], ["181"], ["182"], ["183"], ["184"], ["185"], ["186"], ["187"], ["188"], "@typescript-eslint/no-unused-vars", "'Movie' is defined but never used.", "unusedVar", ["189"], ["190"], ["191"], ["192"], ["193"], {"messageId": "194", "data": "195", "fix": "196", "desc": "197"}, {"messageId": "194", "data": "198", "fix": "199", "desc": "200"}, {"messageId": "194", "data": "201", "fix": "202", "desc": "200"}, {"messageId": "194", "data": "203", "fix": "204", "desc": "200"}, {"messageId": "194", "data": "205", "fix": "206", "desc": "197"}, {"messageId": "194", "data": "207", "fix": "208", "desc": "197"}, {"messageId": "194", "data": "209", "fix": "210", "desc": "197"}, {"messageId": "194", "data": "211", "fix": "212", "desc": "197"}, {"messageId": "194", "data": "213", "fix": "214", "desc": "197"}, {"messageId": "194", "data": "215", "fix": "216", "desc": "197"}, {"messageId": "194", "data": "217", "fix": "218", "desc": "197"}, {"messageId": "194", "data": "219", "fix": "220", "desc": "197"}, {"messageId": "194", "data": "221", "fix": "222", "desc": "197"}, {"messageId": "194", "data": "223", "fix": "224", "desc": "197"}, {"messageId": "194", "data": "225", "fix": "226", "desc": "197"}, {"kind": "227", "justification": "228"}, {"messageId": "194", "data": "229", "fix": "230", "desc": "197"}, {"messageId": "194", "data": "231", "fix": "232", "desc": "197"}, {"messageId": "194", "data": "233", "fix": "234", "desc": "200"}, {"messageId": "194", "data": "235", "fix": "236", "desc": "197"}, "removeConsole", {"propertyName": "237"}, {"range": "238", "text": "228"}, "Remove the console.error().", {"propertyName": "239"}, {"range": "240", "text": "228"}, "Remove the console.log().", {"propertyName": "239"}, {"range": "241", "text": "228"}, {"propertyName": "239"}, {"range": "242", "text": "228"}, {"propertyName": "237"}, {"range": "243", "text": "228"}, {"propertyName": "237"}, {"range": "244", "text": "228"}, {"propertyName": "237"}, {"range": "245", "text": "228"}, {"propertyName": "237"}, {"range": "246", "text": "228"}, {"propertyName": "237"}, {"range": "247", "text": "228"}, {"propertyName": "237"}, {"range": "248", "text": "228"}, {"propertyName": "237"}, {"range": "249", "text": "228"}, {"propertyName": "237"}, {"range": "250", "text": "228"}, {"propertyName": "237"}, {"range": "251", "text": "228"}, {"propertyName": "237"}, {"range": "252", "text": "228"}, {"propertyName": "237"}, {"range": "253", "text": "228"}, "directive", "", {"propertyName": "237"}, {"range": "254", "text": "228"}, {"propertyName": "237"}, {"range": "255", "text": "228"}, {"propertyName": "239"}, {"range": "256", "text": "228"}, {"propertyName": "237"}, {"range": "257", "text": "228"}, "error", [2011, 2062], "log", [2540, 2593], [2736, 2784], [3534, 3580], [1571, 1620], [2364, 2414], [6144, 6193], [4043, 4092], [2185, 2232], [835, 881], [1517, 1564], [1552, 1602], [2695, 2754], [3279, 3326], [4017, 4066], [3731, 3778], [4527, 4593], [2790, 2830], [3898, 3947]]