[{"D:\\dev\\CinemaBotV2\\frontend\\src\\main.ts": "1", "D:\\dev\\CinemaBotV2\\frontend\\src\\App.vue": "2", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\BottomMenu.vue": "3", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\TelegramError.vue": "4", "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\user.ts": "5", "D:\\dev\\CinemaBotV2\\frontend\\src\\services\\api.ts": "6", "D:\\dev\\CinemaBotV2\\frontend\\src\\router\\index.ts": "7", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\ProfileView.vue": "8", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\MovieView.vue": "9", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\TasksView.vue": "10", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\BookmarksView.vue": "11", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\CatalogView.vue": "12", "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\ReferralsView.vue": "13", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\MovieCard.vue": "14", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\TaskCard.vue": "15", "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\tasks.ts": "16", "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\bookmarks.ts": "17", "D:\\dev\\CinemaBotV2\\frontend\\src\\utils\\movieUtils.ts": "18", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\SearchBar.vue": "19", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\MovieRow.vue": "20", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\BannerCarousel.vue": "21", "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\movies.ts": "22", "D:\\dev\\CinemaBotV2\\frontend\\src\\services\\vibixService.ts": "23", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\ErrorMessage.vue": "24", "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\MovieTile.vue": "25", "D:\\dev\\CinemaBotV2\\frontend\\src\\utils\\platform.ts": "26"}, {"size": 350, "mtime": 1747145654864, "results": "27", "hashOfConfig": "28"}, {"size": 6765, "mtime": 1748553511785, "results": "29", "hashOfConfig": "28"}, {"size": 3753, "mtime": 1746903413356, "results": "30", "hashOfConfig": "28"}, {"size": 2804, "mtime": 1747146761116, "results": "31", "hashOfConfig": "28"}, {"size": 2517, "mtime": 1748216626650, "results": "32", "hashOfConfig": "28"}, {"size": 1138, "mtime": 1748218547778, "results": "33", "hashOfConfig": "28"}, {"size": 1017, "mtime": 1748218345441, "results": "34", "hashOfConfig": "28"}, {"size": 1839, "mtime": 1748551449250, "results": "35", "hashOfConfig": "28"}, {"size": 12251, "mtime": 1748550331173, "results": "36", "hashOfConfig": "28"}, {"size": 4327, "mtime": 1748554686962, "results": "37", "hashOfConfig": "28"}, {"size": 4606, "mtime": 1748214978816, "results": "38", "hashOfConfig": "28"}, {"size": 5048, "mtime": 1748106992464, "results": "39", "hashOfConfig": "28"}, {"size": 9301, "mtime": 1748216638699, "results": "40", "hashOfConfig": "28"}, {"size": 6266, "mtime": 1748215001763, "results": "41", "hashOfConfig": "28"}, {"size": 3962, "mtime": 1748554523525, "results": "42", "hashOfConfig": "28"}, {"size": 2372, "mtime": 1748544362156, "results": "43", "hashOfConfig": "28"}, {"size": 5418, "mtime": 1747676164176, "results": "44", "hashOfConfig": "28"}, {"size": 2077, "mtime": 1748176568129, "results": "45", "hashOfConfig": "28"}, {"size": 6826, "mtime": 1748538749297, "results": "46", "hashOfConfig": "28"}, {"size": 12439, "mtime": 1748553531618, "results": "47", "hashOfConfig": "28"}, {"size": 11736, "mtime": 1748552142194, "results": "48", "hashOfConfig": "28"}, {"size": 5924, "mtime": 1748218562500, "results": "49", "hashOfConfig": "28"}, {"size": 2227, "mtime": 1748210100713, "results": "50", "hashOfConfig": "28"}, {"size": 5603, "mtime": 1748218331689, "results": "51", "hashOfConfig": "28"}, {"size": 6095, "mtime": 1748176456553, "results": "52", "hashOfConfig": "28"}, {"size": 562, "mtime": 1748552269724, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f2e78o", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\dev\\CinemaBotV2\\frontend\\src\\main.ts", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\App.vue", ["132", "133", "134", "135"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\BottomMenu.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\TelegramError.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\user.ts", ["136", "137"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\services\\api.ts", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\router\\index.ts", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\ProfileView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\MovieView.vue", ["138"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\TasksView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\BookmarksView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\CatalogView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\views\\ReferralsView.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\MovieCard.vue", ["139"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\TaskCard.vue", ["140"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\tasks.ts", ["141", "142"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\bookmarks.ts", ["143", "144", "145", "146"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\utils\\movieUtils.ts", [], ["147"], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\SearchBar.vue", ["148"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\MovieRow.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\BannerCarousel.vue", ["149"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\store\\movies.ts", ["150"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\services\\vibixService.ts", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\ErrorMessage.vue", [], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\components\\catalog\\MovieTile.vue", ["151"], [], "D:\\dev\\CinemaBotV2\\frontend\\src\\utils\\platform.ts", [], [], {"ruleId": "152", "severity": 1, "message": "153", "line": 65, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 65, "endColumn": 22, "suggestions": "156"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 79, "column": 11, "nodeType": "154", "messageId": "155", "endLine": 79, "endColumn": 22, "suggestions": "157"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 86, "column": 7, "nodeType": "154", "messageId": "155", "endLine": 86, "endColumn": 18, "suggestions": "158"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 115, "column": 11, "nodeType": "154", "messageId": "155", "endLine": 115, "endColumn": 22, "suggestions": "159"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 65, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 65, "endColumn": 22, "suggestions": "160"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 92, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 92, "endColumn": 22, "suggestions": "161"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 163, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 163, "endColumn": 22, "suggestions": "162"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 108, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 108, "endColumn": 22, "suggestions": "163"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 53, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 53, "endColumn": 22, "suggestions": "164"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 36, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 36, "endColumn": 22, "suggestions": "165"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 59, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 59, "endColumn": 22, "suggestions": "166"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 59, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 59, "endColumn": 22, "suggestions": "167"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 92, "column": 13, "nodeType": "154", "messageId": "155", "endLine": 92, "endColumn": 26, "suggestions": "168"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 108, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 108, "endColumn": 22, "suggestions": "169"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 132, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 132, "endColumn": 22, "suggestions": "170"}, {"ruleId": "171", "severity": 2, "message": "172", "line": 3, "column": 10, "nodeType": null, "messageId": "173", "endLine": 3, "endColumn": 15, "suppressions": "174"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 124, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 124, "endColumn": 22, "suggestions": "175"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 131, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 131, "endColumn": 22, "suggestions": "176"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 98, "column": 7, "nodeType": "154", "messageId": "155", "endLine": 98, "endColumn": 18, "suggestions": "177"}, {"ruleId": "152", "severity": 1, "message": "153", "line": 109, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 109, "endColumn": 22, "suggestions": "178"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["179"], ["180"], ["181"], ["182"], ["183"], ["184"], ["185"], ["186"], ["187"], ["188"], ["189"], ["190"], ["191"], ["192"], ["193"], "@typescript-eslint/no-unused-vars", "'Movie' is defined but never used.", "unusedVar", ["194"], ["195"], ["196"], ["197"], ["198"], {"messageId": "199", "data": "200", "fix": "201", "desc": "202"}, {"messageId": "199", "data": "203", "fix": "204", "desc": "205"}, {"messageId": "199", "data": "206", "fix": "207", "desc": "205"}, {"messageId": "199", "data": "208", "fix": "209", "desc": "205"}, {"messageId": "199", "data": "210", "fix": "211", "desc": "202"}, {"messageId": "199", "data": "212", "fix": "213", "desc": "202"}, {"messageId": "199", "data": "214", "fix": "215", "desc": "202"}, {"messageId": "199", "data": "216", "fix": "217", "desc": "202"}, {"messageId": "199", "data": "218", "fix": "219", "desc": "202"}, {"messageId": "199", "data": "220", "fix": "221", "desc": "202"}, {"messageId": "199", "data": "222", "fix": "223", "desc": "202"}, {"messageId": "199", "data": "224", "fix": "225", "desc": "202"}, {"messageId": "199", "data": "226", "fix": "227", "desc": "202"}, {"messageId": "199", "data": "228", "fix": "229", "desc": "202"}, {"messageId": "199", "data": "230", "fix": "231", "desc": "202"}, {"kind": "232", "justification": "233"}, {"messageId": "199", "data": "234", "fix": "235", "desc": "202"}, {"messageId": "199", "data": "236", "fix": "237", "desc": "202"}, {"messageId": "199", "data": "238", "fix": "239", "desc": "205"}, {"messageId": "199", "data": "240", "fix": "241", "desc": "202"}, "removeConsole", {"propertyName": "242"}, {"range": "243", "text": "233"}, "Remove the console.error().", {"propertyName": "244"}, {"range": "245", "text": "233"}, "Remove the console.log().", {"propertyName": "244"}, {"range": "246", "text": "233"}, {"propertyName": "244"}, {"range": "247", "text": "233"}, {"propertyName": "242"}, {"range": "248", "text": "233"}, {"propertyName": "242"}, {"range": "249", "text": "233"}, {"propertyName": "242"}, {"range": "250", "text": "233"}, {"propertyName": "242"}, {"range": "251", "text": "233"}, {"propertyName": "242"}, {"range": "252", "text": "233"}, {"propertyName": "242"}, {"range": "253", "text": "233"}, {"propertyName": "242"}, {"range": "254", "text": "233"}, {"propertyName": "242"}, {"range": "255", "text": "233"}, {"propertyName": "242"}, {"range": "256", "text": "233"}, {"propertyName": "242"}, {"range": "257", "text": "233"}, {"propertyName": "242"}, {"range": "258", "text": "233"}, "directive", "", {"propertyName": "242"}, {"range": "259", "text": "233"}, {"propertyName": "242"}, {"range": "260", "text": "233"}, {"propertyName": "244"}, {"range": "261", "text": "233"}, {"propertyName": "242"}, {"range": "262", "text": "233"}, "error", [2011, 2062], "log", [2540, 2593], [2736, 2784], [3685, 3731], [1571, 1620], [2364, 2414], [6144, 6193], [4043, 4092], [1797, 1844], [835, 881], [1517, 1564], [1552, 1602], [2695, 2754], [3279, 3326], [4017, 4066], [3731, 3778], [4778, 4844], [2790, 2830], [3898, 3947]]