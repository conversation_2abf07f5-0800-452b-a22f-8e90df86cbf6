{"version": 3, "file": "js/338.5d58af27.js", "mappings": "kLAEA,MAAMA,EAAa,CCDZC,MAAM,kBDEPC,EAAa,CCDVD,MAAM,aDETE,EAAa,CCJnBC,IAAA,EAM0BH,MAAM,qBDE1BI,EAAa,CCRnBD,IAAA,EAW6BH,MAAM,mBDC7BK,EAAa,CCZnBF,IAAA,EAgBkBH,MAAM,qBDAlBM,EAAa,CCCNN,MAAM,kBDAbO,EAAa,CCCJP,MAAM,aDAfQ,EAAa,CCCFR,MAAM,cDAjBS,EAAa,CCKNT,MAAM,2BDJbU,EAAc,CCMLV,MAAM,iBDLfW,ECrBN,UDsBMC,EAAc,CCtBpBT,IAAA,EA6BkCU,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDG7KC,EAAc,CChCpBd,IAAA,EAiC0BU,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDSrKE,EAAc,CC1CpBf,IAAA,EAyCyCH,MAAM,kBDKzCmB,EAAc,CCFHnB,MAAM,mBDGjBoB,EAAc,CCAHpB,MAAM,iBDCjBqB,EAAc,CCADrB,MAAM,iBDCnBsB,EAAc,CCADtB,MAAM,iBDCnBuB,EAAc,CClDpBpB,IAAA,EAsDoBH,MAAM,mBDCpB,SAAUwB,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OAAQC,EAAAA,EAAAA,OCvDRC,EAAAA,EAAAA,IAgEM,MAhENjC,EAgEM,EA/DJkC,EAAAA,EAAAA,IA8DM,MA9DNhC,EA8DM,CDNJyB,EAAO,MAAQA,EAAO,KCvDtBO,EAAAA,EAAAA,IAAuC,MAAnCjC,MAAM,cAAa,eAAW,IDwDlC0B,EAAO,MAAQA,EAAO,KCvDtBO,EAAAA,EAAAA,IAAqD,KAAlDjC,MAAM,oBAAmB,yBAAqB,IAEtCyB,EAAAS,UDuDNH,EAAAA,EAAAA,OCvDLC,EAAAA,EAAAA,IAGM,MAHN9B,EAGMwB,EAAA,KAAAA,EAAA,KAFJO,EAAAA,EAAAA,IAAyC,OAApCjC,MAAM,yBAAuB,UAClCiC,EAAAA,EAAAA,IAAyB,SAAtB,sBAAkB,OAGPR,EAAAU,QDuDTJ,EAAAA,EAAAA,OCvDPC,EAAAA,EAAAA,IAGM,MAHN5B,EAGM,EAFJ6B,EAAAA,EAAAA,IAAkB,UAAAG,EAAAA,EAAAA,IAAZX,EAAAU,OAAK,IACXF,EAAAA,EAAAA,IAAuE,UAA9DI,QAAKX,EAAA,KAAAA,EAAA,GD0DtB,IAAIY,IC1DoBb,EAAAc,gBAAAd,EAAAc,kBAAAD,IAAgBtC,MAAM,gBAAe,kBD8DhD+B,EAAAA,EAAAA,OC3DPC,EAAAA,EAAAA,IA+CM,MA/CN3B,EA+CM,EA9CJ4B,EAAAA,EAAAA,IAKM,MALN3B,EAKM,EAJJ2B,EAAAA,EAAAA,IAGM,MAHN1B,EAGM,EAFJ0B,EAAAA,EAAAA,IAAiD,MAAjDzB,GAAiD4B,EAAAA,EAAAA,IAAtBX,EAAAe,eAAa,GD4DlCd,EAAO,KAAOA,EAAO,IC3D3BO,EAAAA,EAAAA,IAAiD,OAA5CjC,MAAM,cAAa,uBAAmB,SAI/CiC,EAAAA,EAAAA,IAeM,MAfNxB,EAeM,CD4CEiB,EAAO,KAAOA,EAAO,IC1D3BO,EAAAA,EAAAA,IAAsD,MAAlDjC,MAAM,iBAAgB,2BAAuB,KACjDiC,EAAAA,EAAAA,IAWM,MAXNvB,EAWM,EAVJuB,EAAAA,EAAAA,IAAoE,SAA7DQ,KAAK,OAAOC,SAAA,GAAUC,MAAOlB,EAAAmB,aAAcC,IAAI,aD+D7C,KAAM,EC1F3BlC,IA4BYsB,EAAAA,EAAAA,IAQS,UARAI,QAAKX,EAAA,KAAAA,EAAA,GDkE1B,IAAIY,IClEwBb,EAAAqB,UAAArB,EAAAqB,YAAAR,IAAUtC,MAAM,eDoEvB,CCnEKyB,EAAAsB,SDgFDhB,EAAAA,EAAAA,OC5EXC,EAAAA,EAAAA,IAEM,MAFNf,EAEMS,EAAA,KAAAA,EAAA,KADJO,EAAAA,EAAAA,IAA6C,YAAnCe,OAAO,kBAAgB,eDgExBjB,EAAAA,EAAAA,OCrEXC,EAAAA,EAAAA,IAGM,MAHNpB,EAGMc,EAAA,KAAAA,EAAA,KAFJO,EAAAA,EAAAA,IAA8D,QAAxDgB,EAAE,IAAIC,EAAE,IAAIC,MAAM,KAAKC,OAAO,KAAKC,GAAG,IAAIC,GAAG,KD4EtC,MAAO,IC3EpBrB,EAAAA,EAAAA,IAAyE,QAAnEsB,EAAE,2DAAyD,iBDmFjE7B,EAAO,KAAOA,EAAO,IC5E3BO,EAAAA,EAAAA,IAAoG,KAAjGjC,MAAM,iBAAgB,2EAAuE,MAGvFyB,EAAA+B,UAAUC,OAAS,ID4EnB1B,EAAAA,EAAAA,OC5EXC,EAAAA,EAAAA,IAWM,MAXNd,EAWM,CDkEMQ,EAAO,KAAOA,EAAO,IC5E/BO,EAAAA,EAAAA,IAAkD,MAA9CjC,MAAM,iBAAgB,uBAAmB,MD6ElC+B,EAAAA,EAAAA,KAAW,IC5EtBC,EAAAA,EAAAA,IAQM0B,EAAAA,GAAA,MAnDhBC,EAAAA,EAAAA,IA2CkClC,EAAA+B,WAAZI,KD6EQ7B,EAAAA,EAAAA,OC7EpBC,EAAAA,EAAAA,IAQM,OAR8B7B,IAAKyD,EAASC,QAAS7D,MAAM,iBDgFlD,EC/EbiC,EAAAA,EAAAA,IAEM,MAFNd,GAEMiB,EAAAA,EAAAA,IADDX,EAAAqC,YAAYF,IAAQ,IAEzB3B,EAAAA,EAAAA,IAGM,MAHNb,EAGM,EAFJa,EAAAA,EAAAA,IAAgE,MAAhEZ,GAAgEe,EAAAA,EAAAA,IAAlCX,EAAAsC,gBAAgBH,IAAQ,IACtD3B,EAAAA,EAAAA,IAA4F,MAA5FX,EAA2B,mBAAec,EAAAA,EAAAA,IAAGX,EAAAuC,WAAWJ,EAASK,oBAAiB,UDiFxE,WAELlC,EAAAA,EAAAA,OC9EXC,EAAAA,EAAAA,IAQM,MARNT,EAQMG,EAAA,KAAAA,EAAA,KA9DdwC,EAAAA,EAAAA,IAAA,wgBD0IA,C,sBCzDA,SAAeC,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,gBACNC,KAAAA,GACE,MAAMC,GAAYC,EAAAA,EAAAA,KACZC,GAAY3B,EAAAA,EAAAA,IAA6B,MACzCE,GAASF,EAAAA,EAAAA,KAAI,GAGbX,GAAUuC,EAAAA,EAAAA,KAAS,IAAMH,EAAUpC,UACnCC,GAAQsC,EAAAA,EAAAA,KAAS,IAAMH,EAAUnC,QACjCS,GAAe6B,EAAAA,EAAAA,KAAS,IAAMH,EAAU1B,eACxCY,GAAYiB,EAAAA,EAAAA,KAAS,IAAMH,EAAUd,WAAWA,WAAa,KAC7DhB,GAAgBiC,EAAAA,EAAAA,KAAS,IAAMH,EAAUd,WAAWkB,OAAS,IAG7DnC,EAAiBoC,gBACfL,EAAU/B,gBAAgB,EAI5BO,EAAWA,KACX0B,EAAU7B,QACZ6B,EAAU7B,MAAMiC,SAChBC,SAASC,YAAY,QAGrB/B,EAAOJ,OAAQ,EACfoC,YAAW,KACThC,EAAOJ,OAAQ,CAAK,GACnB,KACL,EAIImB,EAAeF,GACfA,EAASoB,UACJpB,EAASoB,UAAUC,OAAO,GAAGC,cAC3BtB,EAASuB,SACXvB,EAASuB,SAASF,OAAO,GAAGC,cAE9B,IAIHnB,EAAmBH,GACnBA,EAASoB,WAAapB,EAASwB,SAC1B,GAAGxB,EAASoB,aAAapB,EAASwB,WAChCxB,EAASoB,UACXpB,EAASoB,UACPpB,EAASuB,SACX,IAAIvB,EAASuB,WAEf,OAAOvB,EAASC,UAInBG,EAAcqB,IAClB,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,oBAAoB,EAOlC,OAJAC,EAAAA,EAAAA,KAAU,KACRlD,GAAgB,IAGX,CACLL,UACAC,QACAS,eACAY,YACAhB,gBACAgC,YACAzB,SACAR,iBACAO,WACAgB,cACAC,kBACAC,aAEJ,I,aCzJF,MAAM0B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASlE,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://cinema-bot-frontend/./src/views/ReferralsView.vue?3207", "webpack://cinema-bot-frontend/./src/views/ReferralsView.vue", "webpack://cinema-bot-frontend/./src/views/ReferralsView.vue?abf0"], "sourcesContent": ["import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, createStaticVNode as _createStaticVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"referrals-view\" }\nconst _hoisted_2 = { class: \"container\" }\nconst _hoisted_3 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_4 = {\n  key: 1,\n  class: \"error-container\"\n}\nconst _hoisted_5 = {\n  key: 2,\n  class: \"referrals-content\"\n}\nconst _hoisted_6 = { class: \"referral-stats\" }\nconst _hoisted_7 = { class: \"stat-card\" }\nconst _hoisted_8 = { class: \"stat-value\" }\nconst _hoisted_9 = { class: \"referral-link-container\" }\nconst _hoisted_10 = { class: \"referral-link\" }\nconst _hoisted_11 = [\"value\"]\nconst _hoisted_12 = {\n  key: 0,\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  \"stroke-width\": \"2\",\n  \"stroke-linecap\": \"round\",\n  \"stroke-linejoin\": \"round\"\n}\nconst _hoisted_13 = {\n  key: 1,\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  \"stroke-width\": \"2\",\n  \"stroke-linecap\": \"round\",\n  \"stroke-linejoin\": \"round\"\n}\nconst _hoisted_14 = {\n  key: 0,\n  class: \"referrals-list\"\n}\nconst _hoisted_15 = { class: \"referral-avatar\" }\nconst _hoisted_16 = { class: \"referral-info\" }\nconst _hoisted_17 = { class: \"referral-name\" }\nconst _hoisted_18 = { class: \"referral-date\" }\nconst _hoisted_19 = {\n  key: 1,\n  class: \"empty-referrals\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[10] || (_cache[10] = _createElementVNode(\"h1\", { class: \"page-title\" }, \"Твои друзья\", -1)),\n      _cache[11] || (_cache[11] = _createElementVNode(\"p\", { class: \"page-description\" }, \"Реферальная программа\", -1)),\n      (_ctx.loading)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[2] || (_cache[2] = [\n            _createElementVNode(\"div\", { class: \"loading-spinner large\" }, null, -1),\n            _createElementVNode(\"p\", null, \"Загрузка данных...\", -1)\n          ])))\n        : (_ctx.error)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n              _createElementVNode(\"p\", null, _toDisplayString(_ctx.error), 1),\n              _createElementVNode(\"button\", {\n                onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.fetchReferrals && _ctx.fetchReferrals(...args))),\n                class: \"retry-button\"\n              }, \"Повторить\")\n            ]))\n          : (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n              _createElementVNode(\"div\", _hoisted_6, [\n                _createElementVNode(\"div\", _hoisted_7, [\n                  _createElementVNode(\"div\", _hoisted_8, _toDisplayString(_ctx.referralCount), 1),\n                  _cache[3] || (_cache[3] = _createElementVNode(\"div\", { class: \"stat-label\" }, \"Приглашенных друзей\", -1))\n                ])\n              ]),\n              _createElementVNode(\"div\", _hoisted_9, [\n                _cache[6] || (_cache[6] = _createElementVNode(\"h2\", { class: \"section-title\" }, \"Твоя реферальная ссылка\", -1)),\n                _createElementVNode(\"div\", _hoisted_10, [\n                  _createElementVNode(\"input\", {\n                    type: \"text\",\n                    readonly: \"\",\n                    value: _ctx.referralLink,\n                    ref: \"linkInput\"\n                  }, null, 8, _hoisted_11),\n                  _createElementVNode(\"button\", {\n                    onClick: _cache[1] || (_cache[1] = \n//@ts-ignore\n(...args) => (_ctx.copyLink && _ctx.copyLink(...args))),\n                    class: \"copy-button\"\n                  }, [\n                    (!_ctx.copied)\n                      ? (_openBlock(), _createElementBlock(\"svg\", _hoisted_12, _cache[4] || (_cache[4] = [\n                          _createElementVNode(\"rect\", {\n                            x: \"9\",\n                            y: \"9\",\n                            width: \"13\",\n                            height: \"13\",\n                            rx: \"2\",\n                            ry: \"2\"\n                          }, null, -1),\n                          _createElementVNode(\"path\", { d: \"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\" }, null, -1)\n                        ])))\n                      : (_openBlock(), _createElementBlock(\"svg\", _hoisted_13, _cache[5] || (_cache[5] = [\n                          _createElementVNode(\"polyline\", { points: \"20 6 9 17 4 12\" }, null, -1)\n                        ])))\n                  ])\n                ]),\n                _cache[7] || (_cache[7] = _createElementVNode(\"p\", { class: \"referral-hint\" }, \"Поделись ссылкой с друзьями, чтобы они присоединились к боту через тебя\", -1))\n              ]),\n              (_ctx.referrals.length > 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [\n                    _cache[8] || (_cache[8] = _createElementVNode(\"h2\", { class: \"section-title\" }, \"Список приглашенных\", -1)),\n                    (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.referrals, (referral) => {\n                      return (_openBlock(), _createElementBlock(\"div\", {\n                        key: referral.chat_id,\n                        class: \"referral-item\"\n                      }, [\n                        _createElementVNode(\"div\", _hoisted_15, _toDisplayString(_ctx.getInitials(referral)), 1),\n                        _createElementVNode(\"div\", _hoisted_16, [\n                          _createElementVNode(\"div\", _hoisted_17, _toDisplayString(_ctx.getReferralName(referral)), 1),\n                          _createElementVNode(\"div\", _hoisted_18, \"Присоединился: \" + _toDisplayString(_ctx.formatDate(referral.registration_date)), 1)\n                        ])\n                      ]))\n                    }), 128))\n                  ]))\n                : (_openBlock(), _createElementBlock(\"div\", _hoisted_19, _cache[9] || (_cache[9] = [\n                    _createStaticVNode(\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" class=\\\"empty-icon\\\" data-v-172607cc><path d=\\\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\" data-v-172607cc></path><circle cx=\\\"9\\\" cy=\\\"7\\\" r=\\\"4\\\" data-v-172607cc></circle><path d=\\\"M23 21v-2a4 4 0 0 0-3-3.87\\\" data-v-172607cc></path><path d=\\\"M16 3.13a4 4 0 0 1 0 7.75\\\" data-v-172607cc></path></svg><p data-v-172607cc>У тебя пока нет приглашенных друзей</p>\", 2)\n                  ])))\n            ]))\n    ])\n  ]))\n}", "<template>\n  <div class=\"referrals-view\">\n    <div class=\"container\">\n      <h1 class=\"page-title\">Твои друзья</h1>\n      <p class=\"page-description\">Реферальная программа</p>\n\n      <div v-if=\"loading\" class=\"loading-container\">\n        <div class=\"loading-spinner large\"></div>\n        <p>Загрузка данных...</p>\n      </div>\n\n      <div v-else-if=\"error\" class=\"error-container\">\n        <p>{{ error }}</p>\n        <button @click=\"fetchReferrals\" class=\"retry-button\">Повторить</button>\n      </div>\n\n      <div v-else class=\"referrals-content\">\n        <div class=\"referral-stats\">\n          <div class=\"stat-card\">\n            <div class=\"stat-value\">{{ referralCount }}</div>\n            <div class=\"stat-label\">Приглашенных друзей</div>\n          </div>\n        </div>\n\n        <div class=\"referral-link-container\">\n          <h2 class=\"section-title\">Твоя реферальная ссылка</h2>\n          <div class=\"referral-link\">\n            <input type=\"text\" readonly :value=\"referralLink\" ref=\"linkInput\" />\n            <button @click=\"copyLink\" class=\"copy-button\">\n              <svg v-if=\"!copied\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <rect x=\"9\" y=\"9\" width=\"13\" height=\"13\" rx=\"2\" ry=\"2\"></rect>\n                <path d=\"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\"></path>\n              </svg>\n              <svg v-else xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <polyline points=\"20 6 9 17 4 12\"></polyline>\n              </svg>\n            </button>\n          </div>\n          <p class=\"referral-hint\">Поделись ссылкой с друзьями, чтобы они присоединились к боту через тебя</p>\n        </div>\n\n        <div v-if=\"referrals.length > 0\" class=\"referrals-list\">\n          <h2 class=\"section-title\">Список приглашенных</h2>\n          <div v-for=\"referral in referrals\" :key=\"referral.chat_id\" class=\"referral-item\">\n            <div class=\"referral-avatar\">\n              {{ getInitials(referral) }}\n            </div>\n            <div class=\"referral-info\">\n              <div class=\"referral-name\">{{ getReferralName(referral) }}</div>\n              <div class=\"referral-date\">Присоединился: {{ formatDate(referral.registration_date) }}</div>\n            </div>\n          </div>\n        </div>\n\n        <div v-else class=\"empty-referrals\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"empty-icon\">\n            <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n            <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n            <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n            <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n          </svg>\n          <p>У тебя пока нет приглашенных друзей</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, onMounted, computed, ref } from 'vue';\nimport { useUserStore } from '@/store/user';\n\n// Используем интерфейс Referral из store\ntype Referral = {\n  chat_id: number;\n  username?: string;\n  firstname?: string;\n  lastname?: string;\n  registration_date: string;\n}\n\nexport default defineComponent({\n  name: 'ReferralsView',\n  setup() {\n    const userStore = useUserStore();\n    const linkInput = ref<HTMLInputElement | null>(null);\n    const copied = ref(false);\n\n    // Computed properties\n    const loading = computed(() => userStore.loading);\n    const error = computed(() => userStore.error);\n    const referralLink = computed(() => userStore.referralLink);\n    const referrals = computed(() => userStore.referrals?.referrals || []);\n    const referralCount = computed(() => userStore.referrals?.count || 0);\n\n    // Fetch referrals\n    const fetchReferrals = async () => {\n      await userStore.fetchReferrals();\n    };\n\n    // Copy referral link to clipboard\n    const copyLink = () => {\n      if (linkInput.value) {\n        linkInput.value.select();\n        document.execCommand('copy');\n\n        // Show copied indicator\n        copied.value = true;\n        setTimeout(() => {\n          copied.value = false;\n        }, 2000);\n      }\n    };\n\n    // Get initials for avatar\n    const getInitials = (referral: Referral): string => {\n      if (referral.firstname) {\n        return referral.firstname.charAt(0).toUpperCase();\n      } else if (referral.username) {\n        return referral.username.charAt(0).toUpperCase();\n      }\n      return '?';\n    };\n\n    // Get referral name\n    const getReferralName = (referral: Referral): string => {\n      if (referral.firstname && referral.lastname) {\n        return `${referral.firstname} ${referral.lastname}`;\n      } else if (referral.firstname) {\n        return referral.firstname;\n      } else if (referral.username) {\n        return `@${referral.username}`;\n      }\n      return `ID: ${referral.chat_id}`;\n    };\n\n    // Format date\n    const formatDate = (dateString: string): string => {\n      const date = new Date(dateString);\n      return date.toLocaleDateString();\n    };\n\n    onMounted(() => {\n      fetchReferrals();\n    });\n\n    return {\n      loading,\n      error,\n      referralLink,\n      referrals,\n      referralCount,\n      linkInput,\n      copied,\n      fetchReferrals,\n      copyLink,\n      getInitials,\n      getReferralName,\n      formatDate\n    };\n  }\n});\n</script>\n\n<style scoped>\n.referrals-view {\n  padding-bottom: 80px;\n}\n\n.page-title {\n  font-size: var(--font-size-xl);\n  font-weight: bold;\n  margin-bottom: var(--spacing-xs);\n  text-align: center;\n}\n\n.page-description {\n  color: var(--tg-theme-hint-color);\n  text-align: center;\n  margin-bottom: var(--spacing-lg);\n}\n\n.loading-container, .error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-xl) 0;\n  color: var(--tg-theme-hint-color);\n}\n\n.loading-spinner {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  border: 2px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top-color: var(--tg-theme-button-color);\n  animation: spin 1s ease-in-out infinite;\n  margin-bottom: var(--spacing-md);\n}\n\n.loading-spinner.large {\n  width: 48px;\n  height: 48px;\n  border-width: 3px;\n}\n\n.retry-button {\n  margin-top: var(--spacing-md);\n}\n\n.referral-stats {\n  display: flex;\n  justify-content: center;\n  margin-bottom: var(--spacing-lg);\n}\n\n.stat-card {\n  background-color: var(--tg-theme-secondary-bg-color);\n  border-radius: var(--border-radius);\n  padding: var(--spacing-md);\n  text-align: center;\n  min-width: 150px;\n}\n\n.stat-value {\n  font-size: 32px;\n  font-weight: bold;\n  color: var(--tg-theme-button-color);\n}\n\n.stat-label {\n  color: var(--tg-theme-hint-color);\n  font-size: var(--font-size-sm);\n}\n\n.section-title {\n  font-size: var(--font-size-lg);\n  margin-bottom: var(--spacing-md);\n  text-align: center;\n}\n\n.referral-link-container {\n  margin-bottom: var(--spacing-lg);\n}\n\n.referral-link {\n  display: flex;\n  margin-bottom: var(--spacing-sm);\n}\n\n.referral-link input {\n  flex: 1;\n  padding: var(--spacing-sm) var(--spacing-md);\n  border: 1px solid var(--tg-theme-hint-color);\n  border-radius: var(--border-radius) 0 0 var(--border-radius);\n  background-color: var(--tg-theme-secondary-bg-color);\n  color: var(--tg-theme-text-color);\n  font-size: var(--font-size-sm);\n}\n\n.copy-button {\n  width: 40px;\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  border: none;\n  border-radius: 0 var(--border-radius) var(--border-radius) 0;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.copy-button svg {\n  width: 18px;\n  height: 18px;\n}\n\n.referral-hint {\n  font-size: var(--font-size-sm);\n  color: var(--tg-theme-hint-color);\n  text-align: center;\n}\n\n.referrals-list {\n  margin-top: var(--spacing-lg);\n}\n\n.referral-item {\n  display: flex;\n  align-items: center;\n  background-color: var(--tg-theme-secondary-bg-color);\n  border-radius: var(--border-radius);\n  padding: var(--spacing-sm);\n  margin-bottom: var(--spacing-sm);\n}\n\n.referral-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  margin-right: var(--spacing-sm);\n}\n\n.referral-info {\n  flex: 1;\n}\n\n.referral-name {\n  font-weight: bold;\n}\n\n.referral-date {\n  font-size: var(--font-size-sm);\n  color: var(--tg-theme-hint-color);\n}\n\n.empty-referrals {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-xl) 0;\n  color: var(--tg-theme-hint-color);\n}\n\n.empty-icon {\n  width: 48px;\n  height: 48px;\n  color: var(--tg-theme-hint-color);\n  margin-bottom: var(--spacing-md);\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n</style>\n", "import { render } from \"./ReferralsView.vue?vue&type=template&id=172607cc&scoped=true&ts=true\"\nimport script from \"./ReferralsView.vue?vue&type=script&lang=ts\"\nexport * from \"./ReferralsView.vue?vue&type=script&lang=ts\"\n\nimport \"./ReferralsView.vue?vue&type=style&index=0&id=172607cc&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-172607cc\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "key", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "xmlns", "viewBox", "fill", "stroke", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_openBlock", "_createElementBlock", "_createElementVNode", "loading", "error", "_toDisplayString", "onClick", "args", "fetchReferrals", "referralCount", "type", "readonly", "value", "referralLink", "ref", "copyLink", "copied", "points", "x", "y", "width", "height", "rx", "ry", "d", "referrals", "length", "_Fragment", "_renderList", "referral", "chat_id", "getInitials", "getReferralName", "formatDate", "registration_date", "_createStaticVNode", "defineComponent", "name", "setup", "userStore", "useUserStore", "linkInput", "computed", "count", "async", "select", "document", "execCommand", "setTimeout", "firstname", "char<PERSON>t", "toUpperCase", "username", "lastname", "dateString", "date", "Date", "toLocaleDateString", "onMounted", "__exports__"], "sourceRoot": ""}