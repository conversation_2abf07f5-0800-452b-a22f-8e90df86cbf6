"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[86],{86:(s,e,a)=>{a.r(e),a.d(e,{default:()=>q});var t=a(768),n=a(130),r=a(232);const o={class:"tasks-view"},l={class:"container"},i={class:"page-description"},c={key:0,class:"user-balance"},k={class:"balance-content"},d={class:"balance-value"},u={key:1,class:"loading-container"},p={key:2,class:"error-container"},g={key:3,class:"empty-container"},v={key:4,class:"tasks-list"};function h(s,e,a,h,y,w){const L=(0,t.g2)("TaskCard");return(0,t.uX)(),(0,t.CE)("div",o,[(0,t.Lk)("div",l,[e[6]||(e[6]=(0,t.Lk)("h1",{class:"page-title"},"Задания",-1)),(0,t.Lk)("p",i,[e[2]||(e[2]=(0,t.eW)("Выполняйте задания и получайте ")),(0,t.Lk)("a",{href:"#",class:"cefi-link",onClick:e[0]||(e[0]=(0,n.D$)((()=>{}),["prevent"]))},"$CEFIcoin")]),s.user?((0,t.uX)(),(0,t.CE)("div",c,[(0,t.Lk)("div",k,[e[3]||(e[3]=(0,t.Lk)("span",{class:"balance-label"},"Ваши $CEFIcoin:",-1)),(0,t.Lk)("span",d,(0,r.v_)(s.user.task_points),1)])])):(0,t.Q3)("",!0),s.loading?((0,t.uX)(),(0,t.CE)("div",u,e[4]||(e[4]=[(0,t.Lk)("div",{class:"loading-spinner large"},null,-1),(0,t.Lk)("p",null,"Загрузка заданий...",-1)]))):s.error?((0,t.uX)(),(0,t.CE)("div",p,[(0,t.Lk)("p",null,(0,r.v_)(s.error),1),(0,t.Lk)("button",{onClick:e[1]||(e[1]=(...e)=>s.fetchTasks&&s.fetchTasks(...e)),class:"retry-button"},"Повторить")])):0===s.tasks.length?((0,t.uX)(),(0,t.CE)("div",g,e[5]||(e[5]=[(0,t.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"empty-icon"},[(0,t.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,t.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),(0,t.Lk)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1),(0,t.Lk)("p",null,"Нет доступных заданий",-1)]))):((0,t.uX)(),(0,t.CE)("div",v,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.tasks,(s=>((0,t.uX)(),(0,t.Wv)(L,{key:s._id,task:s},null,8,["task"])))),128))]))])])}const y={class:"task-logo"},w=["src","alt"],L={key:1,class:"logo-placeholder"},m={class:"task-info"},f={class:"task-title"},C={class:"task-meta"},E={class:"task-action-text"},T={class:"task-points"},_={class:"task-action"},x={key:0,class:"loading-spinner"},b={key:1,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"chevron-icon"};function X(s,e,a,n,o,l){return(0,t.uX)(),(0,t.CE)("div",{class:(0,r.C4)(["task-card",{loading:s.loading}]),onClick:e[0]||(e[0]=(...e)=>s.openTask&&s.openTask(...e))},[(0,t.Lk)("div",y,[s.task.logo_url?((0,t.uX)(),(0,t.CE)("img",{key:0,src:s.task.logo_url,alt:s.task.title},null,8,w)):((0,t.uX)(),(0,t.CE)("div",L,e[1]||(e[1]=[(0,t.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,t.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,t.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),(0,t.Lk)("line",{x1:"8",y1:"12",x2:"16",y2:"12"})],-1)])))]),(0,t.Lk)("div",m,[(0,t.Lk)("h3",f,(0,r.v_)(s.task.title),1),(0,t.Lk)("div",C,[(0,t.Lk)("span",E,(0,r.v_)(s.actionText),1),(0,t.Lk)("span",T,(0,r.v_)(s.task.points)+" $CEFIcoin",1)])]),(0,t.Lk)("div",_,[s.loading?((0,t.uX)(),(0,t.CE)("div",x)):((0,t.uX)(),(0,t.CE)("svg",b,e[2]||(e[2]=[(0,t.Lk)("polyline",{points:"9 18 15 12 9 6"},null,-1)])))])],2)}var W=a(144),F=a(657),I=a(526);const A=(0,F.nY)("tasks",{state:()=>({tasks:[],loading:!1,error:null}),actions:{async fetchTasks(){this.loading=!0,this.error=null;try{const s=await I.A.get("/tasks");s.data.success?this.tasks=s.data.data:this.error=s.data.message||"Failed to load tasks"}catch(s){this.error=s.response?.data?.message||s.message||"Unknown error",console.error("Error fetching tasks:",s)}finally{this.loading=!1}},async completeTask(s){this.loading=!0,this.error=null;try{const e=await I.A.post("/tasks/complete",{task_id:s});return e.data.success?(await this.fetchTasks(),!0):(this.error=e.data.message||"Failed to complete task",!1)}catch(e){return this.error=e.response?.data?.message||e.message||"Unknown error",console.error("Error completing task:",e),!1}finally{this.loading=!1}},openTaskLink(s){const e=window.Telegram?.WebApp;e?(s.includes("t.me/")||s.includes("telegram.me/"))&&e.openTelegramLink?e.openTelegramLink(s):e.openLink(s):window.open(s,"_blank")}}}),j=(0,t.pM)({name:"TaskCard",props:{task:{type:Object,required:!0}},setup(s){const e=A(),a=(0,W.KR)(!1),n=(0,t.EW)((()=>{switch(s.task.type){case"telegram":return"Подписаться";case"link":return"Перейти";default:return"Выполнить"}})),r=async()=>{a.value=!0;try{e.openTaskLink(s.task.link),await e.completeTask(s.task._id)}catch(t){console.error("Error completing task:",t)}finally{a.value=!1}};return{actionText:n,openTask:r,loading:a}}});var $=a(241);const B=(0,$.A)(j,[["render",X],["__scopeId","data-v-1ea7f2f6"]]),K=B;var M=a(652);const U=(0,t.pM)({name:"TasksView",components:{TaskCard:K},setup(){const s=A(),e=(0,M.k)(),a=(0,t.EW)((()=>s.tasks)),n=(0,t.EW)((()=>s.loading)),r=(0,t.EW)((()=>s.error)),o=(0,t.EW)((()=>e.user)),l=async()=>{await s.fetchTasks()};return(0,t.sV)((()=>{l()})),{tasks:a,loading:n,error:r,user:o,fetchTasks:l}}}),V=(0,$.A)(U,[["render",h],["__scopeId","data-v-0c17373f"]]),q=V}}]);
//# sourceMappingURL=86.070943f5.js.map