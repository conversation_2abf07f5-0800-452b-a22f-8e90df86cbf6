(()=>{"use strict";var e={502:(e,t,r)=>{var a=r(130),n=r(657),o=r(768),s=r(232);const i={class:"app-container"},l={class:"scrollable-content"};function c(e,t,r,a,n,c){const d=(0,o.g2)("router-view"),u=(0,o.g2)("BottomMenu"),p=(0,o.g2)("TelegramError");return e.isTelegramWebApp?((0,o.uX)(),(0,o.CE)("div",{key:0,class:(0,s.C4)(["app-wrapper",e.colorScheme])},[(0,o.Lk)("div",i,[(0,o.Lk)("div",l,[(0,o.bF)(d)])]),(0,o.bF)(u)],2)):((0,o.uX)(),(0,o.Wv)(p,{key:1,"bot-username":e.botUsername},null,8,["bot-username"]))}var d=r(144),u=r(387);const p={class:"bottom-menu"};function m(e,t,r,a,n,s){const i=(0,o.g2)("router-link");return(0,o.uX)(),(0,o.CE)("nav",p,[(0,o.bF)(i,{to:"/tasks",class:"menu-item","active-class":"active"},{default:(0,o.k6)((()=>t[0]||(t[0]=[(0,o.Lk)("div",{class:"icon"},[(0,o.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,o.Lk)("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),(0,o.Lk)("polyline",{points:"22 4 12 14.01 9 11.01"})])],-1),(0,o.Lk)("div",{class:"label"},"Задания",-1)]))),_:1}),(0,o.bF)(i,{to:"/bookmarks",class:"menu-item","active-class":"active"},{default:(0,o.k6)((()=>t[1]||(t[1]=[(0,o.Lk)("div",{class:"icon"},[(0,o.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,o.Lk)("path",{d:"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"})])],-1),(0,o.Lk)("div",{class:"label"},"Закладки",-1)]))),_:1}),(0,o.bF)(i,{to:"/catalog",class:"menu-item","active-class":"active"},{default:(0,o.k6)((()=>t[2]||(t[2]=[(0,o.Lk)("div",{class:"icon catalog-icon"},[(0,o.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,o.Lk)("rect",{x:"2",y:"2",width:"20",height:"20",rx:"2.18",ry:"2.18"}),(0,o.Lk)("line",{x1:"7",y1:"2",x2:"7",y2:"22"}),(0,o.Lk)("line",{x1:"17",y1:"2",x2:"17",y2:"22"}),(0,o.Lk)("line",{x1:"2",y1:"12",x2:"22",y2:"12"}),(0,o.Lk)("line",{x1:"2",y1:"7",x2:"7",y2:"7"}),(0,o.Lk)("line",{x1:"2",y1:"17",x2:"7",y2:"17"}),(0,o.Lk)("line",{x1:"17",y1:"17",x2:"22",y2:"17"}),(0,o.Lk)("line",{x1:"17",y1:"7",x2:"22",y2:"7"})])],-1),(0,o.Lk)("div",{class:"label"},"Каталог",-1)]))),_:1}),(0,o.bF)(i,{to:"/referrals",class:"menu-item","active-class":"active"},{default:(0,o.k6)((()=>t[3]||(t[3]=[(0,o.Lk)("div",{class:"icon"},[(0,o.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,o.Lk)("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),(0,o.Lk)("circle",{cx:"9",cy:"7",r:"4"}),(0,o.Lk)("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),(0,o.Lk)("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})])],-1),(0,o.Lk)("div",{class:"label"},"Друзья",-1)]))),_:1}),(0,o.bF)(i,{to:"/profile",class:"menu-item","active-class":"active"},{default:(0,o.k6)((()=>t[4]||(t[4]=[(0,o.Lk)("div",{class:"icon"},[(0,o.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,o.Lk)("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),(0,o.Lk)("circle",{cx:"12",cy:"7",r:"4"})])],-1),(0,o.Lk)("div",{class:"label"},"Профиль",-1)]))),_:1})])}const h=(0,o.pM)({name:"BottomMenu"});var v=r(241);const f=(0,v.A)(h,[["render",m],["__scopeId","data-v-8074b3c6"]]),g=f,k={class:"telegram-error"},b={class:"error-container"},w={class:"telegram-link"},y=["href"];function L(e,t,r,a,n,s){return(0,o.uX)(),(0,o.CE)("div",k,[(0,o.Lk)("div",b,[t[0]||(t[0]=(0,o.Fv)('<div class="error-icon" data-v-28a7f8f4><svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-28a7f8f4><circle cx="12" cy="12" r="10" data-v-28a7f8f4></circle><line x1="12" y1="8" x2="12" y2="12" data-v-28a7f8f4></line><line x1="12" y1="16" x2="12.01" y2="16" data-v-28a7f8f4></line></svg></div><h1 data-v-28a7f8f4>Доступ только через Telegram</h1><p data-v-28a7f8f4>Это приложение работает только при запуске из Telegram Mini App.</p><p data-v-28a7f8f4>Пожалуйста, откройте бота в Telegram и запустите приложение оттуда.</p>',4)),(0,o.Lk)("div",w,[(0,o.Lk)("a",{href:e.botLink,target:"_blank",class:"telegram-button"}," Открыть бота в Telegram ",8,y)]),t[1]||(t[1]=(0,o.Lk)("p",{class:"small-text"},"Если вы уже в Telegram, попробуйте закрыть это окно и открыть приложение заново.",-1))])])}const x=(0,o.pM)({name:"TelegramError",props:{botUsername:{type:String,default:"your_bot"}},computed:{botLink(){return`https://t.me/${this.botUsername}`}}}),T=(0,v.A)(x,[["render",L],["__scopeId","data-v-28a7f8f4"]]),A=T;var C=r(652);const E=(0,o.pM)({name:"App",components:{BottomMenu:g,TelegramError:A},setup(){const e=(0,u.rd)(),t=(0,o.EW)((()=>!(!window.Telegram||!window.Telegram.WebApp)&&(!!window.Telegram.WebApp.initData&&("function"===typeof window.Telegram.WebApp.close&&"function"===typeof window.Telegram.WebApp.expand)))),r="tstcinemav2bot",a=(0,d.KR)("light"),n=(0,C.k)(),s=e=>{try{const t=new URLSearchParams(e);return t.get("start_param")}catch(t){return console.error("Error parsing start_param:",t),null}},i=t=>{const r=t.match(/^see_([a-z]+)_(.+)$/);if(r){const[,t,a]=r;if("kp"===t||"imdb"===t)return console.log(`Redirecting to movie: ${t}/${a}`),void e.push(`/movie/${t}/${a}`)}console.log("Unknown start_param:",t)};return(0,o.sV)((()=>{if(t.value&&window.Telegram&&window.Telegram.WebApp){const e=window.Telegram.WebApp;a.value=e.colorScheme||"light",e.enableClosingConfirmation(),e.expand(),e.disableVerticalSwipes&&e.disableVerticalSwipes(),document.documentElement.className=e.colorScheme;const t=s(e.initData);t&&(console.log("Found start_param:",t),setTimeout((()=>{i(t)}),100)),n.initUser(),e.onEvent("themeChanged",(()=>{a.value=e.colorScheme,document.documentElement.className=e.colorScheme}))}else document.documentElement.className="light"})),{isTelegramWebApp:t,colorScheme:a,botUsername:r}}}),_=(0,v.A)(E,[["render",c]]),j=_,S=[{path:"/",redirect:"/catalog"},{path:"/tasks",name:"Tasks",component:()=>r.e(193).then(r.bind(r,193))},{path:"/bookmarks",name:"Bookmarks",component:()=>r.e(942).then(r.bind(r,942))},{path:"/catalog",name:"Catalog",component:()=>r.e(910).then(r.bind(r,910))},{path:"/referrals",name:"Referrals",component:()=>r.e(338).then(r.bind(r,338))},{path:"/profile",name:"Profile",component:()=>r.e(656).then(r.bind(r,656))},{path:"/movie/:source/:id",name:"Movie",component:()=>r.e(791).then(r.bind(r,791))},{path:"/:pathMatch(.*)*",name:"NotFound",redirect:"/catalog"}],M=(0,u.aE)({history:(0,u.LA)("/"),routes:S}),F=M,W=(0,a.Ef)(j),O=(0,n.Ey)();W.use(O),W.use(F),W.mount("#app")},526:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(373);const n=a.A.create({baseURL:"https://v2test.appkinobot.com/api",withCredentials:!0,headers:{"Content-Type":"application/json"}});n.interceptors.request.use((e=>(window.Telegram&&window.Telegram.WebApp&&(e.headers["X-Tg-Init-Data"]=window.Telegram.WebApp.initData),e)),(e=>Promise.reject(e)));const o=n},652:(e,t,r)=>{r.d(t,{k:()=>o});var a=r(657),n=r(526);const o=(0,a.nY)("user",{state:()=>({user:null,referrals:null,loading:!1,error:null}),getters:{referralLink:e=>{if(!e.user)return"";const t="tstcinemav2bot";return`https://t.me/${t}?start=${e.user.chat_id}`}},actions:{async initUser(){if(this.loading=!0,this.error=null,!window.Telegram||!window.Telegram.WebApp)return this.error="Telegram WebApp is not available",void(this.loading=!1);try{const e=await n.A.get("/users/me");e.data.success?this.user=e.data.data:this.error=e.data.message||"Failed to load user data"}catch(e){this.error=e.response?.data?.message||e.message||"Unknown error",console.error("Error initializing user:",e)}finally{this.loading=!1}},async fetchReferrals(){if(this.loading=!0,this.error=null,!window.Telegram||!window.Telegram.WebApp)return this.error="Telegram WebApp is not available",void(this.loading=!1);try{const e=await n.A.get("/users/referrals");e.data.success?this.referrals=e.data.data:this.error=e.data.message||"Failed to load referrals"}catch(e){this.error=e.response?.data?.message||e.message||"Unknown error",console.error("Error fetching referrals:",e)}finally{this.loading=!1}}}})}},t={};function r(a){var n=t[a];if(void 0!==n)return n.exports;var o=t[a]={exports:{}};return e[a](o,o.exports,r),o.exports}r.m=e,(()=>{var e=[];r.O=(t,a,n,o)=>{if(!a){var s=1/0;for(d=0;d<e.length;d++){for(var[a,n,o]=e[d],i=!0,l=0;l<a.length;l++)(!1&o||s>=o)&&Object.keys(r.O).every((e=>r.O[e](a[l])))?a.splice(l--,1):(i=!1,o<s&&(s=o));if(i){e.splice(d--,1);var c=n();void 0!==c&&(t=c)}}return t}o=o||0;for(var d=e.length;d>0&&e[d-1][2]>o;d--)e[d]=e[d-1];e[d]=[a,n,o]}})(),(()=>{r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}})(),(()=>{r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce(((t,a)=>(r.f[a](e,t),t)),[]))})(),(()=>{r.u=e=>"js/"+e+"."+{193:"dd9b4e7a",338:"5d58af27",656:"ed72dc48",791:"7743ef18",910:"f1b2023a",942:"295d5dbe"}[e]+".js"})(),(()=>{r.miniCssF=e=>"css/"+e+"."+{193:"170e3072",338:"a1f5395d",656:"133c0679",791:"954bc37d",910:"a071f1c8",942:"afb13e6a"}[e]+".css"})(),(()=>{r.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="cinema-bot-frontend:";r.l=(a,n,o,s)=>{if(e[a])e[a].push(n);else{var i,l;if(void 0!==o)for(var c=document.getElementsByTagName("script"),d=0;d<c.length;d++){var u=c[d];if(u.getAttribute("src")==a||u.getAttribute("data-webpack")==t+o){i=u;break}}i||(l=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,r.nc&&i.setAttribute("nonce",r.nc),i.setAttribute("data-webpack",t+o),i.src=a),e[a]=[n];var p=(t,r)=>{i.onerror=i.onload=null,clearTimeout(m);var n=e[a];if(delete e[a],i.parentNode&&i.parentNode.removeChild(i),n&&n.forEach((e=>e(r))),t)return t(r)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=p.bind(null,i.onerror),i.onload=p.bind(null,i.onload),l&&document.head.appendChild(i)}}})(),(()=>{r.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{r.p="/"})(),(()=>{if("undefined"!==typeof document){var e=(e,t,a,n,o)=>{var s=document.createElement("link");s.rel="stylesheet",s.type="text/css",r.nc&&(s.nonce=r.nc);var i=r=>{if(s.onerror=s.onload=null,"load"===r.type)n();else{var a=r&&r.type,i=r&&r.target&&r.target.href||t,l=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+i+")");l.name="ChunkLoadError",l.code="CSS_CHUNK_LOAD_FAILED",l.type=a,l.request=i,s.parentNode&&s.parentNode.removeChild(s),o(l)}};return s.onerror=s.onload=i,s.href=t,a?a.parentNode.insertBefore(s,a.nextSibling):document.head.appendChild(s),s},t=(e,t)=>{for(var r=document.getElementsByTagName("link"),a=0;a<r.length;a++){var n=r[a],o=n.getAttribute("data-href")||n.getAttribute("href");if("stylesheet"===n.rel&&(o===e||o===t))return n}var s=document.getElementsByTagName("style");for(a=0;a<s.length;a++){n=s[a],o=n.getAttribute("data-href");if(o===e||o===t)return n}},a=a=>new Promise(((n,o)=>{var s=r.miniCssF(a),i=r.p+s;if(t(s,i))return n();e(a,i,null,n,o)})),n={524:0};r.f.miniCss=(e,t)=>{var r={193:1,338:1,656:1,791:1,910:1,942:1};n[e]?t.push(n[e]):0!==n[e]&&r[e]&&t.push(n[e]=a(e).then((()=>{n[e]=0}),(t=>{throw delete n[e],t})))}}})(),(()=>{var e={524:0};r.f.j=(t,a)=>{var n=r.o(e,t)?e[t]:void 0;if(0!==n)if(n)a.push(n[2]);else{var o=new Promise(((r,a)=>n=e[t]=[r,a]));a.push(n[2]=o);var s=r.p+r.u(t),i=new Error,l=a=>{if(r.o(e,t)&&(n=e[t],0!==n&&(e[t]=void 0),n)){var o=a&&("load"===a.type?"missing":a.type),s=a&&a.target&&a.target.src;i.message="Loading chunk "+t+" failed.\n("+o+": "+s+")",i.name="ChunkLoadError",i.type=o,i.request=s,n[1](i)}};r.l(s,l,"chunk-"+t,t)}},r.O.j=t=>0===e[t];var t=(t,a)=>{var n,o,[s,i,l]=a,c=0;if(s.some((t=>0!==e[t]))){for(n in i)r.o(i,n)&&(r.m[n]=i[n]);if(l)var d=l(r)}for(t&&t(a);c<s.length;c++)o=s[c],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(d)},a=self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})();var a=r.O(void 0,[504],(()=>r(502)));a=r.O(a)})();
//# sourceMappingURL=app.5792be5c.js.map