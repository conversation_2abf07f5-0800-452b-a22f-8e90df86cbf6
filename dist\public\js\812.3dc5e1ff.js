"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[812],{812:(s,e,t)=>{t.r(e),t.d(e,{default:()=>K});var a=t(768),n=t(232);const o={class:"tasks-view"},r={class:"container"},l={key:0,class:"loading-container"},i={key:1,class:"error-container"},k={key:2,class:"empty-container"},c={key:3,class:"tasks-list"},d={key:4,class:"user-points"},u={class:"points-value"};function p(s,e,t,p,g,v){const h=(0,a.g2)("TaskCard");return(0,a.uX)(),(0,a.CE)("div",o,[(0,a.Lk)("div",r,[e[4]||(e[4]=(0,a.Lk)("h1",{class:"page-title"},"Задания",-1)),e[5]||(e[5]=(0,a.Lk)("p",{class:"page-description"},"Выполняйте задания и получайте баллы",-1)),s.loading?((0,a.uX)(),(0,a.CE)("div",l,e[1]||(e[1]=[(0,a.Lk)("div",{class:"loading-spinner large"},null,-1),(0,a.Lk)("p",null,"Загрузка заданий...",-1)]))):s.error?((0,a.uX)(),(0,a.CE)("div",i,[(0,a.Lk)("p",null,(0,n.v_)(s.error),1),(0,a.Lk)("button",{onClick:e[0]||(e[0]=(...e)=>s.fetchTasks&&s.fetchTasks(...e)),class:"retry-button"},"Повторить")])):0===s.tasks.length?((0,a.uX)(),(0,a.CE)("div",k,e[2]||(e[2]=[(0,a.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"empty-icon"},[(0,a.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,a.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),(0,a.Lk)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1),(0,a.Lk)("p",null,"Нет доступных заданий",-1)]))):((0,a.uX)(),(0,a.CE)("div",c,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(s.tasks,(s=>((0,a.uX)(),(0,a.Wv)(h,{key:s._id,task:s},null,8,["task"])))),128))])),s.user?((0,a.uX)(),(0,a.CE)("div",d,[(0,a.Lk)("p",null,[e[3]||(e[3]=(0,a.eW)("Ваши баллы: ")),(0,a.Lk)("span",u,(0,n.v_)(s.user.task_points),1)])])):(0,a.Q3)("",!0)])])}const g={class:"task-logo"},v=["src","alt"],h={key:1,class:"logo-placeholder"},y={class:"task-info"},w={class:"task-title"},m={class:"task-points"},L={class:"task-action"},C={key:0,class:"loading-spinner"},T={key:1,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"chevron-icon"};function f(s,e,t,o,r,l){return(0,a.uX)(),(0,a.CE)("div",{class:(0,n.C4)(["task-card",{loading:s.loading}]),onClick:e[0]||(e[0]=(...e)=>s.openTask&&s.openTask(...e))},[(0,a.Lk)("div",g,[s.task.logo_url?((0,a.uX)(),(0,a.CE)("img",{key:0,src:s.task.logo_url,alt:s.task.title},null,8,v)):((0,a.uX)(),(0,a.CE)("div",h,e[1]||(e[1]=[(0,a.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,a.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,a.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),(0,a.Lk)("line",{x1:"8",y1:"12",x2:"16",y2:"12"})],-1)])))]),(0,a.Lk)("div",y,[(0,a.Lk)("h3",w,(0,n.v_)(s.task.title),1),(0,a.Lk)("div",m,(0,n.v_)(s.task.points)+" "+(0,n.v_)(s.pointsText),1)]),(0,a.Lk)("div",L,[s.loading?((0,a.uX)(),(0,a.CE)("div",C)):((0,a.uX)(),(0,a.CE)("svg",T,e[2]||(e[2]=[(0,a.Lk)("polyline",{points:"9 18 15 12 9 6"},null,-1)])))])],2)}var E=t(144),_=t(657),x=t(526);const X=(0,_.nY)("tasks",{state:()=>({tasks:[],loading:!1,error:null}),actions:{async fetchTasks(){this.loading=!0,this.error=null;try{const s=await x.A.get("/tasks");s.data.success?this.tasks=s.data.data:this.error=s.data.message||"Failed to load tasks"}catch(s){this.error=s.response?.data?.message||s.message||"Unknown error",console.error("Error fetching tasks:",s)}finally{this.loading=!1}},async completeTask(s){this.loading=!0,this.error=null;try{const e=await x.A.post("/tasks/complete",{task_id:s});return e.data.success?(await this.fetchTasks(),!0):(this.error=e.data.message||"Failed to complete task",!1)}catch(e){return this.error=e.response?.data?.message||e.message||"Unknown error",console.error("Error completing task:",e),!1}finally{this.loading=!1}},openTaskLink(s){const e=window.Telegram?.WebApp;e?(s.includes("t.me/")||s.includes("telegram.me/"))&&e.openTelegramLink?e.openTelegramLink(s):e.openLink(s):window.open(s,"_blank")}}}),b=(0,a.pM)({name:"TaskCard",props:{task:{type:Object,required:!0}},setup(s){const e=X(),t=(0,E.KR)(!1),n=(0,a.EW)((()=>{const e=s.task.points;return e%10===1&&e%100!==11?"балл":[2,3,4].includes(e%10)&&![12,13,14].includes(e%100)?"балла":"баллов"})),o=async()=>{t.value=!0;try{e.openTaskLink(s.task.link),await e.completeTask(s.task._id)}catch(a){console.error("Error completing task:",a)}finally{t.value=!1}};return{pointsText:n,openTask:o,loading:t}}});var W=t(241);const A=(0,W.A)(b,[["render",f],["__scopeId","data-v-a3c6d22a"]]),j=A;var B=t(652);const F=(0,a.pM)({name:"TasksView",components:{TaskCard:j},setup(){const s=X(),e=(0,B.k)(),t=(0,a.EW)((()=>s.tasks)),n=(0,a.EW)((()=>s.loading)),o=(0,a.EW)((()=>s.error)),r=(0,a.EW)((()=>e.user)),l=async()=>{await s.fetchTasks()};return(0,a.sV)((()=>{l()})),{tasks:t,loading:n,error:o,user:r,fetchTasks:l}}}),I=(0,W.A)(F,[["render",p],["__scopeId","data-v-84c675a2"]]),K=I}}]);
//# sourceMappingURL=812.3dc5e1ff.js.map