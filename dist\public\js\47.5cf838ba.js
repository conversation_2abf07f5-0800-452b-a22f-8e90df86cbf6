"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[47],{47:(n,o,t)=>{t.r(o),t.d(o,{default:()=>r});var a=t(768);const e={class:"profile-view"};function c(n,o,t,c,s,i){return(0,a.uX)(),(0,a.CE)("div",e,o[0]||(o[0]=[(0,a.Fv)('<div class="container" data-v-18c688f0><h1 class="page-title" data-v-18c688f0>Личный кабинет</h1><div class="under-construction" data-v-18c688f0><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="construction-icon" data-v-18c688f0><polygon points="12 2 2 7 12 12 22 7 12 2" data-v-18c688f0></polygon><polyline points="2 17 12 22 22 17" data-v-18c688f0></polyline><polyline points="2 12 12 17 22 12" data-v-18c688f0></polyline></svg><h2 data-v-18c688f0>В разработке</h2><p data-v-18c688f0>Эта функция будет доступна в ближайшее время</p></div></div>',1)]))}const s=(0,a.pM)({name:"ProfileView"});var i=t(241);const l=(0,i.A)(s,[["render",c],["__scopeId","data-v-18c688f0"]]),r=l}}]);
//# sourceMappingURL=47.5cf838ba.js.map