"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MovieCategory = void 0;
const axios_1 = __importDefault(require("axios"));
const https_proxy_agent_1 = require("https-proxy-agent");
const http_proxy_agent_1 = require("http-proxy-agent");
const socks_proxy_agent_1 = require("socks-proxy-agent");
// Define movie categories
var MovieCategory;
(function (MovieCategory) {
    MovieCategory["POPULAR"] = "popular";
    MovieCategory["NEW"] = "new";
    MovieCategory["THRILLER"] = "thriller";
    MovieCategory["DRAMA"] = "drama";
    MovieCategory["ACTION"] = "action";
    MovieCategory["SCIFI"] = "scifi";
    MovieCategory["DETECTIVE"] = "detective";
})(MovieCategory || (exports.MovieCategory = MovieCategory = {}));
const CATEGORY_MAP = {
    [MovieCategory.POPULAR]: { paramType: 'tag', id: 3 },
    [MovieCategory.NEW]: { paramType: 'tag', id: 6 },
    [MovieCategory.THRILLER]: { paramType: 'genre', id: 11 },
    [MovieCategory.DRAMA]: { paramType: 'genre', id: 8 },
    [MovieCategory.ACTION]: { paramType: 'genre', id: 13 },
    [MovieCategory.SCIFI]: { paramType: 'genre', id: 17 },
    [MovieCategory.DETECTIVE]: { paramType: 'genre', id: 19 }
};
class VibixService {
    constructor() {
        this.apiToken = process.env.VIBIX_API_TOKEN || '';
        this.baseUrl = 'https://vibix.org/api/v1/publisher';
        this.fields = 'id,name_rus,name_original,iframe_url,type,year,kp_id,imdb_id,kp_rating,imdb_rating,description_short,poster_url';
        // Create axios instance with proxy support
        this.axiosInstance = this.createAxiosInstance();
    }
    createAxiosInstance() {
        const config = {
            timeout: 30000, // 30 seconds timeout
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        };
        // Configure proxy if environment variables are set
        const proxyUrl = process.env.VIBIX_PROXY_URL;
        if (proxyUrl) {
            console.log(`[VibixService] Using proxy: ${proxyUrl}`);
            try {
                // Determine proxy type based on the URL protocol
                if (proxyUrl.startsWith('socks5://') || proxyUrl.startsWith('socks4://')) {
                    // SOCKS proxy
                    const socksAgent = new socks_proxy_agent_1.SocksProxyAgent(proxyUrl);
                    config.httpAgent = socksAgent;
                    config.httpsAgent = socksAgent;
                }
                else if (proxyUrl.startsWith('https://')) {
                    // HTTPS proxy
                    config.httpsAgent = new https_proxy_agent_1.HttpsProxyAgent(proxyUrl);
                    config.httpAgent = new http_proxy_agent_1.HttpProxyAgent(proxyUrl.replace('https://', 'http://'));
                }
                else if (proxyUrl.startsWith('http://')) {
                    // HTTP proxy
                    config.httpAgent = new http_proxy_agent_1.HttpProxyAgent(proxyUrl);
                    config.httpsAgent = new https_proxy_agent_1.HttpsProxyAgent(proxyUrl);
                }
                else {
                    // Default to HTTP if no protocol specified
                    const httpProxyUrl = `http://${proxyUrl}`;
                    config.httpAgent = new http_proxy_agent_1.HttpProxyAgent(httpProxyUrl);
                    config.httpsAgent = new https_proxy_agent_1.HttpsProxyAgent(httpProxyUrl);
                }
                console.log(`[VibixService] Proxy configured successfully`);
            }
            catch (error) {
                console.error('[VibixService] Error configuring proxy:', error);
                console.warn('[VibixService] Continuing without proxy...');
            }
        }
        else {
            console.log('[VibixService] No proxy configured, using direct connection');
        }
        return axios_1.default.create(config);
    }
    // Get movies by category
    async getMoviesByCategory(category, page = 1, limit = 20, type = 'movie') {
        try {
            const categoryInfo = CATEGORY_MAP[category];
            const params = {
                type,
                limit,
                page,
                fields: this.fields
            };
            // Add the appropriate parameter based on category type
            if (categoryInfo.paramType === 'tag') {
                params['tag[]'] = categoryInfo.id;
            }
            else if (categoryInfo.paramType === 'genre') {
                params['genre[]'] = categoryInfo.id;
            }
            const response = await this.axiosInstance.get(`${this.baseUrl}/videos/links`, {
                params,
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`
                }
            });
            return response.data;
        }
        catch (error) {
            console.error(`Error fetching movies for category ${category} from Vibix API:`, error.message);
            throw error;
        }
    }
    // Get movie by Kinopoisk ID
    async getMovieByKpId(kpId) {
        try {
            const response = await this.axiosInstance.get(`${this.baseUrl}/videos/kp/${kpId}`, {
                params: {
                    fields: this.fields
                },
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`
                }
            });
            return response.data;
        }
        catch (error) {
            console.error(`Error fetching movie with KP ID ${kpId} from Vibix API:`, error.message);
            // Если это 404 ошибка, создаем специальную ошибку
            if (error.response?.status === 404) {
                const notFoundError = new Error(`Фильм не найден в базе`);
                notFoundError.statusCode = 404;
                notFoundError.isMovieNotFound = true;
                throw notFoundError;
            }
            // Для других ошибок передаем как есть
            throw error;
        }
    }
    // Get movie by IMDB ID
    async getMovieByImdbId(imdbId) {
        try {
            const response = await this.axiosInstance.get(`${this.baseUrl}/videos/imdb/${imdbId}`, {
                params: {
                    fields: this.fields
                },
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`
                }
            });
            return response.data;
        }
        catch (error) {
            console.error(`Error fetching movie with IMDB ID ${imdbId} from Vibix API:`, error.message);
            // Если это 404 ошибка, создаем специальную ошибку
            if (error.response?.status === 404) {
                const notFoundError = new Error(`Фильм не найден в базе`);
                notFoundError.statusCode = 404;
                notFoundError.isMovieNotFound = true;
                throw notFoundError;
            }
            // Для других ошибок передаем как есть
            throw error;
        }
    }
    // Extract movie ID from URL
    extractMovieId(url) {
        // Extract Kinopoisk ID
        const kpMatch = url.match(/kinopoisk\.ru\/(film|series)\/(\d+)/i);
        if (kpMatch && kpMatch[2]) {
            return { source: 'kp', id: kpMatch[2] };
        }
        // Extract IMDB ID
        const imdbMatch = url.match(/imdb\.com\/title\/(tt\d+)/i);
        if (imdbMatch && imdbMatch[1]) {
            return { source: 'imdb', id: imdbMatch[1] };
        }
        return { source: null, id: null };
    }
}
exports.default = new VibixService();
