import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Bookmark from '../models/Bookmark';
import vibixService from '../services/vibixService';

// Функция для проверки валидности ObjectId
const isValidObjectId = (id: string): boolean => {
  return mongoose.Types.ObjectId.isValid(id);
};

// Get user's bookmarks
export const getBookmarks = async (req: Request, res: Response) => {
  try {
    const userId = req.user.chat_id;

    const bookmarks = await Bookmark.find({ user_id: userId }).sort({ created_at: -1 });

    // Fetch movie details for each bookmark sequentially to avoid overwhelming APIs
    const bookmarksWithDetails = [];
    for (const bookmark of bookmarks) {
      try {
        let movieDetails;
        if (bookmark.source === 'kp') {
          movieDetails = await vibixService.getMovieByKpId(bookmark.movie_id);
        } else {
          movieDetails = await vibixService.getMovieByImdbId(bookmark.movie_id);
        }

        bookmarksWithDetails.push({
          _id: bookmark._id,
          source: bookmark.source,
          details: movieDetails
        });
      } catch (error) {
        console.error(`Error fetching details for bookmark ${bookmark._id}:`, error);
        bookmarksWithDetails.push({
          _id: bookmark._id,
          source: bookmark.source,
          details: null
        });
      }
    }

    res.json({
      success: true,
      data: bookmarksWithDetails
    });
  } catch (error) {
    console.error('Error in getBookmarks controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bookmarks'
    });
  }
};

// Add bookmark
export const addBookmark = async (req: Request, res: Response) => {
  try {
    const userId = req.user.chat_id;
    const { movie_id, source } = req.body;

    if (!movie_id || !source) {
      return res.status(400).json({
        success: false,
        message: 'Movie ID and source are required'
      });
    }

    if (source !== 'kp' && source !== 'imdb') {
      return res.status(400).json({
        success: false,
        message: 'Invalid source. Must be "kp" or "imdb".'
      });
    }

    // Check if bookmark already exists
    const existingBookmark = await Bookmark.findOne({ user_id: userId, movie_id, source });

    if (existingBookmark) {
      return res.status(400).json({
        success: false,
        message: 'Bookmark already exists'
      });
    }

    // Create new bookmark
    const bookmark = new Bookmark({
      user_id: userId,
      movie_id,
      source
    });

    await bookmark.save();

    res.status(201).json({
      success: true,
      data: bookmark
    });
  } catch (error) {
    console.error('Error in addBookmark controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add bookmark'
    });
  }
};

// Delete bookmark
export const deleteBookmark = async (req: Request, res: Response) => {
  try {
    const userId = req.user.chat_id;
    const { id } = req.params;

    if (!isValidObjectId(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid bookmark ID format'
      });
    }

    const bookmark = await Bookmark.findOne({ _id: id, user_id: userId });

    if (!bookmark) {
      return res.status(404).json({
        success: false,
        message: 'Bookmark not found'
      });
    }

    await Bookmark.deleteOne({ _id: id });

    res.json({
      success: true,
      message: 'Bookmark deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteBookmark controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete bookmark'
    });
  }
};
