"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[932],{932:(s,a,e)=>{e.r(a),e.d(a,{default:()=>M});var t=e(768),n=e(232);const r={class:"tasks-view"},o={class:"container"},l={key:0,class:"loading-container"},i={key:1,class:"error-container"},k={key:2,class:"empty-container"},c={key:3,class:"tasks-list"},d={key:4,class:"user-points"},u={class:"points-value"};function p(s,a,e,p,g,v){const y=(0,t.g2)("TaskCard");return(0,t.uX)(),(0,t.CE)("div",r,[(0,t.Lk)("div",o,[a[4]||(a[4]=(0,t.Lk)("h1",{class:"page-title"},"Задания",-1)),a[5]||(a[5]=(0,t.Lk)("p",{class:"page-description"},"Выполняйте задания и получайте баллы",-1)),s.loading?((0,t.uX)(),(0,t.CE)("div",l,a[1]||(a[1]=[(0,t.Lk)("div",{class:"loading-spinner large"},null,-1),(0,t.Lk)("p",null,"Загрузка заданий...",-1)]))):s.error?((0,t.uX)(),(0,t.CE)("div",i,[(0,t.Lk)("p",null,(0,n.v_)(s.error),1),(0,t.Lk)("button",{onClick:a[0]||(a[0]=(...a)=>s.fetchTasks&&s.fetchTasks(...a)),class:"retry-button"},"Повторить")])):0===s.tasks.length?((0,t.uX)(),(0,t.CE)("div",k,a[2]||(a[2]=[(0,t.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"empty-icon"},[(0,t.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,t.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),(0,t.Lk)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1),(0,t.Lk)("p",null,"Нет доступных заданий",-1)]))):((0,t.uX)(),(0,t.CE)("div",c,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.tasks,(s=>((0,t.uX)(),(0,t.Wv)(y,{key:s._id,task:s},null,8,["task"])))),128))])),s.user?((0,t.uX)(),(0,t.CE)("div",d,[(0,t.Lk)("p",null,[a[3]||(a[3]=(0,t.eW)("Ваши баллы: ")),(0,t.Lk)("span",u,(0,n.v_)(s.user.task_points),1)])])):(0,t.Q3)("",!0)])])}const g={class:"task-card"},v={class:"task-logo"},y=["src","alt"],h={key:1,class:"logo-placeholder"},w={class:"task-info"},L={class:"task-title"},m={class:"task-points"},f=["disabled"],C={key:0},T={key:1,class:"loading-spinner"};function E(s,a,e,r,o,l){return(0,t.uX)(),(0,t.CE)("div",g,[(0,t.Lk)("div",v,[s.task.logo_url?((0,t.uX)(),(0,t.CE)("img",{key:0,src:s.task.logo_url,alt:s.task.title},null,8,y)):((0,t.uX)(),(0,t.CE)("div",h,a[1]||(a[1]=[(0,t.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,t.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,t.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),(0,t.Lk)("line",{x1:"8",y1:"12",x2:"16",y2:"12"})],-1)])))]),(0,t.Lk)("div",w,[(0,t.Lk)("h3",L,(0,n.v_)(s.task.title),1),(0,t.Lk)("div",m,(0,n.v_)(s.task.points)+" "+(0,n.v_)(s.pointsText),1)]),(0,t.Lk)("button",{class:"task-button",onClick:a[0]||(a[0]=(...a)=>s.openTask&&s.openTask(...a)),disabled:s.loading},[s.loading?((0,t.uX)(),(0,t.CE)("span",T)):((0,t.uX)(),(0,t.CE)("span",C,"Выполнить"))],8,f)])}var _=e(144),x=e(657),b=e(526);const X=(0,x.nY)("tasks",{state:()=>({tasks:[],loading:!1,error:null}),actions:{async fetchTasks(){this.loading=!0,this.error=null;try{const s=await b.A.get("/tasks");s.data.success?this.tasks=s.data.data:this.error=s.data.message||"Failed to load tasks"}catch(s){this.error=s.response?.data?.message||s.message||"Unknown error",console.error("Error fetching tasks:",s)}finally{this.loading=!1}},async completeTask(s){this.loading=!0,this.error=null;try{const a=await b.A.post("/tasks/complete",{task_id:s});return a.data.success?(await this.fetchTasks(),!0):(this.error=a.data.message||"Failed to complete task",!1)}catch(a){return this.error=a.response?.data?.message||a.message||"Unknown error",console.error("Error completing task:",a),!1}finally{this.loading=!1}},openTaskLink(s){window.Telegram?.WebApp?.openLink?window.Telegram.WebApp.openLink(s):window.open(s,"_blank")}}}),W=(0,t.pM)({name:"TaskCard",props:{task:{type:Object,required:!0}},setup(s){const a=X(),e=(0,_.KR)(!1),n=(0,t.EW)((()=>{const a=s.task.points;return a%10===1&&a%100!==11?"балл":[2,3,4].includes(a%10)&&![12,13,14].includes(a%100)?"балла":"баллов"})),r=async()=>{e.value=!0;try{a.openTaskLink(s.task.link),await a.completeTask(s.task._id)}catch(t){console.error("Error completing task:",t)}finally{e.value=!1}};return{pointsText:n,openTask:r,loading:e}}});var A=e(241);const j=(0,A.A)(W,[["render",E],["__scopeId","data-v-1af51492"]]),F=j;var I=e(652);const B=(0,t.pM)({name:"TasksView",components:{TaskCard:F},setup(){const s=X(),a=(0,I.k)(),e=(0,t.EW)((()=>s.tasks)),n=(0,t.EW)((()=>s.loading)),r=(0,t.EW)((()=>s.error)),o=(0,t.EW)((()=>a.user)),l=async()=>{await s.fetchTasks()};return(0,t.sV)((()=>{l()})),{tasks:e,loading:n,error:r,user:o,fetchTasks:l}}}),K=(0,A.A)(B,[["render",p],["__scopeId","data-v-51e058c4"]]),M=K}}]);
//# sourceMappingURL=932.fa6aeb6f.js.map