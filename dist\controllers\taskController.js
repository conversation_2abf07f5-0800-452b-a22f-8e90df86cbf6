"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCompletedTasks = exports.completeTask = exports.getTasks = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const Task_1 = __importDefault(require("../models/Task"));
const CompletedTask_1 = __importDefault(require("../models/CompletedTask"));
const User_1 = __importDefault(require("../models/User"));
// Функция для проверки валидности ObjectId
const isValidObjectId = (id) => {
    return mongoose_1.default.Types.ObjectId.isValid(id);
};
// Get active tasks for user
const getTasks = async (req, res) => {
    try {
        const userId = req.user.chat_id;
        // Get all active tasks
        const activeTasks = await Task_1.default.find({ is_active: true });
        // Get tasks completed by the user
        const completedTasks = await CompletedTask_1.default.find({ user_id: userId });
        const completedTaskIds = completedTasks.map(task => task.task_id.toString());
        // Filter out completed tasks
        const availableTasks = activeTasks.filter((task) => !completedTaskIds.includes(task._id.toString()));
        res.json({
            success: true,
            data: availableTasks
        });
    }
    catch (error) {
        console.error('Error in getTasks controller:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch tasks'
        });
    }
};
exports.getTasks = getTasks;
// Complete a task
const completeTask = async (req, res) => {
    try {
        const userId = req.user.chat_id;
        const { task_id } = req.body;
        if (!task_id) {
            return res.status(400).json({
                success: false,
                message: 'Task ID is required'
            });
        }
        if (!isValidObjectId(task_id)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid task ID format'
            });
        }
        // Check if task exists and is active
        const task = await Task_1.default.findOne({ _id: task_id, is_active: true });
        if (!task) {
            return res.status(404).json({
                success: false,
                message: 'Task not found or inactive'
            });
        }
        // Check if task is already completed
        const existingCompletion = await CompletedTask_1.default.findOne({ user_id: userId, task_id });
        if (existingCompletion) {
            return res.status(400).json({
                success: false,
                message: 'Task already completed'
            });
        }
        // Create completed task record
        const completedTask = new CompletedTask_1.default({
            user_id: userId,
            task_id
        });
        await completedTask.save();
        // Update user's task points
        await User_1.default.updateOne({ chat_id: userId }, { $inc: { task_points: task.points } });
        res.status(201).json({
            success: true,
            data: {
                task_id: task._id,
                points: task.points,
                completed_at: completedTask.created_at
            }
        });
    }
    catch (error) {
        console.error('Error in completeTask controller:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to complete task'
        });
    }
};
exports.completeTask = completeTask;
// Get completed tasks
const getCompletedTasks = async (req, res) => {
    try {
        const userId = req.user.chat_id;
        // Get completed tasks with task details
        const completedTasks = await CompletedTask_1.default.find({ user_id: userId })
            .populate('task_id')
            .sort({ completed_at: -1 });
        res.json({
            success: true,
            data: completedTasks
        });
    }
    catch (error) {
        console.error('Error in getCompletedTasks controller:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch completed tasks'
        });
    }
};
exports.getCompletedTasks = getCompletedTasks;
