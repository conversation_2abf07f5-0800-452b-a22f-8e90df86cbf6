{"version": 3, "file": "js/app.117a7d5b.js", "mappings": "gFAEA,MAAMA,EAAa,CCAVC,MAAM,iBDCTC,EAAa,CCARD,MAAM,sBDEX,SAAUE,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAC3CC,GAAwBD,EAAAA,EAAAA,IAAkB,cAC1CE,GAA2BF,EAAAA,EAAAA,IAAkB,iBAEnD,OCTWP,EAAAU,mBDUNC,EAAAA,EAAAA,OCVLC,EAAAA,EAAAA,IAOM,OARRC,IAAA,EAC+BhB,OAD/BiB,EAAAA,EAAAA,IAAA,CACqC,cAAsBd,EAAAe,eDalD,ECZLC,EAAAA,EAAAA,IAIM,MAJNpB,EAIM,EAHJoB,EAAAA,EAAAA,IAEM,MAFNlB,EAEM,EADJmB,EAAAA,EAAAA,IAAeX,QAGnBW,EAAAA,EAAAA,IAAcT,IDcT,MACFG,EAAAA,EAAAA,OCbLO,EAAAA,EAAAA,IAAoDT,EAAA,CATtDI,IAAA,EASyB,eAAcb,EAAAmB,aDgB9B,KAAM,EAAG,CAAC,iBACnB,C,sBExBA,MAAMvB,EAAa,CCDZC,MAAM,eDGP,SAAUE,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMe,GAAyBb,EAAAA,EAAAA,IAAkB,eAEjD,OAAQI,EAAAA,EAAAA,OCNRC,EAAAA,EAAAA,IAyDM,MAzDNhB,EAyDM,EAxDJqB,EAAAA,EAAAA,IAQcG,EAAA,CARDC,GAAG,SAASxB,MAAM,YAAY,eAAa,UDUrD,CCZPyB,SAAAC,EAAAA,EAAAA,KAGM,IAKMtB,EAAA,KAAAA,EAAA,KALNe,EAAAA,EAAAA,IAKM,OALDnB,MAAM,QAAM,EACfmB,EAAAA,EAAAA,IAGM,OAHDQ,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDmBjJ,EClBHX,EAAAA,EAAAA,IAAoD,QAA9CY,EAAE,wCACRZ,EAAAA,EAAAA,IAAoD,YAA1Ca,OAAO,8BDqBf,IClBNb,EAAAA,EAAAA,IAAgC,OAA3BnB,MAAM,SAAQ,WAAO,OAThCiC,EAAA,KAYIb,EAAAA,EAAAA,IAOcG,EAAA,CAPDC,GAAG,aAAaxB,MAAM,YAAY,eAAa,UDwBzD,CCpCPyB,SAAAC,EAAAA,EAAAA,KAaM,IAIMtB,EAAA,KAAAA,EAAA,KAJNe,EAAAA,EAAAA,IAIM,OAJDnB,MAAM,QAAM,EACfmB,EAAAA,EAAAA,IAEM,OAFDQ,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDiCjJ,EChCHX,EAAAA,EAAAA,IAAmE,QAA7DY,EAAE,0DDmCN,IChCNZ,EAAAA,EAAAA,IAAiC,OAA5BnB,MAAM,SAAQ,YAAQ,OAlBjCiC,EAAA,KAqBIb,EAAAA,EAAAA,IAccG,EAAA,CAdDC,GAAG,WAAWxB,MAAM,YAAY,eAAa,UDsCvD,CC3DPyB,SAAAC,EAAAA,EAAAA,KAsBM,IAWMtB,EAAA,KAAAA,EAAA,KAXNe,EAAAA,EAAAA,IAWM,OAXDnB,MAAM,qBAAmB,EAC5BmB,EAAAA,EAAAA,IASM,OATDQ,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SD+CjJ,EC9CHX,EAAAA,EAAAA,IAAoE,QAA9De,EAAE,IAAIC,EAAE,IAAIC,MAAM,KAAKC,OAAO,KAAKC,GAAG,OAAOC,GAAG,UACtDpB,EAAAA,EAAAA,IAA0C,QAApCqB,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIC,GAAG,QAC9BxB,EAAAA,EAAAA,IAA4C,QAAtCqB,GAAG,KAAKC,GAAG,IAAIC,GAAG,KAAKC,GAAG,QAChCxB,EAAAA,EAAAA,IAA4C,QAAtCqB,GAAG,IAAIC,GAAG,KAAKC,GAAG,KAAKC,GAAG,QAChCxB,EAAAA,EAAAA,IAAyC,QAAnCqB,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIC,GAAG,OAC9BxB,EAAAA,EAAAA,IAA2C,QAArCqB,GAAG,IAAIC,GAAG,KAAKC,GAAG,IAAIC,GAAG,QAC/BxB,EAAAA,EAAAA,IAA6C,QAAvCqB,GAAG,KAAKC,GAAG,KAAKC,GAAG,KAAKC,GAAG,QACjCxB,EAAAA,EAAAA,IAA2C,QAArCqB,GAAG,KAAKC,GAAG,IAAIC,GAAG,KAAKC,GAAG,UD2F9B,ICxFNxB,EAAAA,EAAAA,IAAgC,OAA3BnB,MAAM,SAAQ,WAAO,OAlChCiC,EAAA,KAqCIb,EAAAA,EAAAA,IAUcG,EAAA,CAVDC,GAAG,aAAaxB,MAAM,YAAY,eAAa,UD8FzD,CCnIPyB,SAAAC,EAAAA,EAAAA,KAsCM,IAOMtB,EAAA,KAAAA,EAAA,KAPNe,EAAAA,EAAAA,IAOM,OAPDnB,MAAM,QAAM,EACfmB,EAAAA,EAAAA,IAKM,OALDQ,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDuGjJ,ECtGHX,EAAAA,EAAAA,IAA2D,QAArDY,EAAE,+CACRZ,EAAAA,EAAAA,IAAqC,UAA7ByB,GAAG,IAAIC,GAAG,IAAIC,EAAE,OACxB3B,EAAAA,EAAAA,IAA4C,QAAtCY,EAAE,gCACRZ,EAAAA,EAAAA,IAA2C,QAArCY,EAAE,kCD6GN,IC1GNZ,EAAAA,EAAAA,IAA+B,OAA1BnB,MAAM,SAAQ,UAAM,OA9C/BiC,EAAA,KAiDIb,EAAAA,EAAAA,IAQcG,EAAA,CARDC,GAAG,WAAWxB,MAAM,YAAY,eAAa,UDgHvD,CCjKPyB,SAAAC,EAAAA,EAAAA,KAkDM,IAKMtB,EAAA,KAAAA,EAAA,KALNe,EAAAA,EAAAA,IAKM,OALDnB,MAAM,QAAM,EACfmB,EAAAA,EAAAA,IAGM,OAHDQ,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDyHjJ,ECxHHX,EAAAA,EAAAA,IAA2D,QAArDY,EAAE,+CACRZ,EAAAA,EAAAA,IAAsC,UAA9ByB,GAAG,KAAKC,GAAG,IAAIC,EAAE,UD+HvB,IC5HN3B,EAAAA,EAAAA,IAAgC,OAA3BnB,MAAM,SAAQ,WAAO,OAxDhCiC,EAAA,KD0LA,CC1HA,SAAec,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,e,aC1DR,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,ICPMlD,EAAa,CCDZC,MAAM,kBDEPC,EAAa,CCDVD,MAAM,mBDETkD,EAAa,CCSRlD,MAAM,iBDRXmD,ECLN,SDOM,SAAUjD,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OAAQM,EAAAA,EAAAA,OCPRC,EAAAA,EAAAA,IAmBM,MAnBNhB,EAmBM,EAlBJoB,EAAAA,EAAAA,IAiBM,MAjBNlB,EAiBM,CDTJG,EAAO,KAAOA,EAAO,ICV3BgD,EAAAA,EAAAA,IAAA,2pBAaMjC,EAAAA,EAAAA,IAIM,MAJN+B,EAIM,EAHJ/B,EAAAA,EAAAA,IAEI,KAFAkC,KAAMlD,EAAAmD,QAASC,OAAO,SAASvD,MAAM,mBAAkB,4BAE3D,EAhBRmD,KDkBM/C,EAAO,KAAOA,EAAO,ICArBe,EAAAA,EAAAA,IAA0G,KAAvGnB,MAAM,cAAa,oFAAgF,ODG5G,CCKA,SAAe+C,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,gBACNQ,MAAO,CACLlC,YAAa,CACXmC,KAAMC,OACNjC,QAAS,aAGbkC,SAAU,CACRL,OAAAA,GACE,MAAO,gBAAgBM,KAAKtC,aAC9B,KC9BE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,I,aNYA,SAAeyB,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,MACNa,WAAY,CACVC,WAAU,EACVC,cAAaA,GAEfC,KAAAA,GACE,MAAMC,GAASC,EAAAA,EAAAA,MAGTrD,GAAmB8C,EAAAA,EAAAA,KAAS,OAE3BQ,OAAOC,WAAaD,OAAOC,SAASC,YAKpCF,OAAOC,SAASC,OAAOC,WAKgB,oBAAjCH,OAAOC,SAASC,OAAOE,OACW,oBAAlCJ,OAAOC,SAASC,OAAOG,WAQ9BlD,EAAcmD,iBAGdvD,GAAcwD,EAAAA,EAAAA,IAAI,SAClBC,GAAYC,EAAAA,EAAAA,KAGZC,EAAmBP,IACvB,IACE,MAAMQ,EAAS,IAAIC,gBAAgBT,GACnC,OAAOQ,EAAOE,IAAI,cACpB,CAAE,MAAOC,GAEP,OADAC,QAAQD,MAAM,6BAA8BA,GACrC,IACT,GAIIE,EAAoBC,IAExB,MAAMC,EAAaD,EAAWE,MAAM,uBACpC,GAAID,EAAY,CACd,MAAO,CAAEE,EAAQC,GAAMH,EAGvB,GAAe,OAAXE,GAA8B,SAAXA,EAGrB,OAFAL,QAAQO,IAAI,yBAAyBF,KAAUC,UAC/CvB,EAAOyB,KAAK,UAAUH,KAAUC,IAGpC,CAGAN,QAAQO,IAAI,uBAAwBL,EAAW,EAiDjD,OA7CAO,EAAAA,EAAAA,KAAU,KACR,GAAI9E,EAAiB+E,OAASzB,OAAOC,UAAYD,OAAOC,SAASC,OAAQ,CAEvE,MAAMwB,EAAK1B,OAAOC,SAAUC,OAG5BnD,EAAY0E,MAAQC,EAAG3E,aAAe,QAGtC2E,EAAGC,4BAGHD,EAAGrB,SAGHuB,SAASC,gBAAgBC,UAAYJ,EAAG3E,YAGxC,MAAMkE,EAAaP,EAAgBgB,EAAGvB,UAClCc,IACFF,QAAQO,IAAI,qBAAsBL,GAGlCc,YAAW,KACTf,EAAiBC,EAAW,GAC3B,MAILT,EAAUwB,WAGVN,EAAGO,QAAQ,gBAAgB,KACzBlF,EAAY0E,MAAQC,EAAG3E,YACvB6E,SAASC,gBAAgBC,UAAYJ,EAAG3E,WAAW,GAKvD,MAEE6E,SAASC,gBAAgBC,UAAY,OACvC,IAGK,CACLpF,mBACAK,cACAI,cAEJ,IOpII,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASpB,KAEpE,ICPMmG,EAAgC,CACpC,CACEC,KAAM,IACNC,SAAU,YAEZ,CACED,KAAM,SACNtD,KAAM,QACNwD,UAAWA,IAAM,8BAEnB,CACEF,KAAM,aACNtD,KAAM,YACNwD,UAAWA,IAAM,8BAEnB,CACEF,KAAM,WACNtD,KAAM,UACNwD,UAAWA,IAAM,8BAEnB,CACEF,KAAM,aACNtD,KAAM,YACNwD,UAAWA,IAAM,8BAEnB,CACEF,KAAM,WACNtD,KAAM,UACNwD,UAAWA,IAAM,8BAEnB,CACEF,KAAM,qBACNtD,KAAM,QACNwD,UAAWA,IAAM,8BAEnB,CACEF,KAAM,mBACNtD,KAAM,WACNuD,SAAU,aAIRtC,GAASwC,EAAAA,EAAAA,IAAa,CAC1BC,SAASC,EAAAA,EAAAA,IAAiB,KAC1BN,WAGF,IC1CMO,GAAMC,EAAAA,EAAAA,IAAUC,GAGhBC,GAAQC,EAAAA,EAAAA,MACdJ,EAAIK,IAAIF,GAGRH,EAAIK,IAAIhD,GAGR2C,EAAIM,MAAM,O,8CCLV,MAAMC,EAAMC,EAAAA,EAAMC,OAAO,CACvBC,QAAS7C,oCACT8C,iBAAiB,EACjBC,QAAS,CACP,eAAgB,sBAKpBL,EAAIM,aAAaC,QAAQT,KAAKU,IAExBxD,OAAOC,UAAYD,OAAOC,SAASC,SAErCsD,EAAOH,QAAQ,kBAAoBrD,OAAOC,SAASC,OAAQC,UAEtDqD,KACL1C,GACK2C,QAAQC,OAAO5C,KAGxB,S,uDCNO,MAAML,GAAekD,EAAAA,EAAAA,IAAY,OAAQ,CAC9CC,MAAOA,KAAA,CACLC,KAAM,KACNC,UAAW,KACXC,SAAS,EACTjD,MAAO,OAGTkD,QAAS,CACPC,aAAeL,IACb,IAAKA,EAAMC,KAAM,MAAO,GACxB,MAAM1G,EAAcmD,iBACpB,MAAO,gBAAgBnD,WAAqByG,EAAMC,KAAKK,SAAS,GAIpEC,QAAS,CACP,cAAMnC,GAKJ,GAJAvC,KAAKsE,SAAU,EACftE,KAAKqB,MAAQ,MAGRd,OAAOC,WAAaD,OAAOC,SAASC,OAGvC,OAFAT,KAAKqB,MAAQ,wCACbrB,KAAKsE,SAAU,GAIjB,IACE,MAAMK,QAAiBpB,EAAAA,EAAInC,IAAI,aAE3BuD,EAASC,KAAKC,QAChB7E,KAAKoE,KAAOO,EAASC,KAAKA,KAE1B5E,KAAKqB,MAAQsD,EAASC,KAAKE,SAAW,0BAE1C,CAAE,MAAOzD,GACPrB,KAAKqB,MAAQA,EAAMsD,UAAUC,MAAME,SAAWzD,EAAMyD,SAAW,gBAC/DxD,QAAQD,MAAM,2BAA4BA,EAC5C,CAAE,QACArB,KAAKsE,SAAU,CACjB,CACF,EAEA,oBAAMS,GAKJ,GAJA/E,KAAKsE,SAAU,EACftE,KAAKqB,MAAQ,MAGRd,OAAOC,WAAaD,OAAOC,SAASC,OAGvC,OAFAT,KAAKqB,MAAQ,wCACbrB,KAAKsE,SAAU,GAIjB,IACE,MAAMK,QAAiBpB,EAAAA,EAAInC,IAAI,oBAE3BuD,EAASC,KAAKC,QAChB7E,KAAKqE,UAAYM,EAASC,KAAKA,KAE/B5E,KAAKqB,MAAQsD,EAASC,KAAKE,SAAW,0BAE1C,CAAE,MAAOzD,GACPrB,KAAKqB,MAAQA,EAAMsD,UAAUC,MAAME,SAAWzD,EAAMyD,SAAW,gBAC/DxD,QAAQD,MAAM,4BAA6BA,EAC7C,CAAE,QACArB,KAAKsE,SAAU,CACjB,CACF,I,GC9FAU,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUI,EAAQA,EAAOD,QAASJ,GAG/CK,EAAOD,OACf,CAGAJ,EAAoBO,EAAID,E,MCzBxB,IAAIE,EAAW,GACfR,EAAoBS,EAAI,CAACC,EAAQC,EAAUC,EAAIC,KAC9C,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIR,EAASS,OAAQD,IAAK,CAGzC,IAFA,IAAKL,EAAUC,EAAIC,GAAYL,EAASQ,GACpCE,GAAY,EACPC,EAAI,EAAGA,EAAIR,EAASM,OAAQE,MACpB,EAAXN,GAAsBC,GAAgBD,IAAaO,OAAOC,KAAKrB,EAAoBS,GAAGa,OAAOnJ,GAAS6H,EAAoBS,EAAEtI,GAAKwI,EAASQ,MAC9IR,EAASY,OAAOJ,IAAK,IAErBD,GAAY,EACTL,EAAWC,IAAcA,EAAeD,IAG7C,GAAGK,EAAW,CACbV,EAASe,OAAOP,IAAK,GACrB,IAAI/G,EAAI2G,SACET,IAANlG,IAAiByG,EAASzG,EAC/B,CACD,CACA,OAAOyG,CAnBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIR,EAASS,OAAQD,EAAI,GAAKR,EAASQ,EAAI,GAAG,GAAKH,EAAUG,IAAKR,EAASQ,GAAKR,EAASQ,EAAI,GACrGR,EAASQ,GAAK,CAACL,EAAUC,EAAIC,EAqBjB,C,WCzBdb,EAAoB9G,EAAI,CAACkH,EAASoB,KACjC,IAAI,IAAIrJ,KAAOqJ,EACXxB,EAAoByB,EAAED,EAAYrJ,KAAS6H,EAAoByB,EAAErB,EAASjI,IAC5EiJ,OAAOM,eAAetB,EAASjI,EAAK,CAAEwJ,YAAY,EAAMxF,IAAKqF,EAAWrJ,IAE1E,C,WCND6H,EAAoB4B,EAAI,CAAC,EAGzB5B,EAAoB6B,EAAKC,GACjB/C,QAAQgD,IAAIX,OAAOC,KAAKrB,EAAoB4B,GAAGI,QAAO,CAACC,EAAU9J,KACvE6H,EAAoB4B,EAAEzJ,GAAK2J,EAASG,GAC7BA,IACL,I,WCNJjC,EAAoBkC,EAAKJ,GAEjB,MAAQA,EAAU,IAAM,CAAC,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,K,WCFnJ9B,EAAoBmC,SAAYL,GAExB,OAASA,EAAU,IAAM,CAAC,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,M,WCHpJ9B,EAAoBoC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOtH,MAAQ,IAAIuH,SAAS,cAAb,EAChB,CAAE,MAAOT,GACR,GAAsB,kBAAXvG,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,WCAxB0E,EAAoByB,EAAI,CAACc,EAAKC,IAAUpB,OAAOqB,UAAUC,eAAeC,KAAKJ,EAAKC,E,WCAlF,IAAII,EAAa,CAAC,EACdC,EAAoB,uBAExB7C,EAAoB8C,EAAI,CAACC,EAAKC,EAAM7K,EAAK2J,KACxC,GAAGc,EAAWG,GAAQH,EAAWG,GAAKlG,KAAKmG,OAA3C,CACA,IAAIC,EAAQC,EACZ,QAAW/C,IAARhI,EAEF,IADA,IAAIgL,EAAUjG,SAASkG,qBAAqB,UACpCpC,EAAI,EAAGA,EAAImC,EAAQlC,OAAQD,IAAK,CACvC,IAAIqC,EAAIF,EAAQnC,GAChB,GAAGqC,EAAEC,aAAa,QAAUP,GAAOM,EAAEC,aAAa,iBAAmBT,EAAoB1K,EAAK,CAAE8K,EAASI,EAAG,KAAO,CACpH,CAEGJ,IACHC,GAAa,EACbD,EAAS/F,SAASqG,cAAc,UAEhCN,EAAOO,QAAU,QACjBP,EAAOQ,QAAU,IACbzD,EAAoB0D,IACvBT,EAAOU,aAAa,QAAS3D,EAAoB0D,IAElDT,EAAOU,aAAa,eAAgBd,EAAoB1K,GAExD8K,EAAOW,IAAMb,GAEdH,EAAWG,GAAO,CAACC,GACnB,IAAIa,EAAmB,CAACC,EAAMC,KAE7Bd,EAAOe,QAAUf,EAAOgB,OAAS,KACjCC,aAAaT,GACb,IAAIU,EAAUvB,EAAWG,GAIzB,UAHOH,EAAWG,GAClBE,EAAOmB,YAAcnB,EAAOmB,WAAWC,YAAYpB,GACnDkB,GAAWA,EAAQG,SAAS1D,GAAQA,EAAGmD,KACpCD,EAAM,OAAOA,EAAKC,EAAM,EAExBN,EAAUpG,WAAWwG,EAAiBU,KAAK,UAAMpE,EAAW,CAAEvF,KAAM,UAAWF,OAAQuI,IAAW,MACtGA,EAAOe,QAAUH,EAAiBU,KAAK,KAAMtB,EAAOe,SACpDf,EAAOgB,OAASJ,EAAiBU,KAAK,KAAMtB,EAAOgB,QACnDf,GAAchG,SAASsH,KAAKC,YAAYxB,EApCkB,CAoCX,C,WCvChDjD,EAAoB/F,EAAKmG,IACH,qBAAXsE,QAA0BA,OAAOC,aAC1CvD,OAAOM,eAAetB,EAASsE,OAAOC,YAAa,CAAE5H,MAAO,WAE7DqE,OAAOM,eAAetB,EAAS,aAAc,CAAErD,OAAO,GAAO,C,WCL9DiD,EAAoB4E,EAAI,G,WCAxB,GAAwB,qBAAb1H,SAAX,CACA,IAAI2H,EAAmB,CAAC/C,EAASgD,EAAUC,EAAQC,EAAShG,KAC3D,IAAIiG,EAAU/H,SAASqG,cAAc,QAErC0B,EAAQC,IAAM,aACdD,EAAQrK,KAAO,WACXoF,EAAoB0D,KACvBuB,EAAQE,MAAQnF,EAAoB0D,IAErC,IAAI0B,EAAkBrB,IAGrB,GADAkB,EAAQjB,QAAUiB,EAAQhB,OAAS,KAChB,SAAfF,EAAMnJ,KACToK,QACM,CACN,IAAIK,EAAYtB,GAASA,EAAMnJ,KAC3B0K,EAAWvB,GAASA,EAAMrJ,QAAUqJ,EAAMrJ,OAAOF,MAAQsK,EACzDS,EAAM,IAAIC,MAAM,qBAAuB1D,EAAU,cAAgBuD,EAAY,KAAOC,EAAW,KACnGC,EAAIpL,KAAO,iBACXoL,EAAIE,KAAO,wBACXF,EAAI3K,KAAOyK,EACXE,EAAI1G,QAAUyG,EACVL,EAAQb,YAAYa,EAAQb,WAAWC,YAAYY,GACvDjG,EAAOuG,EACR,GAWD,OATAN,EAAQjB,QAAUiB,EAAQhB,OAASmB,EACnCH,EAAQzK,KAAOsK,EAGXC,EACHA,EAAOX,WAAWsB,aAAaT,EAASF,EAAOY,aAE/CzI,SAASsH,KAAKC,YAAYQ,GAEpBA,CAAO,EAEXW,EAAiB,CAACpL,EAAMsK,KAE3B,IADA,IAAIe,EAAmB3I,SAASkG,qBAAqB,QAC7CpC,EAAI,EAAGA,EAAI6E,EAAiB5E,OAAQD,IAAK,CAChD,IAAI8E,EAAMD,EAAiB7E,GACvB+E,EAAWD,EAAIxC,aAAa,cAAgBwC,EAAIxC,aAAa,QACjE,GAAe,eAAZwC,EAAIZ,MAAyBa,IAAavL,GAAQuL,IAAajB,GAAW,OAAOgB,CACrF,CACA,IAAIE,EAAoB9I,SAASkG,qBAAqB,SACtD,IAAQpC,EAAI,EAAGA,EAAIgF,EAAkB/E,OAAQD,IAAK,CAC7C8E,EAAME,EAAkBhF,GACxB+E,EAAWD,EAAIxC,aAAa,aAChC,GAAGyC,IAAavL,GAAQuL,IAAajB,EAAU,OAAOgB,CACvD,GAEGG,EAAkBnE,GACd,IAAI/C,SAAQ,CAACiG,EAAShG,KAC5B,IAAIxE,EAAOwF,EAAoBmC,SAASL,GACpCgD,EAAW9E,EAAoB4E,EAAIpK,EACvC,GAAGoL,EAAepL,EAAMsK,GAAW,OAAOE,IAC1CH,EAAiB/C,EAASgD,EAAU,KAAME,EAAShG,EAAO,IAIxDkH,EAAqB,CACxB,IAAK,GAGNlG,EAAoB4B,EAAEuE,QAAU,CAACrE,EAASG,KACzC,IAAImE,EAAY,CAAC,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,GAC5DF,EAAmBpE,GAAUG,EAASpF,KAAKqJ,EAAmBpE,IACzB,IAAhCoE,EAAmBpE,IAAkBsE,EAAUtE,IACtDG,EAASpF,KAAKqJ,EAAmBpE,GAAWmE,EAAenE,GAASuE,MAAK,KACxEH,EAAmBpE,GAAW,CAAC,IAC5BD,IAEH,aADOqE,EAAmBpE,GACpBD,CAAC,IAET,CA1E0C,C,WCK3C,IAAIyE,EAAkB,CACrB,IAAK,GAGNtG,EAAoB4B,EAAET,EAAI,CAACW,EAASG,KAElC,IAAIsE,EAAqBvG,EAAoByB,EAAE6E,EAAiBxE,GAAWwE,EAAgBxE,QAAW3B,EACtG,GAA0B,IAAvBoG,EAGF,GAAGA,EACFtE,EAASpF,KAAK0J,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIzH,SAAQ,CAACiG,EAAShG,IAAYuH,EAAqBD,EAAgBxE,GAAW,CAACkD,EAAShG,KAC1GiD,EAASpF,KAAK0J,EAAmB,GAAKC,GAGtC,IAAIzD,EAAM/C,EAAoB4E,EAAI5E,EAAoBkC,EAAEJ,GAEpD1F,EAAQ,IAAIoJ,MACZiB,EAAgB1C,IACnB,GAAG/D,EAAoByB,EAAE6E,EAAiBxE,KACzCyE,EAAqBD,EAAgBxE,GACX,IAAvByE,IAA0BD,EAAgBxE,QAAW3B,GACrDoG,GAAoB,CACtB,IAAIlB,EAAYtB,IAAyB,SAAfA,EAAMnJ,KAAkB,UAAYmJ,EAAMnJ,MAChE8L,EAAU3C,GAASA,EAAMrJ,QAAUqJ,EAAMrJ,OAAOkJ,IACpDxH,EAAMyD,QAAU,iBAAmBiC,EAAU,cAAgBuD,EAAY,KAAOqB,EAAU,IAC1FtK,EAAMjC,KAAO,iBACbiC,EAAMxB,KAAOyK,EACbjJ,EAAMyC,QAAU6H,EAChBH,EAAmB,GAAGnK,EACvB,CACD,EAED4D,EAAoB8C,EAAEC,EAAK0D,EAAc,SAAW3E,EAASA,EAE/D,CACD,EAWF9B,EAAoBS,EAAEU,EAAKW,GAA0C,IAA7BwE,EAAgBxE,GAGxD,IAAI6E,EAAuB,CAACC,EAA4BjH,KACvD,IAGIM,EAAU6B,GAHTnB,EAAUkG,EAAaC,GAAWnH,EAGhBqB,EAAI,EAC3B,GAAGL,EAASoG,MAAMpK,GAAgC,IAAxB2J,EAAgB3J,KAAa,CACtD,IAAIsD,KAAY4G,EACZ7G,EAAoByB,EAAEoF,EAAa5G,KACrCD,EAAoBO,EAAEN,GAAY4G,EAAY5G,IAGhD,GAAG6G,EAAS,IAAIpG,EAASoG,EAAQ9G,EAClC,CAEA,IADG4G,GAA4BA,EAA2BjH,GACrDqB,EAAIL,EAASM,OAAQD,IACzBc,EAAUnB,EAASK,GAChBhB,EAAoByB,EAAE6E,EAAiBxE,IAAYwE,EAAgBxE,IACrEwE,EAAgBxE,GAAS,KAE1BwE,EAAgBxE,GAAW,EAE5B,OAAO9B,EAAoBS,EAAEC,EAAO,EAGjCsG,EAAqBC,KAAK,mCAAqCA,KAAK,oCAAsC,GAC9GD,EAAmB1C,QAAQqC,EAAqBpC,KAAK,KAAM,IAC3DyC,EAAmBnK,KAAO8J,EAAqBpC,KAAK,KAAMyC,EAAmBnK,KAAK0H,KAAKyC,G,KClFvF,IAAIE,EAAsBlH,EAAoBS,OAAEN,EAAW,CAAC,MAAM,IAAOH,EAAoB,OAC7FkH,EAAsBlH,EAAoBS,EAAEyG,E", "sources": ["webpack://cinema-bot-frontend/./src/App.vue?1969", "webpack://cinema-bot-frontend/./src/App.vue", "webpack://cinema-bot-frontend/./src/components/BottomMenu.vue?dbe7", "webpack://cinema-bot-frontend/./src/components/BottomMenu.vue", "webpack://cinema-bot-frontend/./src/components/BottomMenu.vue?ffaa", "webpack://cinema-bot-frontend/./src/components/TelegramError.vue?ad39", "webpack://cinema-bot-frontend/./src/components/TelegramError.vue", "webpack://cinema-bot-frontend/./src/components/TelegramError.vue?e15e", "webpack://cinema-bot-frontend/./src/App.vue?7ccd", "webpack://cinema-bot-frontend/./src/router/index.ts", "webpack://cinema-bot-frontend/./src/main.ts", "webpack://cinema-bot-frontend/./src/services/api.ts", "webpack://cinema-bot-frontend/./src/store/user.ts", "webpack://cinema-bot-frontend/webpack/bootstrap", "webpack://cinema-bot-frontend/webpack/runtime/chunk loaded", "webpack://cinema-bot-frontend/webpack/runtime/define property getters", "webpack://cinema-bot-frontend/webpack/runtime/ensure chunk", "webpack://cinema-bot-frontend/webpack/runtime/get javascript chunk filename", "webpack://cinema-bot-frontend/webpack/runtime/get mini-css chunk filename", "webpack://cinema-bot-frontend/webpack/runtime/global", "webpack://cinema-bot-frontend/webpack/runtime/hasOwnProperty shorthand", "webpack://cinema-bot-frontend/webpack/runtime/load script", "webpack://cinema-bot-frontend/webpack/runtime/make namespace object", "webpack://cinema-bot-frontend/webpack/runtime/publicPath", "webpack://cinema-bot-frontend/webpack/runtime/css loading", "webpack://cinema-bot-frontend/webpack/runtime/jsonp chunk loading", "webpack://cinema-bot-frontend/webpack/startup"], "sourcesContent": ["import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"app-container\" }\nconst _hoisted_2 = { class: \"scrollable-content\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_router_view = _resolveComponent(\"router-view\")!\n  const _component_BottomMenu = _resolveComponent(\"BottomMenu\")!\n  const _component_TelegramError = _resolveComponent(\"TelegramError\")!\n\n  return (_ctx.isTelegramWebApp)\n    ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: _normalizeClass([\"app-wrapper\", _ctx.colorScheme])\n      }, [\n        _createElementVNode(\"div\", _hoisted_1, [\n          _createElementVNode(\"div\", _hoisted_2, [\n            _createVNode(_component_router_view)\n          ])\n        ]),\n        _createVNode(_component_BottomMenu)\n      ], 2))\n    : (_openBlock(), _createBlock(_component_TelegramError, {\n        key: 1,\n        \"bot-username\": _ctx.botUsername\n      }, null, 8, [\"bot-username\"]))\n}", "<template>\n  <div v-if=\"isTelegramWebApp\" class=\"app-wrapper\" :class=\"colorScheme\">\n    <div class=\"app-container\">\n      <div class=\"scrollable-content\">\n        <router-view />\n      </div>\n    </div>\n    <BottomMenu />\n  </div>\n  <TelegramError v-else :bot-username=\"botUsername\" />\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, onMounted, ref, computed } from 'vue';\nimport { useRouter } from 'vue-router';\nimport BottomMenu from '@/components/BottomMenu.vue';\nimport TelegramError from '@/components/TelegramError.vue';\nimport { useUserStore } from '@/store/user';\n\n// Global declaration уже есть в services/api.ts\n\nexport default defineComponent({\n  name: 'App',\n  components: {\n    BottomMenu,\n    TelegramError\n  },\n  setup() {\n    const router = useRouter();\n\n    // Проверка наличия Telegram WebApp с дополнительной валидацией\n    const isTelegramWebApp = computed(() => {\n      // Проверяем наличие объекта Telegram и WebApp\n      if (!window.Telegram || !window.Telegram.WebApp) {\n        return false;\n      }\n\n      // Проверяем наличие initData - это ключевой признак, что приложение запущено из Telegram\n      if (!window.Telegram.WebApp.initData) {\n        return false;\n      }\n\n      // Проверяем наличие методов, которые должны быть в Telegram WebApp\n      if (typeof window.Telegram.WebApp.close !== 'function' ||\n          typeof window.Telegram.WebApp.expand !== 'function') {\n        return false;\n      }\n\n      return true;\n    });\n\n    // Имя бота для ссылки в сообщении об ошибке\n    const botUsername = process.env.VUE_APP_TELEGRAM_BOT_USERNAME || 'your_bot';\n\n    // Значения по умолчанию, если Telegram WebApp недоступен\n    const colorScheme = ref('light');\n    const userStore = useUserStore();\n\n    // Функция для парсинга параметров запуска мини-приложения\n    const parseStartParam = (initData: string): string | null => {\n      try {\n        const params = new URLSearchParams(initData);\n        return params.get('start_param');\n      } catch (error) {\n        console.error('Error parsing start_param:', error);\n        return null;\n      }\n    };\n\n    // Функция для обработки параметров запуска и перенаправления\n    const handleStartParam = (startParam: string) => {\n      // Проверяем, является ли параметр ссылкой на фильм\n      const movieMatch = startParam.match(/^see_([a-z]+)_(.+)$/);\n      if (movieMatch) {\n        const [, source, id] = movieMatch;\n\n        // Проверяем, что source является валидным (kp или imdb)\n        if (source === 'kp' || source === 'imdb') {\n          console.log(`Redirecting to movie: ${source}/${id}`);\n          router.push(`/movie/${source}/${id}`);\n          return;\n        }\n      }\n\n      // Если параметр не распознан, логируем это\n      console.log('Unknown start_param:', startParam);\n    };\n\n    // Initialize Telegram Web App\n    onMounted(() => {\n      if (isTelegramWebApp.value && window.Telegram && window.Telegram.WebApp) {\n        // Используем non-null assertion operator, так как мы уже проверили наличие WebApp\n        const tg = window.Telegram!.WebApp!;\n\n        // Update color scheme\n        colorScheme.value = tg.colorScheme || 'light';\n\n        // Enable closing confirmation\n        tg.enableClosingConfirmation();\n\n        // Expand WebApp to full screen\n        tg.expand();\n\n        // Set theme class\n        document.documentElement.className = tg.colorScheme;\n\n        // Проверяем параметры запуска мини-приложения\n        const startParam = parseStartParam(tg.initData);\n        if (startParam) {\n          console.log('Found start_param:', startParam);\n          // Обрабатываем параметр запуска после небольшой задержки,\n          // чтобы дать время роутеру инициализироваться\n          setTimeout(() => {\n            handleStartParam(startParam);\n          }, 100);\n        }\n\n        // Initialize user data\n        userStore.initUser();\n\n        // Listen for theme changes\n        tg.onEvent('themeChanged', () => {\n          colorScheme.value = tg.colorScheme;\n          document.documentElement.className = tg.colorScheme;\n        });\n\n        // Вызываем expand() только один раз при запуске приложения\n        // Это достаточно, так как мы используем CSS для предотвращения сворачивания\n      } else {\n        // Установка светлой темы по умолчанию, если не в Telegram\n        document.documentElement.className = 'light';\n      }\n    });\n\n    return {\n      isTelegramWebApp,\n      colorScheme,\n      botUsername\n    };\n  }\n});\n</script>\n\n<style>\n/* Глобальные стили для предотвращения проблем с прокруткой в iOS */\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n  width: 100%;\n  overscroll-behavior: none; /* Предотвращает \"отскок\" при прокрутке */\n  touch-action: pan-y; /* Разрешает только вертикальную прокрутку */\n}\n\n/* Основной контейнер приложения */\n.app-wrapper {\n  font-family: 'Roboto', sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  height: 100vh;\n  width: 100%;\n  position: relative;\n}\n\n/* Контейнер с фиксированным позиционированием */\n.app-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: calc(100% - 60px); /* Вычитаем высоту нижнего меню */\n  overflow: hidden;\n  z-index: 1;\n}\n\n/* Контейнер с прокруткой */\n.scrollable-content {\n  height: 100%;\n  width: 100%;\n  overflow-y: auto;\n  overflow-x: hidden;\n  -webkit-overflow-scrolling: touch; /* Плавная прокрутка на iOS */\n  overscroll-behavior: contain; /* Предотвращает \"отскок\" при прокрутке */\n  padding-bottom: 20px; /* Дополнительный отступ внизу */\n}\n\n/* Темы */\n.app-wrapper.dark {\n  background-color: var(--tg-theme-bg-color, #212121);\n  color: var(--tg-theme-text-color, #ffffff);\n}\n\n.app-wrapper.light {\n  background-color: var(--tg-theme-bg-color, #ffffff);\n  color: var(--tg-theme-text-color, #000000);\n}\n</style>\n", "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"bottom-menu\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_router_link = _resolveComponent(\"router-link\")!\n\n  return (_openBlock(), _createElementBlock(\"nav\", _hoisted_1, [\n    _createVNode(_component_router_link, {\n      to: \"/tasks\",\n      class: \"menu-item\",\n      \"active-class\": \"active\"\n    }, {\n      default: _withCtx(() => _cache[0] || (_cache[0] = [\n        _createElementVNode(\"div\", { class: \"icon\" }, [\n          _createElementVNode(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            \"stroke-width\": \"2\",\n            \"stroke-linecap\": \"round\",\n            \"stroke-linejoin\": \"round\"\n          }, [\n            _createElementVNode(\"path\", { d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\" }),\n            _createElementVNode(\"polyline\", { points: \"22 4 12 14.01 9 11.01\" })\n          ])\n        ], -1),\n        _createElementVNode(\"div\", { class: \"label\" }, \"Задания\", -1)\n      ])),\n      _: 1\n    }),\n    _createVNode(_component_router_link, {\n      to: \"/bookmarks\",\n      class: \"menu-item\",\n      \"active-class\": \"active\"\n    }, {\n      default: _withCtx(() => _cache[1] || (_cache[1] = [\n        _createElementVNode(\"div\", { class: \"icon\" }, [\n          _createElementVNode(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            \"stroke-width\": \"2\",\n            \"stroke-linecap\": \"round\",\n            \"stroke-linejoin\": \"round\"\n          }, [\n            _createElementVNode(\"path\", { d: \"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\" })\n          ])\n        ], -1),\n        _createElementVNode(\"div\", { class: \"label\" }, \"Закладки\", -1)\n      ])),\n      _: 1\n    }),\n    _createVNode(_component_router_link, {\n      to: \"/catalog\",\n      class: \"menu-item\",\n      \"active-class\": \"active\"\n    }, {\n      default: _withCtx(() => _cache[2] || (_cache[2] = [\n        _createElementVNode(\"div\", { class: \"icon catalog-icon\" }, [\n          _createElementVNode(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            \"stroke-width\": \"2\",\n            \"stroke-linecap\": \"round\",\n            \"stroke-linejoin\": \"round\"\n          }, [\n            _createElementVNode(\"rect\", {\n              x: \"2\",\n              y: \"2\",\n              width: \"20\",\n              height: \"20\",\n              rx: \"2.18\",\n              ry: \"2.18\"\n            }),\n            _createElementVNode(\"line\", {\n              x1: \"7\",\n              y1: \"2\",\n              x2: \"7\",\n              y2: \"22\"\n            }),\n            _createElementVNode(\"line\", {\n              x1: \"17\",\n              y1: \"2\",\n              x2: \"17\",\n              y2: \"22\"\n            }),\n            _createElementVNode(\"line\", {\n              x1: \"2\",\n              y1: \"12\",\n              x2: \"22\",\n              y2: \"12\"\n            }),\n            _createElementVNode(\"line\", {\n              x1: \"2\",\n              y1: \"7\",\n              x2: \"7\",\n              y2: \"7\"\n            }),\n            _createElementVNode(\"line\", {\n              x1: \"2\",\n              y1: \"17\",\n              x2: \"7\",\n              y2: \"17\"\n            }),\n            _createElementVNode(\"line\", {\n              x1: \"17\",\n              y1: \"17\",\n              x2: \"22\",\n              y2: \"17\"\n            }),\n            _createElementVNode(\"line\", {\n              x1: \"17\",\n              y1: \"7\",\n              x2: \"22\",\n              y2: \"7\"\n            })\n          ])\n        ], -1),\n        _createElementVNode(\"div\", { class: \"label\" }, \"Каталог\", -1)\n      ])),\n      _: 1\n    }),\n    _createVNode(_component_router_link, {\n      to: \"/referrals\",\n      class: \"menu-item\",\n      \"active-class\": \"active\"\n    }, {\n      default: _withCtx(() => _cache[3] || (_cache[3] = [\n        _createElementVNode(\"div\", { class: \"icon\" }, [\n          _createElementVNode(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            \"stroke-width\": \"2\",\n            \"stroke-linecap\": \"round\",\n            \"stroke-linejoin\": \"round\"\n          }, [\n            _createElementVNode(\"path\", { d: \"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\" }),\n            _createElementVNode(\"circle\", {\n              cx: \"9\",\n              cy: \"7\",\n              r: \"4\"\n            }),\n            _createElementVNode(\"path\", { d: \"M23 21v-2a4 4 0 0 0-3-3.87\" }),\n            _createElementVNode(\"path\", { d: \"M16 3.13a4 4 0 0 1 0 7.75\" })\n          ])\n        ], -1),\n        _createElementVNode(\"div\", { class: \"label\" }, \"Друзья\", -1)\n      ])),\n      _: 1\n    }),\n    _createVNode(_component_router_link, {\n      to: \"/profile\",\n      class: \"menu-item\",\n      \"active-class\": \"active\"\n    }, {\n      default: _withCtx(() => _cache[4] || (_cache[4] = [\n        _createElementVNode(\"div\", { class: \"icon\" }, [\n          _createElementVNode(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            \"stroke-width\": \"2\",\n            \"stroke-linecap\": \"round\",\n            \"stroke-linejoin\": \"round\"\n          }, [\n            _createElementVNode(\"path\", { d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\" }),\n            _createElementVNode(\"circle\", {\n              cx: \"12\",\n              cy: \"7\",\n              r: \"4\"\n            })\n          ])\n        ], -1),\n        _createElementVNode(\"div\", { class: \"label\" }, \"Профиль\", -1)\n      ])),\n      _: 1\n    })\n  ]))\n}", "<template>\n  <nav class=\"bottom-menu\">\n    <router-link to=\"/tasks\" class=\"menu-item\" active-class=\"active\">\n      <div class=\"icon\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\n          <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\n        </svg>\n      </div>\n      <div class=\"label\">Задания</div>\n    </router-link>\n    \n    <router-link to=\"/bookmarks\" class=\"menu-item\" active-class=\"active\">\n      <div class=\"icon\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <path d=\"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\"></path>\n        </svg>\n      </div>\n      <div class=\"label\">Закладки</div>\n    </router-link>\n    \n    <router-link to=\"/catalog\" class=\"menu-item\" active-class=\"active\">\n      <div class=\"icon catalog-icon\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" rx=\"2.18\" ry=\"2.18\"></rect>\n          <line x1=\"7\" y1=\"2\" x2=\"7\" y2=\"22\"></line>\n          <line x1=\"17\" y1=\"2\" x2=\"17\" y2=\"22\"></line>\n          <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\"></line>\n          <line x1=\"2\" y1=\"7\" x2=\"7\" y2=\"7\"></line>\n          <line x1=\"2\" y1=\"17\" x2=\"7\" y2=\"17\"></line>\n          <line x1=\"17\" y1=\"17\" x2=\"22\" y2=\"17\"></line>\n          <line x1=\"17\" y1=\"7\" x2=\"22\" y2=\"7\"></line>\n        </svg>\n      </div>\n      <div class=\"label\">Каталог</div>\n    </router-link>\n    \n    <router-link to=\"/referrals\" class=\"menu-item\" active-class=\"active\">\n      <div class=\"icon\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n          <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n          <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n          <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n        </svg>\n      </div>\n      <div class=\"label\">Друзья</div>\n    </router-link>\n    \n    <router-link to=\"/profile\" class=\"menu-item\" active-class=\"active\">\n      <div class=\"icon\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n          <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n        </svg>\n      </div>\n      <div class=\"label\">Профиль</div>\n    </router-link>\n  </nav>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from 'vue';\n\nexport default defineComponent({\n  name: 'BottomMenu'\n});\n</script>\n\n<style scoped>\n.bottom-menu {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: space-around;\n  background-color: var(--tg-theme-bg-color);\n  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);\n  padding: 8px 0;\n  z-index: 100;\n}\n\n.menu-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  color: var(--tg-theme-hint-color);\n  text-decoration: none;\n  width: 20%;\n  padding: 5px 0;\n  transition: color 0.2s;\n}\n\n.menu-item.active {\n  color: var(--tg-theme-button-color);\n}\n\n.icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 4px;\n}\n\n.catalog-icon {\n  transform: scale(0.9);\n}\n\n.label {\n  font-size: 12px;\n  text-align: center;\n}\n\nsvg {\n  width: 100%;\n  height: 100%;\n}\n</style>\n", "import { render } from \"./BottomMenu.vue?vue&type=template&id=8074b3c6&scoped=true&ts=true\"\nimport script from \"./BottomMenu.vue?vue&type=script&lang=ts\"\nexport * from \"./BottomMenu.vue?vue&type=script&lang=ts\"\n\nimport \"./BottomMenu.vue?vue&type=style&index=0&id=8074b3c6&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-8074b3c6\"]])\n\nexport default __exports__", "import { createElementVNode as _createElementVNode, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"telegram-error\" }\nconst _hoisted_2 = { class: \"error-container\" }\nconst _hoisted_3 = { class: \"telegram-link\" }\nconst _hoisted_4 = [\"href\"]\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[0] || (_cache[0] = _createStaticVNode(\"<div class=\\\"error-icon\\\" data-v-28a7f8f4><svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"64\\\" height=\\\"64\\\" viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" data-v-28a7f8f4><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\" data-v-28a7f8f4></circle><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"12\\\" data-v-28a7f8f4></line><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12.01\\\" y2=\\\"16\\\" data-v-28a7f8f4></line></svg></div><h1 data-v-28a7f8f4>Доступ только через Telegram</h1><p data-v-28a7f8f4>Это приложение работает только при запуске из Telegram Mini App.</p><p data-v-28a7f8f4>Пожалуйста, откройте бота в Telegram и запустите приложение оттуда.</p>\", 4)),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createElementVNode(\"a\", {\n          href: _ctx.botLink,\n          target: \"_blank\",\n          class: \"telegram-button\"\n        }, \" Открыть бота в Telegram \", 8, _hoisted_4)\n      ]),\n      _cache[1] || (_cache[1] = _createElementVNode(\"p\", { class: \"small-text\" }, \"Если вы уже в Telegram, попробуйте закрыть это окно и открыть приложение заново.\", -1))\n    ])\n  ]))\n}", "<template>\n  <div class=\"telegram-error\">\n    <div class=\"error-container\">\n      <div class=\"error-icon\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n          <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n          <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\n        </svg>\n      </div>\n      <h1>Доступ только через Telegram</h1>\n      <p>Это приложение работает только при запуске из Telegram Mini App.</p>\n      <p>Пожалуйста, откройте бота в Telegram и запустите приложение оттуда.</p>\n      <div class=\"telegram-link\">\n        <a :href=\"botLink\" target=\"_blank\" class=\"telegram-button\">\n          Открыть бота в Telegram\n        </a>\n      </div>\n      <p class=\"small-text\">Если вы уже в Telegram, попробуйте закрыть это окно и открыть приложение заново.</p>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from 'vue';\n\nexport default defineComponent({\n  name: 'TelegramError',\n  props: {\n    botUsername: {\n      type: String,\n      default: 'your_bot'\n    }\n  },\n  computed: {\n    botLink(): string {\n      return `https://t.me/${this.botUsername}`;\n    }\n  }\n});\n</script>\n\n<style scoped>\n.telegram-error {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: var(--tg-theme-bg-color, #ffffff);\n  color: var(--tg-theme-text-color, #000000);\n  z-index: 9999;\n}\n\n.error-container {\n  max-width: 90%;\n  padding: 2rem;\n  text-align: center;\n  border-radius: 12px;\n  background-color: var(--tg-theme-secondary-bg-color, #f5f5f5);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.error-icon {\n  margin-bottom: 1rem;\n  color: var(--tg-theme-button-color, #3390ec);\n}\n\nh1 {\n  margin-bottom: 1rem;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\np {\n  margin-bottom: 0.5rem;\n  font-size: 1rem;\n  line-height: 1.5;\n}\n\n.telegram-link {\n  margin-top: 1.5rem;\n}\n\n.telegram-button {\n  display: inline-block;\n  padding: 0.75rem 1.5rem;\n  background-color: var(--tg-theme-button-color, #3390ec);\n  color: var(--tg-theme-button-text-color, #ffffff);\n  text-decoration: none;\n  border-radius: 8px;\n  font-weight: 500;\n  transition: opacity 0.2s;\n}\n\n.telegram-button:hover {\n  opacity: 0.9;\n}\n\n.small-text {\n  margin-top: 1rem;\n  font-size: 0.85rem;\n  opacity: 0.8;\n}\n\n/* Dark theme support */\n:global(.dark) .error-container {\n  background-color: var(--tg-theme-secondary-bg-color, #2c2c2c);\n}\n</style>\n", "import { render } from \"./TelegramError.vue?vue&type=template&id=28a7f8f4&scoped=true&ts=true\"\nimport script from \"./TelegramError.vue?vue&type=script&lang=ts\"\nexport * from \"./TelegramError.vue?vue&type=script&lang=ts\"\n\nimport \"./TelegramError.vue?vue&type=style&index=0&id=28a7f8f4&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-28a7f8f4\"]])\n\nexport default __exports__", "import { render } from \"./App.vue?vue&type=template&id=0cdbb0d8&ts=true\"\nimport script from \"./App.vue?vue&type=script&lang=ts\"\nexport * from \"./App.vue?vue&type=script&lang=ts\"\n\nimport \"./App.vue?vue&type=style&index=0&id=0cdbb0d8&lang=css\"\n\nimport exportComponent from \"../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';\n\nconst routes: Array<RouteRecordRaw> = [\n  {\n    path: '/',\n    redirect: '/catalog'\n  },\n  {\n    path: '/tasks',\n    name: 'Tasks',\n    component: () => import('../views/TasksView.vue')\n  },\n  {\n    path: '/bookmarks',\n    name: 'Bookmarks',\n    component: () => import('../views/BookmarksView.vue')\n  },\n  {\n    path: '/catalog',\n    name: 'Catalog',\n    component: () => import('../views/CatalogView.vue')\n  },\n  {\n    path: '/referrals',\n    name: 'Referrals',\n    component: () => import('../views/ReferralsView.vue')\n  },\n  {\n    path: '/profile',\n    name: 'Profile',\n    component: () => import('../views/ProfileView.vue')\n  },\n  {\n    path: '/movie/:source/:id',\n    name: 'Movie',\n    component: () => import('../views/MovieView.vue')\n  },\n  {\n    path: '/:pathMatch(.*)*',\n    name: 'NotFound',\n    redirect: '/catalog'\n  }\n];\n\nconst router = createRouter({\n  history: createWebHistory('/'),\n  routes\n});\n\nexport default router;\n", "import { createApp } from 'vue';\nimport { createPinia } from 'pinia';\nimport App from './App.vue';\nimport router from './router';\nimport './assets/css/main.css';\n\n// Create app\nconst app = createApp(App);\n\n// Use Pinia for state management\nconst pinia = createPinia();\napp.use(pinia);\n\n// Use router\napp.use(router);\n\n// Mount app\napp.mount('#app');\n", "import axios from 'axios';\n\n// Global declaration для Telegram WebApp\ndeclare global {\n  interface Window {\n    Telegram?: {\n      WebApp?: any;\n    };\n  }\n}\n\n// Создаем экземпляр axios с базовым URL из переменных окружения\nconst api = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || '/api',\n  withCredentials: true, // Включаем передачу куки для кросс-доменных запросов\n  headers: {\n    'Content-Type': 'application/json',\n  }\n});\n\n// Добавляем перехватчик запросов для добавления Telegram initData\napi.interceptors.request.use((config) => {\n  // Добавляем заголовок с данными Telegram WebApp, если они доступны\n  if (window.Telegram && window.Telegram.WebApp) {\n    // Используем non-null assertion operator, так как мы уже проверили наличие WebApp\n    config.headers['X-Tg-Init-Data'] = window.Telegram.WebApp!.initData;\n  }\n  return config;\n}, (error) => {\n  return Promise.reject(error);\n});\n\nexport default api;\n", "import { defineStore } from 'pinia';\nimport api from '@/services/api';\n\ninterface User {\n  chat_id: number;\n  username?: string;\n  firstname?: string;\n  lastname?: string;\n  role: string;\n  task_points: number;\n  registration_date: string;\n}\n\ninterface Referral {\n  chat_id: number;\n  username?: string;\n  firstname?: string;\n  lastname?: string;\n  registration_date: string;\n}\n\ninterface ReferralData {\n  count: number;\n  referrals: Referral[];\n}\n\nexport const useUserStore = defineStore('user', {\n  state: () => ({\n    user: null as User | null,\n    referrals: null as ReferralData | null,\n    loading: false,\n    error: null as string | null\n  }),\n\n  getters: {\n    referralLink: (state) => {\n      if (!state.user) return '';\n      const botUsername = process.env.VUE_APP_TELEGRAM_BOT_USERNAME || 'your_bot';\n      return `https://t.me/${botUsername}?start=${state.user.chat_id}`;\n    }\n  },\n\n  actions: {\n    async initUser() {\n      this.loading = true;\n      this.error = null;\n\n      // Проверяем наличие Telegram WebApp\n      if (!window.Telegram || !window.Telegram.WebApp) {\n        this.error = 'Telegram WebApp is not available';\n        this.loading = false;\n        return;\n      }\n\n      try {\n        const response = await api.get('/users/me');\n\n        if (response.data.success) {\n          this.user = response.data.data;\n        } else {\n          this.error = response.data.message || 'Failed to load user data';\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error initializing user:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async fetchReferrals() {\n      this.loading = true;\n      this.error = null;\n\n      // Проверяем наличие Telegram WebApp\n      if (!window.Telegram || !window.Telegram.WebApp) {\n        this.error = 'Telegram WebApp is not available';\n        this.loading = false;\n        return;\n      }\n\n      try {\n        const response = await api.get('/users/referrals');\n\n        if (response.data.success) {\n          this.referrals = response.data.data;\n        } else {\n          this.error = response.data.message || 'Failed to load referrals';\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error fetching referrals:', error);\n      } finally {\n        this.loading = false;\n      }\n    }\n  }\n});\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"js/\" + chunkId + \".\" + {\"338\":\"5d58af27\",\"378\":\"90a87340\",\"656\":\"ed72dc48\",\"791\":\"7743ef18\",\"812\":\"3dc5e1ff\",\"942\":\"295d5dbe\"}[chunkId] + \".js\";\n};", "// This function allow to reference async chunks\n__webpack_require__.miniCssF = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"css/\" + chunkId + \".\" + {\"338\":\"a1f5395d\",\"378\":\"09ac790c\",\"656\":\"133c0679\",\"791\":\"954bc37d\",\"812\":\"b8349ba1\",\"942\":\"afb13e6a\"}[chunkId] + \".css\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "var inProgress = {};\nvar dataWebpackPrefix = \"cinema-bot-frontend:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"/\";", "if (typeof document === \"undefined\") return;\nvar createStylesheet = (chunkId, fullhref, oldTag, resolve, reject) => {\n\tvar linkTag = document.createElement(\"link\");\n\n\tlinkTag.rel = \"stylesheet\";\n\tlinkTag.type = \"text/css\";\n\tif (__webpack_require__.nc) {\n\t\tlinkTag.nonce = __webpack_require__.nc;\n\t}\n\tvar onLinkComplete = (event) => {\n\t\t// avoid mem leaks.\n\t\tlinkTag.onerror = linkTag.onload = null;\n\t\tif (event.type === 'load') {\n\t\t\tresolve();\n\t\t} else {\n\t\t\tvar errorType = event && event.type;\n\t\t\tvar realHref = event && event.target && event.target.href || fullhref;\n\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + errorType + \": \" + realHref + \")\");\n\t\t\terr.name = \"ChunkLoadError\";\n\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n\t\t\terr.type = errorType;\n\t\t\terr.request = realHref;\n\t\t\tif (linkTag.parentNode) linkTag.parentNode.removeChild(linkTag)\n\t\t\treject(err);\n\t\t}\n\t}\n\tlinkTag.onerror = linkTag.onload = onLinkComplete;\n\tlinkTag.href = fullhref;\n\n\n\tif (oldTag) {\n\t\toldTag.parentNode.insertBefore(linkTag, oldTag.nextSibling);\n\t} else {\n\t\tdocument.head.appendChild(linkTag);\n\t}\n\treturn linkTag;\n};\nvar findStylesheet = (href, fullhref) => {\n\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n\tfor(var i = 0; i < existingLinkTags.length; i++) {\n\t\tvar tag = existingLinkTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return tag;\n\t}\n\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n\tfor(var i = 0; i < existingStyleTags.length; i++) {\n\t\tvar tag = existingStyleTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\");\n\t\tif(dataHref === href || dataHref === fullhref) return tag;\n\t}\n};\nvar loadStylesheet = (chunkId) => {\n\treturn new Promise((resolve, reject) => {\n\t\tvar href = __webpack_require__.miniCssF(chunkId);\n\t\tvar fullhref = __webpack_require__.p + href;\n\t\tif(findStylesheet(href, fullhref)) return resolve();\n\t\tcreateStylesheet(chunkId, fullhref, null, resolve, reject);\n\t});\n}\n// object to store loaded CSS chunks\nvar installedCssChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.miniCss = (chunkId, promises) => {\n\tvar cssChunks = {\"338\":1,\"378\":1,\"656\":1,\"791\":1,\"812\":1,\"942\":1};\n\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n\t\tpromises.push(installedCssChunks[chunkId] = loadStylesheet(chunkId).then(() => {\n\t\t\tinstalledCssChunks[chunkId] = 0;\n\t\t}, (e) => {\n\t\t\tdelete installedCssChunks[chunkId];\n\t\t\tthrow e;\n\t\t}));\n\t}\n};\n\n// no hmr\n\n// no prefetching\n\n// no preloaded", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkcinema_bot_frontend\"] = self[\"webpackChunkcinema_bot_frontend\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], () => (__webpack_require__(252)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["_hoisted_1", "class", "_hoisted_2", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_router_view", "_resolveComponent", "_component_BottomMenu", "_component_TelegramError", "isTelegramWebApp", "_openBlock", "_createElementBlock", "key", "_normalizeClass", "colorScheme", "_createElementVNode", "_createVNode", "_createBlock", "botUsername", "_component_router_link", "to", "default", "_withCtx", "xmlns", "viewBox", "fill", "stroke", "d", "points", "_", "x", "y", "width", "height", "rx", "ry", "x1", "y1", "x2", "y2", "cx", "cy", "r", "defineComponent", "name", "__exports__", "_hoisted_3", "_hoisted_4", "_createStaticVNode", "href", "botLink", "target", "props", "type", "String", "computed", "this", "components", "BottomMenu", "TelegramError", "setup", "router", "useRouter", "window", "Telegram", "WebApp", "initData", "close", "expand", "process", "ref", "userStore", "useUserStore", "parseStartParam", "params", "URLSearchParams", "get", "error", "console", "handleStartParam", "startParam", "movieMatch", "match", "source", "id", "log", "push", "onMounted", "value", "tg", "enableClosingConfirmation", "document", "documentElement", "className", "setTimeout", "initUser", "onEvent", "routes", "path", "redirect", "component", "createRouter", "history", "createWebHistory", "app", "createApp", "App", "pinia", "createPinia", "use", "mount", "api", "axios", "create", "baseURL", "withCredentials", "headers", "interceptors", "request", "config", "Promise", "reject", "defineStore", "state", "user", "referrals", "loading", "getters", "referralLink", "chat_id", "actions", "response", "data", "success", "message", "fetchReferrals", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "length", "fulfilled", "j", "Object", "keys", "every", "splice", "definition", "o", "defineProperty", "enumerable", "f", "e", "chunkId", "all", "reduce", "promises", "u", "miniCssF", "g", "globalThis", "Function", "obj", "prop", "prototype", "hasOwnProperty", "call", "inProgress", "dataWebpackPrefix", "l", "url", "done", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "getElementsByTagName", "s", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "onScriptComplete", "prev", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "bind", "head", "append<PERSON><PERSON><PERSON>", "Symbol", "toStringTag", "p", "createStylesheet", "fullhref", "oldTag", "resolve", "linkTag", "rel", "nonce", "onLinkComplete", "errorType", "realHref", "err", "Error", "code", "insertBefore", "nextS<PERSON>ling", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "existingLinkTags", "tag", "dataHref", "existingStyleTags", "loadStylesheet", "installedCssChunks", "miniCss", "cssChunks", "then", "installedChunks", "installedChunkData", "promise", "loadingEnded", "realSrc", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "__webpack_exports__"], "sourceRoot": ""}