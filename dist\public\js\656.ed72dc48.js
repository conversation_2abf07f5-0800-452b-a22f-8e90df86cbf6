"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[656],{656:(e,n,o)=>{o.r(n),o.d(n,{default:()=>d});var t=o(768);const a={class:"profile-view"};function s(e,n,o,s,i,l){return(0,t.uX)(),(0,t.CE)("div",a,n[0]||(n[0]=[(0,t.Fv)('<div class="container" data-v-5fe95708><h1 class="page-title" data-v-5fe95708>Личный кабинет</h1><div class="under-construction" data-v-5fe95708><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="construction-icon" data-v-5fe95708><polygon points="12 2 2 7 12 12 22 7 12 2" data-v-5fe95708></polygon><polyline points="2 17 12 22 22 17" data-v-5fe95708></polyline><polyline points="2 12 12 17 22 12" data-v-5fe95708></polyline></svg><h2 data-v-5fe95708>В разработке</h2><p data-v-5fe95708>Эта функция будет доступна в ближайшее время</p></div></div>',1)]))}const i=(0,t.pM)({name:"ProfileView"});var l=o(241);const r=(0,l.A)(i,[["render",s],["__scopeId","data-v-5fe95708"]]),d=r}}]);
//# sourceMappingURL=656.ed72dc48.js.map