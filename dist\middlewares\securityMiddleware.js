"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ipRestriction = exports.secureHeaders = exports.mongoSanitizeMiddleware = exports.apiLimiter = exports.helmetMiddleware = void 0;
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const express_mongo_sanitize_1 = __importDefault(require("express-mongo-sanitize"));
// Helmet middleware for security headers
exports.helmetMiddleware = (0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'", 'telegram.org', 't.me'],
            connectSrc: ["'self'", 'vibix.org', 'api.telegram.org'],
            frameSrc: ["'self'", 'vibix.org', 'videocdn.tv'],
            imgSrc: ["'self'", 'data:', 'telegram.org', 't.me', 'vibix.org', '*'],
            styleSrc: ["'self'", "'unsafe-inline'", 'fonts.googleapis.com'],
            fontSrc: ["'self'", 'fonts.gstatic.com'],
        },
    },
    crossOriginEmbedderPolicy: false,
    crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
    crossOriginResourcePolicy: { policy: 'cross-origin' },
});
// Rate limiting middleware
exports.apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    standardHeaders: true,
    legacyHeaders: false,
    message: 'Too many requests from this IP, please try again after 15 minutes',
    skip: (_req) => {
        // Skip rate limiting for local development
        return process.env.NODE_ENV === 'development';
    },
});
// Sanitize data to prevent NoSQL injection
exports.mongoSanitizeMiddleware = (0, express_mongo_sanitize_1.default)();
// Примечание: пустые middleware (xssMiddleware и hppMiddleware) были удалены
// XSS защита реализована на уровне Vue.js и при выводе данных
// HPP защита реализована на уровне бизнес-логики при необходимости
// Custom middleware to set additional secure headers not covered by Helmet
const secureHeaders = (req, res, next) => {
    // Примечание: Заголовок Strict-Transport-Security уже устанавливается Helmet
    // Примечание: Заголовок X-Content-Type-Options уже устанавливается Helmet
    // Специальная настройка для Telegram Web App:
    // Разрешаем загрузку в iframe с доменов Telegram вместо X-Frame-Options,
    // который блокирует загрузку в Telegram Web App
    res.setHeader('Content-Security-Policy', "frame-ancestors 'self' https://web.telegram.org https://*.telegram.org https://telegram.org");
    // Отключаем кэширование только для API-ответов
    if (req.path.startsWith('/api')) {
        res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.setHeader('Surrogate-Control', 'no-store');
    }
    next();
};
exports.secureHeaders = secureHeaders;
// IP restriction middleware - оптимизированная версия
const ipRestriction = (allowedIps) => {
    // Если в списке разрешенных IP есть "*", то доступ открыт для всех
    const allowAll = allowedIps.includes('*');
    return (req, res, next) => {
        // Пропускаем проверку в режиме разработки или если доступ открыт для всех
        if (process.env.NODE_ENV === 'development' || allowAll) {
            return next();
        }
        // Получаем IP-адрес клиента
        const clientIp = req.ip || req.socket.remoteAddress || '';
        // Проверяем, есть ли IP в списке разрешенных (включая localhost)
        if (clientIp === '127.0.0.1' || clientIp === '::1' || allowedIps.includes(clientIp)) {
            return next();
        }
        // Логируем попытку доступа с неразрешенного IP
        console.warn(`Access denied for IP: ${clientIp}`);
        // Если IP не разрешен, возвращаем 403 Forbidden
        return res.status(403).json({
            success: false,
            message: 'Access denied'
        });
    };
};
exports.ipRestriction = ipRestriction;
