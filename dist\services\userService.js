"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userService = void 0;
const User_1 = __importDefault(require("../models/User"));
/**
 * User Service - сервис для работы с пользователями
 * Обеспечивает консистентность создания и обновления пользователей
 * между различными частями приложения (authMiddleware, bot.ts)
 */
class UserService {
    /**
     * Создает нового пользователя с согласованными параметрами
     */
    async createUser(params) {
        const user = new User_1.default({
            chat_id: params.chat_id,
            username: params.username,
            firstname: params.firstname,
            lastname: params.lastname,
            active: true,
            banned: false,
            last_activity: new Date(),
            role: params.role || 'user',
            referrer: params.referrer,
            task_points: 0
        });
        await user.save();
        return user;
    }
    /**
     * Обновляет данные пользователя при необходимости
     */
    async updateUserIfNeeded(user, newData) {
        const needsUpdate = user.username !== newData.username ||
            user.firstname !== newData.firstname ||
            user.lastname !== newData.lastname;
        if (needsUpdate) {
            user.username = newData.username;
            user.firstname = newData.firstname;
            user.lastname = newData.lastname;
            user.last_activity = new Date();
            await user.save();
        }
        else {
            // Обновляем только last_activity
            await User_1.default.updateOne({ chat_id: user.chat_id }, { last_activity: new Date() });
        }
    }
}
// Экспортируем единственный экземпляр сервиса
exports.userService = new UserService();
