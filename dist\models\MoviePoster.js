"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const MoviePosterSchema = new mongoose_1.Schema({
    vibix_id: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    imdb_id: {
        type: String,
        sparse: true,
        index: true
    },
    kp_id: {
        type: String,
        sparse: true,
        index: true
    },
    tmdb_id: {
        type: Number,
        sparse: true,
        index: true
    },
    tmdb_poster_path: {
        type: String,
        sparse: true
    },
    poster_urls: {
        w500: {
            type: String,
            default: ''
        },
        original: {
            type: String,
            default: ''
        }
    },
    cache_status: {
        type: String,
        enum: ['pending', 'found', 'not_found', 'error'],
        default: 'pending',
        index: true
    },
    expires_at: {
        type: Date,
        default: () => new Date(Date.now() + 60 * 24 * 60 * 60 * 1000)
    },
    retry_count: {
        type: Number,
        default: 0,
        min: 0,
        max: 3
    },
    error_message: {
        type: String,
        sparse: true
    }
}, {
    timestamps: true
});
// Оптимизированные индексы
MoviePosterSchema.index({ cache_status: 1, expires_at: 1 });
MoviePosterSchema.index({ cache_status: 1, retry_count: 1 });
MoviePosterSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 });
exports.default = mongoose_1.default.model('MoviePoster', MoviePosterSchema);
