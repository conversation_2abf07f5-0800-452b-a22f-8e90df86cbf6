{"version": 3, "file": "js/880.500c5490.js", "mappings": "kLAEA,MAAMA,EAAa,CCDZC,MAAM,gBDEPC,EAAa,CCDVD,MAAM,aDETE,EAAa,CCCRF,MAAM,cDCX,SAAUG,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OAAQC,EAAAA,EAAAA,OCNRC,EAAAA,EAAAA,IAsBM,MAtBNZ,EAsBM,EArBJa,EAAAA,EAAAA,IAoBM,MApBNX,EAoBM,CDbJI,EAAO,KAAOA,EAAO,ICNrBO,EAAAA,EAAAA,IAA0C,MAAtCZ,MAAM,cAAa,kBAAc,KAErCY,EAAAA,EAAAA,IAMM,MANNV,EAMM,CDAJG,EAAO,KAAOA,EAAO,ICLrBO,EAAAA,EAAAA,IAAoB,UAAhB,eAAW,KACfA,EAAAA,EAAAA,IAAgD,UDM9CP,EAAO,KAAOA,EAAO,ICNpBO,EAAAA,EAAAA,IAA0B,cAAlB,aAAS,KAP5BC,EAAAA,EAAAA,IAOqC,KAACC,EAAAA,EAAAA,IAAGV,EAAAW,UAAQ,MACzCH,EAAAA,EAAAA,IAA8C,UDS5CP,EAAO,KAAOA,EAAO,ICTpBO,EAAAA,EAAAA,IAAyB,cAAjB,YAAQ,KAR3BC,EAAAA,EAAAA,IAQoC,KAACC,EAAAA,EAAAA,IAAGV,EAAAY,SAAO,MACvCJ,EAAAA,EAAAA,IAA4D,UDY1DP,EAAO,KAAOA,EAAO,ICZpBO,EAAAA,EAAAA,IAAgC,cAAxB,mBAAe,KATlCC,EAAAA,EAAAA,IAS2C,KAACC,EAAAA,EAAAA,IAAGV,EAAAa,gBAAc,MACrDL,EAAAA,EAAAA,IAAwE,UDetEP,EAAO,KAAOA,EAAO,ICfpBO,EAAAA,EAAAA,IAAsC,cAA9B,yBAAqB,KAVxCC,EAAAA,EAAAA,IAUiD,KAACC,EAAAA,EAAAA,IAAGV,EAAAc,sBAAoB,ODmBnEb,EAAO,KAAOA,EAAO,IC7B3Bc,EAAAA,EAAAA,IAAA,yjBDgCA,C,aCHA,SAAeC,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,cACNC,KAAAA,GACE,MAAMP,GAAWQ,EAAAA,EAAAA,IAAI,IACfP,GAAUO,EAAAA,EAAAA,IAAI,IACdN,GAAiBM,EAAAA,EAAAA,IAAI,GACrBL,GAAuBK,EAAAA,EAAAA,IAAI,GAajC,OAXAC,EAAAA,EAAAA,KAAU,KACJC,OAAOC,UAAUC,QACnBZ,EAASa,MAAQH,OAAOC,SAASC,OAAOZ,UAAY,UACpDC,EAAQY,MAAQH,OAAOC,SAASC,OAAOX,SAAW,UAClDC,EAAeW,MAAQH,OAAOC,SAASC,OAAOV,gBAAkB,EAChEC,EAAqBU,MAAQH,OAAOC,SAASC,OAAOT,sBAAwB,GAE5EH,EAASa,MAAQ,sBACnB,IAGK,CACLb,WACAC,UACAC,iBACAC,uBAEJ,I,aC/CF,MAAMW,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS1B,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://cinema-bot-frontend/./src/views/ProfileView.vue?0ad0", "webpack://cinema-bot-frontend/./src/views/ProfileView.vue", "webpack://cinema-bot-frontend/./src/views/ProfileView.vue?3e49"], "sourcesContent": ["import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"profile-view\" }\nconst _hoisted_2 = { class: \"container\" }\nconst _hoisted_3 = { class: \"debug-info\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[5] || (_cache[5] = _createElementVNode(\"h1\", { class: \"page-title\" }, \"Личный кабинет\", -1)),\n      _createElementVNode(\"div\", _hoisted_3, [\n        _cache[4] || (_cache[4] = _createElementVNode(\"h3\", null, \"Debug Info:\", -1)),\n        _createElementVNode(\"p\", null, [\n          _cache[0] || (_cache[0] = _createElementVNode(\"strong\", null, \"Platform:\", -1)),\n          _createTextVNode(\" \" + _toDisplayString(_ctx.platform), 1)\n        ]),\n        _createElementVNode(\"p\", null, [\n          _cache[1] || (_cache[1] = _createElementVNode(\"strong\", null, \"Version:\", -1)),\n          _createTextVNode(\" \" + _toDisplayString(_ctx.version), 1)\n        ]),\n        _createElementVNode(\"p\", null, [\n          _cache[2] || (_cache[2] = _createElementVNode(\"strong\", null, \"ViewportHeight:\", -1)),\n          _createTextVNode(\" \" + _toDisplayString(_ctx.viewportHeight), 1)\n        ]),\n        _createElementVNode(\"p\", null, [\n          _cache[3] || (_cache[3] = _createElementVNode(\"strong\", null, \"ViewportStableHeight:\", -1)),\n          _createTextVNode(\" \" + _toDisplayString(_ctx.viewportStableHeight), 1)\n        ])\n      ]),\n      _cache[6] || (_cache[6] = _createStaticVNode(\"<div class=\\\"under-construction\\\" data-v-2bcddd7a><svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" class=\\\"construction-icon\\\" data-v-2bcddd7a><polygon points=\\\"12 2 2 7 12 12 22 7 12 2\\\" data-v-2bcddd7a></polygon><polyline points=\\\"2 17 12 22 22 17\\\" data-v-2bcddd7a></polyline><polyline points=\\\"2 12 12 17 22 12\\\" data-v-2bcddd7a></polyline></svg><h2 data-v-2bcddd7a>В разработке</h2><p data-v-2bcddd7a>Эта функция будет доступна в ближайшее время</p></div>\", 1))\n    ])\n  ]))\n}", "<template>\n  <div class=\"profile-view\">\n    <div class=\"container\">\n      <h1 class=\"page-title\">Личный кабинет</h1>\n\n      <div class=\"debug-info\">\n        <h3>Debug Info:</h3>\n        <p><strong>Platform:</strong> {{ platform }}</p>\n        <p><strong>Version:</strong> {{ version }}</p>\n        <p><strong>ViewportHeight:</strong> {{ viewportHeight }}</p>\n        <p><strong>ViewportStableHeight:</strong> {{ viewportStableHeight }}</p>\n      </div>\n\n      <div class=\"under-construction\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"construction-icon\">\n          <polygon points=\"12 2 2 7 12 12 22 7 12 2\"></polygon>\n          <polyline points=\"2 17 12 22 22 17\"></polyline>\n          <polyline points=\"2 12 12 17 22 12\"></polyline>\n        </svg>\n        <h2>В разработке</h2>\n        <p>Эта функция будет доступна в ближайшее время</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted } from 'vue';\n\nexport default defineComponent({\n  name: 'ProfileView',\n  setup() {\n    const platform = ref('');\n    const version = ref('');\n    const viewportHeight = ref(0);\n    const viewportStableHeight = ref(0);\n\n    onMounted(() => {\n      if (window.Telegram?.WebApp) {\n        platform.value = window.Telegram.WebApp.platform || 'unknown';\n        version.value = window.Telegram.WebApp.version || 'unknown';\n        viewportHeight.value = window.Telegram.WebApp.viewportHeight || 0;\n        viewportStableHeight.value = window.Telegram.WebApp.viewportStableHeight || 0;\n      } else {\n        platform.value = 'WebApp not available';\n      }\n    });\n\n    return {\n      platform,\n      version,\n      viewportHeight,\n      viewportStableHeight\n    };\n  }\n});\n</script>\n\n<style scoped>\n.profile-view {\n  padding-bottom: 80px;\n}\n\n.page-title {\n  font-size: var(--font-size-xl);\n  font-weight: bold;\n  margin-bottom: var(--spacing-lg);\n  text-align: center;\n}\n\n.debug-info {\n  background-color: var(--tg-theme-secondary-bg-color);\n  border-radius: var(--border-radius);\n  padding: var(--spacing-md);\n  margin-bottom: var(--spacing-lg);\n  font-size: 14px;\n}\n\n.debug-info h3 {\n  margin: 0 0 var(--spacing-sm) 0;\n  font-size: 16px;\n}\n\n.debug-info p {\n  margin: 4px 0;\n}\n\n.under-construction {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-xl) 0;\n  text-align: center;\n}\n\n.construction-icon {\n  width: 80px;\n  height: 80px;\n  color: var(--tg-theme-button-color);\n  margin-bottom: var(--spacing-md);\n}\n\n.under-construction h2 {\n  font-size: var(--font-size-lg);\n  margin-bottom: var(--spacing-sm);\n}\n\n.under-construction p {\n  color: var(--tg-theme-hint-color);\n}\n</style>\n", "import { render } from \"./ProfileView.vue?vue&type=template&id=2bcddd7a&scoped=true&ts=true\"\nimport script from \"./ProfileView.vue?vue&type=script&lang=ts\"\nexport * from \"./ProfileView.vue?vue&type=script&lang=ts\"\n\nimport \"./ProfileView.vue?vue&type=style&index=0&id=2bcddd7a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-2bcddd7a\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_openBlock", "_createElementBlock", "_createElementVNode", "_createTextVNode", "_toDisplayString", "platform", "version", "viewportHeight", "viewportStableHeight", "_createStaticVNode", "defineComponent", "name", "setup", "ref", "onMounted", "window", "Telegram", "WebApp", "value", "__exports__"], "sourceRoot": ""}