<template>
  <div class="tasks-view">
    <div class="container">
      <h1 class="page-title">Задания</h1>
      <p class="page-description">Выполняйте задания и получайте <a href="#" class="cefi-link" @click.prevent>$CEFIcoin</a></p>

      <!-- Блок с балансом пользователя -->
      <div v-if="user" class="user-balance">
        <div class="balance-content">
          <span class="balance-label">Ваши $CEFIcoin:</span>
          <span class="balance-value">{{ user.task_points }}</span>
        </div>
      </div>

      <div v-if="loading" class="loading-container">
        <div class="loading-spinner large"></div>
        <p>Загрузка заданий...</p>
      </div>

      <div v-else-if="error" class="error-container">
        <p>{{ error }}</p>
        <button @click="fetchTasks" class="retry-button">Повторить</button>
      </div>

      <div v-else-if="tasks.length === 0" class="empty-container">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="empty-icon">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
        <p>Нет доступных заданий</p>
      </div>

      <div v-else class="tasks-list">
        <TaskCard v-for="task in tasks" :key="task._id" :task="task" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed } from 'vue';
import TaskCard from '@/components/TaskCard.vue';
import { useTasksStore } from '@/store/tasks';
import { useUserStore } from '@/store/user';

export default defineComponent({
  name: 'TasksView',
  components: {
    TaskCard
  },
  setup() {
    const tasksStore = useTasksStore();
    const userStore = useUserStore();

    // Computed properties
    const tasks = computed(() => tasksStore.tasks);
    const loading = computed(() => tasksStore.loading);
    const error = computed(() => tasksStore.error);
    const user = computed(() => userStore.user);

    // Fetch tasks on mount
    const fetchTasks = async () => {
      await tasksStore.fetchTasks();
    };

    onMounted(() => {
      fetchTasks();
    });

    return {
      tasks,
      loading,
      error,
      user,
      fetchTasks
    };
  }
});
</script>

<style scoped>
.tasks-view {
  padding-bottom: 80px;
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
  text-align: center;
}

.page-description {
  color: var(--tg-theme-hint-color);
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.cefi-link {
  color: var(--tg-theme-link-color, #2481cc);
  text-decoration: none;
  cursor: pointer;
}

.cefi-link:hover {
  text-decoration: underline;
}

.user-balance {
  background-color: var(--tg-theme-secondary-bg-color, rgba(0, 0, 0, 0.05));
  border-radius: 12px;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--tg-theme-section-separator-color, rgba(0, 0, 0, 0.1));
}

.balance-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-label {
  font-size: 16px;
  color: var(--tg-theme-text-color);
  font-weight: 500;
}

.balance-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--tg-theme-button-color);
}

.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;
  color: var(--tg-theme-hint-color);
}

.loading-spinner {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--tg-theme-button-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: var(--spacing-md);
}

.loading-spinner.large {
  width: 48px;
  height: 48px;
  border-width: 3px;
}

.empty-icon {
  width: 48px;
  height: 48px;
  color: var(--tg-theme-hint-color);
  margin-bottom: var(--spacing-md);
}

.retry-button {
  margin-top: var(--spacing-md);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
