"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const taskController_1 = require("../controllers/taskController");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const router = express_1.default.Router();
// Apply auth middleware to all routes
router.use(authMiddleware_1.authMiddleware);
// Get active tasks for user
router.get('/', taskController_1.getTasks);
// Complete a task
router.post('/complete', taskController_1.completeTask);
// Get completed tasks
router.get('/completed', taskController_1.getCompletedTasks);
exports.default = router;
