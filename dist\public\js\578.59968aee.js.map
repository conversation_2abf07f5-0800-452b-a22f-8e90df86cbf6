{"version": 3, "file": "js/578.59968aee.js", "mappings": "8HASM,SAAUA,EAAeC,GAC7B,OAAKA,EAEDA,EAAMC,MAAc,KACpBD,EAAME,QAAgB,OAEnB,KALY,IAMrB,CAOM,SAAUC,EAAWH,GACzB,IAAKA,EAAO,OAAO,KAEnB,MAAMI,EAASL,EAAeC,GAE9B,MAAe,OAAXI,EAAwBJ,EAAMC,OAAS,KAC5B,SAAXG,GAA0BJ,EAAME,SAE7B,IACT,CAOM,SAAUG,EAAgBC,GAC9B,MAAMC,EAAgC,CACpCP,MAAO,QACPQ,GAAI,SACJC,MAAO,QACP,YAAa,SACb,eAAgB,gBAGlB,OAAOF,EAAMD,IAASA,CACxB,CAOM,SAAUI,EAAkBV,GAChC,OAAKA,EAGDA,EAAMW,aAAaC,KACdZ,EAAMW,YAAYC,KAGpBZ,EAAMa,YAAc,KAPR,IAQrB,C,wGC/DA,MAAMC,EAAa,CCDZC,MAAM,gBDEPC,EAAa,CCDVD,MAAM,aDETE,EAAa,CCWRC,IAAI,kBAAkBH,MAAM,qBDNjC,SAAUI,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAA4BC,EAAAA,EAAAA,IAAkB,kBAC9CC,GAAsBD,EAAAA,EAAAA,IAAkB,YACxCE,GAAuBF,EAAAA,EAAAA,IAAkB,aAE/C,OAAQG,EAAAA,EAAAA,OCbRC,EAAAA,EAAAA,IA6BM,MA7BNjB,EA6BM,EA5BJkB,EAAAA,EAAAA,IAwBM,MAxBNhB,EAwBM,EAtBJiB,EAAAA,EAAAA,IAAkBP,KDajBI,EAAAA,EAAAA,KAAW,ICVZC,EAAAA,EAAAA,IAKMG,EAAAA,GAAA,MAZZC,EAAAA,EAAAA,IAO8Bf,EAAAgB,mBAAZC,KDWFP,EAAAA,EAAAA,OCXVC,EAAAA,EAAAA,IAKM,OALsCO,IAAKD,EAASE,IDarD,ECZHN,EAAAA,EAAAA,IAGEL,EAAA,CAFCY,MAAOH,EAASG,MAChBH,SAAUA,EAASA,UDcjB,KAAM,EAAG,CAAC,QAAS,kBAEtB,OCXJL,EAAAA,EAAAA,IAA2D,MAA3Df,EAA2D,UAG3CG,EAAAqB,2BDWXX,EAAAA,EAAAA,KAAW,ICVdC,EAAAA,EAAAA,IAKMG,EAAAA,GAAA,CAxBdI,IAAA,IAAAH,EAAAA,EAAAA,IAmBgCf,EAAAsB,sBAAZL,KDWAP,EAAAA,EAAAA,OCXZC,EAAAA,EAAAA,IAKM,OALyCO,IAAKD,EAASE,IDatD,ECZLN,EAAAA,EAAAA,IAGEL,EAAA,CAFCY,MAAOH,EAASG,MAChBH,SAAUA,EAASA,UDcf,KAAM,EAAG,CAAC,QAAS,kBAEtB,OCtCdM,EAAAA,EAAAA,IAAA,UA6BIV,EAAAA,EAAAA,IAAaJ,IDcjB,C,+BEzCA,MAAMf,ECFN,aDGME,EAAa,CCHnBsB,IAAA,EAI4BvB,MAAM,wBDG5BE,EAAa,CCPnBqB,IAAA,EAM4CM,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDWvLC,EAAa,CCjBnBV,IAAA,EAWoBM,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDiB/J,SAAU5B,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMwB,GAA0BtB,EAAAA,EAAAA,IAAkB,gBAElD,OAAQG,EAAAA,EAAAA,OC9BRC,EAAAA,EAAAA,IAqCM,aApCJC,EAAAA,EAAAA,IAwBM,OAxBDjB,OAFTmC,EAAAA,EAAAA,IAAA,CAEe,mBAAkB,CAAAC,SAAqB/B,EAAAgC,cAAeC,QAAKhC,EAAA,KAAAA,EAAA,IAF1EiC,EAAAA,EAAAA,KAEoE,QAAW,YDiCxE,EChCDtB,EAAAA,EAAAA,IAYS,UAZDjB,MAAM,gBAAiBsC,QAAKhC,EAAA,KAAAA,EAAA,IAH1CiC,EAAAA,EAAAA,KDwCA,IAAIC,ICrC6CnC,EAAAoC,uBAAApC,EAAAoC,yBAAAD,IAAqB,WAAGE,SAAUrC,EAAAsC,SDuC1E,CCtCUtC,EAAAsC,UDwCN5B,EAAAA,EAAAA,OCxCLC,EAAAA,EAAAA,IAAuD,MAAvDf,IAEgBI,EAAAuC,YAAYC,SDwCrB9B,EAAAA,EAAAA,OCxCPC,EAAAA,EAAAA,IAGM,MAHNd,EAGMI,EAAA,KAAAA,EAAA,KAFJW,EAAAA,EAAAA,IAA4C,QAAtC6B,GAAG,IAAIC,GAAG,KAAKC,GAAG,KAAKC,GAAG,MD6CvB,MAAO,IC5ChBhC,EAAAA,EAAAA,IAA+C,YAArCiC,OAAO,oBAAkB,eD+C9BnC,EAAAA,EAAAA,OC5CPC,EAAAA,EAAAA,IAGM,MAHNiB,EAGM3B,EAAA,KAAAA,EAAA,KAFJW,EAAAA,EAAAA,IAAuC,UAA/BkC,GAAG,KAAKC,GAAG,KAAKC,EAAE,KDgDjB,MAAO,IC/ChBpC,EAAAA,EAAAA,IAAmD,QAA7C6B,GAAG,KAAKC,GAAG,KAAKC,GAAG,QAAQC,GAAG,SDqD3B,MAAO,QAEjB,ECpETlD,IAiBMkB,EAAAA,EAAAA,IAQM,OARDjB,MAAM,cAAesC,QAAKhC,EAAA,KAAAA,EAAA,IAjBrCiC,EAAAA,EAAAA,KAiB+B,QAAW,YDuDjC,EACDe,EAAAA,EAAAA,KCvDArC,EAAAA,EAAAA,IAME,SALAd,IAAI,cACJZ,KAAK,OApBf,sBAAAe,EAAA,KAAAA,EAAA,GAAAiD,GAqBmBlD,EAAAuC,YAAWW,GACpBC,YAAY,+BACXC,QAAKnD,EAAA,KAAAA,EAAA,IAvBhBoD,EAAAA,EAAAA,KDgFA,IAAIlB,ICzDoBnC,EAAAsD,QAAAtD,EAAAsD,UAAAnB,IAAM,aD0DnB,KAAM,KAAM,CACb,CAACoB,EAAAA,GC7DQvD,EAAAuC,kBDgEZ,GCvDKvC,EAAAwD,YAAYC,OAASzD,EAAAgC,aDyDxBtB,EAAAA,EAAAA,OC1DLgD,EAAAA,EAAAA,IAQE7B,EAAA,CArCNX,IAAA,EA+BOyC,MAAM,EACNC,QAAS5D,EAAAwD,YAAYC,MACrBI,QAAQ,EACR,cAAY,EACZC,QAAO9D,EAAA+D,WACRpE,MAAM,gBD2DD,KAAM,EAAG,CAAC,UAAW,cC/FhC4B,EAAAA,EAAAA,IAAA,QDkGA,C,sBEhGA,MAAM7B,EAAa,CCAVC,MAAM,iBDCTC,EAAa,CCHnBsB,IAAA,EAGWvB,MAAM,cDIXE,EAAa,CCIRF,MAAM,cDHXiC,EAAa,CCRnBV,IAAA,EAYoCvB,MAAM,eDApCqE,EAAa,CCCRrE,MAAM,qBDAXsE,ECbN,aDcMC,EAAa,CCdnBhD,IAAA,EAsB6BvB,MAAM,mBDJ7BwE,EAAa,CClBnBjD,IAAA,GDoBM,SAAUnB,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OCpBWL,EAAA2D,ODqBNjD,EAAAA,EAAAA,OCrBLC,EAAAA,EAAAA,IAqCM,OAtCRO,IAAA,EACmBvB,OADnBmC,EAAAA,EAAAA,IAAA,CACyB,gBAAe,yBAAoC9B,EAAA6D,WDwBnE,ECvBLjD,EAAAA,EAAAA,IAmCM,MAnCNlB,EAmCM,CAlC2BM,EAAA6D,QAHrCtC,EAAAA,EAAAA,IAAA,SD4Beb,EAAAA,EAAAA,OCzBTC,EAAAA,EAAAA,IAMM,MANNf,EAMMK,EAAA,KAAAA,EAAA,KALJW,EAAAA,EAAAA,IAIM,OAJDY,MAAM,6BAA6B4C,MAAM,KAAKC,OAAO,KAAK5C,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDmClK,EClCTf,EAAAA,EAAAA,IAAwC,UAAhCkC,GAAG,KAAKC,GAAG,KAAKC,EAAE,QAC1BpC,EAAAA,EAAAA,IAA4C,QAAtC6B,GAAG,KAAKC,GAAG,IAAIC,GAAG,KAAKC,GAAG,QAChChC,EAAAA,EAAAA,IAAgD,QAA1C6B,GAAG,KAAKC,GAAG,KAAKC,GAAG,QAAQC,GAAG,SDkD1B,QC9CdhC,EAAAA,EAAAA,IAGM,MAHNf,EAGM,CAFMG,EAAAoB,QAAUpB,EAAA6D,SDkDXnD,EAAAA,EAAAA,OClDTC,EAAAA,EAAAA,IAAgE,KAAhEiB,GAAgE0C,EAAAA,EAAAA,IAAbtE,EAAAoB,OAAK,KAZhEG,EAAAA,EAAAA,IAAA,QAaQX,EAAAA,EAAAA,IAA8C,IAA9CoD,GAA8CM,EAAAA,EAAAA,IAAdtE,EAAA4D,SAAO,KAIjC5D,EAAAuE,YDkDC7D,EAAAA,EAAAA,OCnDTC,EAAAA,EAAAA,IAQS,UAxBfO,IAAA,EAkBSe,QAAKhC,EAAA,KAAAA,EAAA,GAAAiD,GAAElD,EAAAwE,MAAM,UACd7E,MAAM,eACL0C,SAAUrC,EAAAsC,SDoDF,CClDGtC,EAAAsC,UDoDC5B,EAAAA,EAAAA,OCpDbC,EAAAA,EAAAA,IAAoD,OAApDuD,MDqDaxD,EAAAA,EAAAA,OCpDbC,EAAAA,EAAAA,IAAmC,OAvB3CwD,GAAAG,EAAAA,EAAAA,IAuBwBtE,EAAAyE,WAAS,KDqDhB,EC5EjBR,KAAA1C,EAAAA,EAAAA,IAAA,OA2BcvB,EAAA0E,YDoDChE,EAAAA,EAAAA,OCrDTC,EAAAA,EAAAA,IAUS,UApCfO,IAAA,EA4BSe,QAAKhC,EAAA,KAAAA,EAAA,GAAAiD,GAAElD,EAAAwE,MAAM,UACd7E,MAAM,eACN,aAAW,WDsDFM,EAAO,KAAOA,EAAO,GAAK,ECpDnCW,EAAAA,EAAAA,IAGM,OAHDY,MAAM,6BAA6B4C,MAAM,KAAKC,OAAO,KAAK5C,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SD+DlK,EC9DTf,EAAAA,EAAAA,IAA2C,QAArC6B,GAAG,KAAKC,GAAG,IAAIC,GAAG,IAAIC,GAAG,QAC/BhC,EAAAA,EAAAA,IAA2C,QAArC6B,GAAG,IAAIC,GAAG,IAAIC,GAAG,KAAKC,GAAG,SD0ErB,QC5GpBrB,EAAAA,EAAAA,IAAA,UDgHS,KChHTA,EAAAA,EAAAA,IAAA,MDkHA,CCtEA,SAAeoD,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,eACNC,MAAO,CACLlB,KAAM,CACJzE,KAAM4F,QACNC,SAAS,GAEXnB,QAAS,CACP1E,KAAM8F,OACNC,UAAU,GAEZ7D,MAAO,CACLlC,KAAM8F,OACND,QAAS,UAEXlB,OAAQ,CACN3E,KAAM4F,QACNC,SAAS,GAEXR,UAAW,CACTrF,KAAM4F,QACNC,SAAS,GAEXL,UAAW,CACTxF,KAAM4F,QACNC,SAAS,GAEXN,UAAW,CACTvF,KAAM8F,OACND,QAAS,aAEXzC,QAAS,CACPpD,KAAM4F,QACNC,SAAS,IAGbG,MAAO,CAAC,QAAS,W,aCzEnB,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,IHsCA,GAAeR,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,YACNQ,WAAY,CACVC,aAAYA,GAEdC,KAAAA,GACE,MAAMC,GAASC,EAAAA,EAAAA,MACThC,GAAciC,EAAAA,EAAAA,KACdlD,GAAczC,EAAAA,EAAAA,IAAI,IAClBkC,GAAalC,EAAAA,EAAAA,KAAI,GACjBwC,GAAUxC,EAAAA,EAAAA,KAAI,GACd4F,GAAc5F,EAAAA,EAAAA,IAA6B,MAG3CiE,EAAaA,KACjBP,EAAYC,MAAQ,IAAI,EAIpBkC,EAAeA,KACnB3D,EAAW4D,OAAS5D,EAAW4D,MAC3B5D,EAAW4D,OAASF,EAAYE,OAElCC,YAAW,KACTH,EAAYE,OAAOE,OAAO,GACzB,IACL,EAII1D,EAAwBA,KACxBG,EAAYqD,MAAMpD,OAEpBc,IAGAqC,GACF,EAIIrC,EAASyC,UACb,GAAKxD,EAAYqD,MAAMpD,SAAUF,EAAQsD,MAAzC,CAEAtD,EAAQsD,OAAQ,EAEhB7B,IAEA,IACE,MAAMnF,QAAc4E,EAAYwC,YAAYzD,EAAYqD,OAExD,GAAIhH,EAAO,CAET,IAAII,EAAS,KACTmC,EAAKvC,EAAMC,OAEVsC,GAAMvC,EAAME,UACfE,EAAS,OACTmC,EAAKvC,EAAME,SAGTE,GAAUmC,IAEZoE,EAAOU,KAAK,UAAUjH,KAAUmC,KAGhCoB,EAAYqD,MAAQ,GACpB5D,EAAW4D,OAAQ,EACnB7B,IAEJ,CACF,CAAE,MAAON,GACPyC,QAAQzC,MAAM,yBAA0BA,EAC1C,CAAE,QACAnB,EAAQsD,OAAQ,CAClB,CAjCsD,CAiCtD,EAIIO,EAAqBA,KACrBnE,EAAW4D,QAEb5D,EAAW4D,OAAQ,EACnBrD,EAAYqD,MAAQ,GACpB7B,IACF,EAYF,OARAqC,EAAAA,EAAAA,KAAU,KACRC,SAASC,iBAAiB,QAASH,EAAmB,KAGxDI,EAAAA,EAAAA,KAAgB,KACdF,SAASG,oBAAoB,QAASL,EAAmB,IAGpD,CACL5D,cACAP,aACAM,UACAoD,cACAlC,cACAmC,eACAvD,wBACAkB,SACAS,aAEJ,IIpJI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,ICPMrE,EAAa,CCDZC,MAAM,mBDEPC,EAAa,CCDVD,MAAM,qBAAqBG,IAAI,qBDKlCD,ECPN,YDQM+B,EAAa,CCKJjC,MAAM,iBDJfqE,EAAa,CCKHrE,MAAM,eDJhBsE,EAAa,CCKJtE,MAAM,qBDJfuE,EAAa,CCUVvE,MAAM,uBDTTwE,ECZN,YDcM,SAAUpE,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OAAQK,EAAAA,EAAAA,OCdRC,EAAAA,EAAAA,IAyCM,MAzCNjB,EAyCM,EAxCJkB,EAAAA,EAAAA,IAiBM,MAjBNhB,EAiBM,GDFHc,EAAAA,EAAAA,KAAW,ICdZC,EAAAA,EAAAA,IAeMG,EAAAA,GAAA,MAlBZC,EAAAA,EAAAA,IAIiCf,EAAAyG,QAJjC,CAIgBC,EAAOC,MDcPjG,EAAAA,EAAAA,OCfVC,EAAAA,EAAAA,IAeM,OAbHO,IAAKyF,EACNhH,OANRmC,EAAAA,EAAAA,IAAA,CAMc,iBAAgB,CAAA8E,OACJ5G,EAAA6G,eAAiBF,KAClCG,OARTC,EAAAA,EAAAA,IAAA,EAAAC,UAAA,mBAQkDL,EAAQ3G,EAAA6G,mBAElD,qBADC5E,QAAKiB,GAAElD,EAAAiH,iBAAiBP,IDctB,ECXH9F,EAAAA,EAAAA,IAKM,OALDjB,MAAM,gBAAiBmH,OAZpCC,EAAAA,EAAAA,IAAA,CAAAG,gBAAA,OAYqER,EAAMS,eDe9D,ECdHvG,EAAAA,EAAAA,IAGM,MAHNgB,EAGM,EAFJhB,EAAAA,EAAAA,IAA8C,KAA9CoD,GAA8CM,EAAAA,EAAAA,IAAnBoC,EAAMtF,OAAK,IACtCR,EAAAA,EAAAA,IAAwD,IAAxDqD,GAAwDK,EAAAA,EAAAA,IAAxBoC,EAAMU,aAAW,MDiBhD,IACF,GCjCXvH,MDkCU,OACH,MCdHe,EAAAA,EAAAA,IAQM,MARNsD,EAQM,GDQHxD,EAAAA,EAAAA,KAAW,ICfZC,EAAAA,EAAAA,IAMUG,EAAAA,GAAA,MA5BhBC,EAAAA,EAAAA,IAuBiCf,EAAAyG,QAvBjC,CAuBgBC,EAAOC,MDePjG,EAAAA,EAAAA,OChBVC,EAAAA,EAAAA,IAMU,UAJPO,IAAKyF,EACNhH,OAzBRmC,EAAAA,EAAAA,IAAA,CAyBc,gBAAe,CAAA8E,OACH5G,EAAA6G,eAAiBF,KAClC1E,QAAKiB,GAAElD,EAAAqH,UAAUV,IDef,KAAM,GC1CjBxC,MD2CU,SCZNvD,EAAAA,EAAAA,IAIS,UAJDjB,MAAM,wBAAyBsC,QAAKhC,EAAA,KAAAA,EAAA,GDkBhD,IAAIkC,IClB8CnC,EAAAsH,WAAAtH,EAAAsH,aAAAnF,KDmB3ClC,EAAO,KAAOA,EAAO,GAAK,EClB3BW,EAAAA,EAAAA,IAEM,OAFDY,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SD2BnJ,EC1BDf,EAAAA,EAAAA,IAA8C,YAApCiC,OAAO,sBD4Bf,OCxBNjC,EAAAA,EAAAA,IAIS,UAJDjB,MAAM,wBAAyBsC,QAAKhC,EAAA,KAAAA,EAAA,GD8BhD,IAAIkC,IC9B8CnC,EAAAuH,WAAAvH,EAAAuH,aAAApF,KD+B3ClC,EAAO,KAAOA,EAAO,GAAK,EC9B3BW,EAAAA,EAAAA,IAEM,OAFDY,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDuCnJ,ECtCDf,EAAAA,EAAAA,IAA6C,YAAnCiC,OAAO,qBDwCf,OAGV,C,aCvBA,SAAe8B,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,iBACNU,KAAAA,GAEE,SAASkC,EAAkBC,EAAUC,GACnC,MAAMC,EAAc,GACdC,EAAO,IAAIC,IACjB,MAAOF,EAAOG,OAASJ,GAAKE,EAAKG,KAAON,EAAIK,OAAQ,CAClD,MAAME,EAAMC,KAAKC,MAAMD,KAAKE,SAAWV,EAAIK,QACtCF,EAAKQ,IAAIJ,KACZL,EAAO1B,KAAKwB,EAAIO,IAChBJ,EAAKS,IAAIL,GAEb,CACA,OAAOL,CACT,CAEA,MAAMlB,GAAS3G,EAAAA,EAAAA,IAAqB,IAC9B+G,GAAe/G,EAAAA,EAAAA,IAAI,GACnBwI,GAAoBxI,EAAAA,EAAAA,IAAwB,MAC5CyI,GAAmBzI,EAAAA,EAAAA,IAAmB,MACtCwC,GAAUxC,EAAAA,EAAAA,KAAI,GACd2D,GAAQ3D,EAAAA,EAAAA,IAAmB,MAC3B0D,GAAciC,EAAAA,EAAAA,KACdF,GAASC,EAAAA,EAAAA,MAGTgD,EAAqBzC,UACzBzD,EAAQsD,OAAQ,EAChBnC,EAAMmC,MAAQ,KAEd,IAEE,MAAM6C,QAAejF,EAAYkF,oBAAoBC,EAAAA,EAAcC,QAAS,EAAG,IAGzEC,GAAYJ,GAAU,IAAIK,QAAOlK,GAASA,EAAMW,aAAaC,OAG7DuJ,EAAevB,EAAeqB,EAAU,GAGxCG,EAA+BD,EAAaE,KAAIrK,IAAI,CACxDwC,MAAOxC,EAAMsK,UAAYtK,EAAMuK,eAAiB,GAChD/B,YAAaxI,EAAMwK,mBAAqB,GAAkB,WAAfxK,EAAMM,KAAoB,SAAW,WAAWN,EAAMyK,OACjGlC,SAAUvI,EAAMW,aAAaC,MAAQ,GACrC8J,KAAM,UAAU1K,EAAMC,MAAQ,KAAO,UAAUD,EAAMC,OAASD,EAAME,cAIhEyK,EAAyB,CAC7BnI,MAAO,UACPgG,YAAa,kDACbD,SAAU,yHACVqC,MAAM,GAIJR,EAAYlB,QAAU,EACxBkB,EAAYS,OAAO,EAAG,EAAGF,GAEzBP,EAAY/C,KAAKsD,GAGnB9C,EAAOb,MAAQoD,CACjB,CAAE,MAAOU,GACPxD,QAAQzC,MAAM,8CAA+CiG,GAC7DjG,EAAMmC,MAAQ,2CAGda,EAAOb,MAAQ,CACb,CACExE,MAAO,oBACPgG,YAAa,mCACbD,SAAU,0HAEZ,CACE/F,MAAO,UACPgG,YAAa,sBACbD,SAAU,0HAEZ,CACE/F,MAAO,UACPgG,YAAa,kDACbD,SAAU,yHACVqC,MAAM,GAER,CACEpI,MAAO,aACPgG,YAAa,eACbD,SAAU,0HAGhB,CAAE,QACA7E,EAAQsD,OAAQ,CAClB,GAII2B,EAAYA,KAChBV,EAAajB,OAASiB,EAAajB,MAAQ,GAAKa,EAAOb,MAAMkC,OAC7D6B,GAAe,EAGXrC,EAAYA,KAChBT,EAAajB,OAASiB,EAAajB,MAAQ,EAAIa,EAAOb,MAAMkC,QAAUrB,EAAOb,MAAMkC,OACnF6B,GAAe,EAGXtC,EAAaV,IACjBE,EAAajB,MAAQe,EACrBgD,GAAe,EAIXC,EAAgBA,KACpBC,IACAtB,EAAiB3C,MAAQkE,OAAOC,aAAY,KAC1CxC,GAAW,GACV,IAAK,EAGJsC,EAAeA,KACY,OAA3BtB,EAAiB3C,QACnBoE,cAAczB,EAAiB3C,OAC/B2C,EAAiB3C,MAAQ,KAC3B,EAGI+D,EAAgBA,KACpBE,IACAD,GAAe,EAIjB,IAAIK,EAAc,EACdC,EAAY,EAEhB,MAAMC,EAAoBC,IACxBH,EAAcG,EAAEC,eAAe,GAAGC,OAAO,EAGrCC,EAAkBH,IACtBF,EAAYE,EAAEC,eAAe,GAAGC,QAChCE,GAAa,EAGTA,EAAcA,KAClB,MAAMC,EAAiB,GACnBP,EAAYD,EAAcQ,EAC5BlD,IACS2C,EAAYD,EAAcQ,GACnCnD,GACF,EAIIL,EAAoBP,IACpBA,EAAM8C,MACN9C,EAAM4C,MACR/D,EAAOU,KAAKS,EAAM4C,KACpB,EAwBF,OArBAlD,EAAAA,EAAAA,KAAU,KACRoC,IACAoB,IAGItB,EAAkB1C,QACpB0C,EAAkB1C,MAAMU,iBAAiB,aAAc6D,EAAkB,CAAEO,SAAS,IACpFpC,EAAkB1C,MAAMU,iBAAiB,WAAYiE,EAAgB,CAAEG,SAAS,IAClF,KAGFnE,EAAAA,EAAAA,KAAgB,KACdsD,IAGIvB,EAAkB1C,QACpB0C,EAAkB1C,MAAMY,oBAAoB,aAAc2D,GAC1D7B,EAAkB1C,MAAMY,oBAAoB,WAAY+D,GAC1D,IAGK,CACL9D,SACAI,eACAyB,oBACAf,YACAD,YACAD,YACAJ,mBAEJ,ICtPI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,ICPMvH,EAAa,CCDZC,MAAM,YAAYG,IAAI,UDKvBF,EAAa,CCJXD,MAAM,kBDKRE,EAAa,CCHVF,MAAM,oBDITiC,EAAa,CCGRjC,MAAM,gBAAgBG,IAAI,mBDC/BkE,EAAa,CCZnB9C,IAAA,EAY4BvB,MAAM,qBDI5BsE,EAAa,CChBnB/C,IAAA,EAgB+BvB,MAAM,mBDI/BuE,EAAa,CCpBnBhD,IAAA,EAqB6CvB,MAAM,mBDG7CwE,GAAa,CCxBnBjD,IAAA,EAyBoBvB,MAAM,eDGpBgL,GAAa,CC5BnBzJ,IAAA,EAkCmCvB,MAAM,8BDFnCiL,GChCN,aDiCMC,GAAc,CCjCpB3J,IAAA,EAwC0CvB,MAAM,yBDH1CmL,GAAc,CCrCpB5J,IAAA,GDuCM,SAAUnB,GAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAM0K,GAAuBxK,EAAAA,EAAAA,IAAkB,aAE/C,OAAQG,EAAAA,EAAAA,OCzCRC,EAAAA,EAAAA,IAoDM,MApDNjB,EAoDM,EAnDJkB,EAAAA,EAAAA,IAA2C,KAA3ChB,GAA2C0E,EAAAA,EAAAA,IAAbtE,EAAAoB,OAAK,IAEnCR,EAAAA,EAAAA,IAgDM,MAhDNf,EAgDM,CA/CUG,EAAAgL,eAAiBhL,EAAAiL,oBDyC1BvK,EAAAA,EAAAA,OCzCLC,EAAAA,EAAAA,IAIS,UATfO,IAAA,EAKwDvB,MAAM,qBAAsBsC,QAAKhC,EAAA,KAAAA,EAAA,GD8CzF,IAAIkC,IC9CuFnC,EAAAkL,YAAAlL,EAAAkL,cAAA/I,KD+C9ElC,EAAO,KAAOA,EAAO,GAAK,EC9C/BW,EAAAA,EAAAA,IAEM,OAFDY,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDuD/I,ECtDLf,EAAAA,EAAAA,IAA8C,YAApCiC,OAAO,sBDwDX,QC/DhBtB,EAAAA,EAAAA,IAAA,QAWMX,EAAAA,EAAAA,IAkCM,MAlCNgB,EAkCM,CAjCO5B,EAAAsC,UDwDN5B,EAAAA,EAAAA,OCxDLC,EAAAA,EAAAA,IAEM,MAFNqD,EAEM/D,EAAA,KAAAA,EAAA,KADJW,EAAAA,EAAAA,IAAmC,OAA9BjB,MAAM,mBAAiB,aAGdK,EAAAyD,QDwDT/C,EAAAA,EAAAA,OCxDPC,EAAAA,EAAAA,IAGM,MAHNsD,EAGM,EAFJrD,EAAAA,EAAAA,IAAkB,UAAA0D,EAAAA,EAAAA,IAAZtE,EAAAyD,OAAK,IACX7C,EAAAA,EAAAA,IAAmE,UAA1DqB,QAAKhC,EAAA,KAAAA,EAAA,GD2DxB,IAAIkC,IC3DsBnC,EAAAmL,YAAAnL,EAAAmL,cAAAhJ,IAAYxC,MAAM,gBAAe,gBAGjB,IAAlBK,EAAAyI,OAAOX,SD6DdpH,EAAAA,EAAAA,OC7DTC,EAAAA,EAAAA,IAEM,MAFNuD,EAEMjE,EAAA,KAAAA,EAAA,KADJW,EAAAA,EAAAA,IAAwB,SAArB,qBAAiB,SD+DbF,EAAAA,EAAAA,OC5DTC,EAAAA,EAAAA,IAmBM,MAnBNwD,GAmBM,GD0CKzD,EAAAA,EAAAA,KAAW,IC5DpBC,EAAAA,EAAAA,IAKEG,EAAAA,GAAA,MA/BZC,EAAAA,EAAAA,IA2B4Bf,EAAAyI,QAAT7J,KD4DS8B,EAAAA,EAAAA,OC7DlBgD,EAAAA,EAAAA,IAKEqH,EAAA,CAHC7J,IAAKtC,EAAMuC,GACXvC,MAAOA,EACP,eAAcoB,EAAAoL,YAAYxM,ID6DhB,KAAM,EAAG,CAAC,QAAS,oBACpB,MC1DDoB,EAAAqL,eD4DE3K,EAAAA,EAAAA,OC5DbC,EAAAA,EAAAA,IASM,MATNgK,GASM,EARJ/J,EAAAA,EAAAA,IAOS,UANPjB,MAAM,mBACLsC,QAAKhC,EAAA,KAAAA,EAAA,GD8DpB,IAAIkC,IC9DkBnC,EAAAsL,cAAAtL,EAAAsL,gBAAAnJ,IACPE,SAAUrC,EAAAuL,iBD+DE,CC7DFvL,EAAAuL,kBD+DM7K,EAAAA,EAAAA,OC/DjBC,EAAAA,EAAAA,IAAgE,MAAhEkK,ODgEiBnK,EAAAA,EAAAA,OC/DjBC,EAAAA,EAAAA,IAAuB,OAzCrCmK,GAyC2B,SDgEA,ECzG3BF,QAAArJ,EAAAA,EAAAA,IAAA,WD6GS,KC9DWvB,EAAAwL,gBAAkBxL,EAAAiL,oBDgE3BvK,EAAAA,EAAAA,OChELC,EAAAA,EAAAA,IAIS,UAnDfO,IAAA,EA+CyDvB,MAAM,sBAAuBsC,QAAKhC,EAAA,KAAAA,EAAA,GDqE3F,IAAIkC,ICrEyFnC,EAAAyL,aAAAzL,EAAAyL,eAAAtJ,KDsEhFlC,EAAO,KAAOA,EAAO,GAAK,ECrE/BW,EAAAA,EAAAA,IAEM,OAFDY,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SD8E/I,EC7ELf,EAAAA,EAAAA,IAA6C,YAAnCiC,OAAO,qBD+EX,QChIhBtB,EAAAA,EAAAA,IAAA,UDoIK,IACL,CEnIA,MAAM7B,GAAa,CCAVC,MAAM,gBDCTC,GCHN,cDIMC,GAAa,CCJnBqB,IAAA,EAIkBvB,MAAM,sBDIlBiC,GAAa,CCRnBV,IAAA,EAe8BvB,MAAM,mBDH9BqE,GAAa,CCZnB9C,IAAA,EAgBuDvB,MAAM,gBDAvDsE,GAAa,CChBnB/C,IAAA,EAmB0CvB,MAAM,cDC1CuE,GAAa,CCEVvE,MAAM,cDDTwE,GAAa,CCETxE,MAAM,eDDVgL,GAAa,CCEVhL,MAAM,kBDDTiL,GCvBN,SDyBM,SAAU7K,GAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OAAQK,EAAAA,EAAAA,OCzBRC,EAAAA,EAAAA,IA+BM,OA/BDhB,MAAM,aAAcsC,QAAKhC,EAAA,KAAAA,EAAA,GD6BhC,IAAIkC,IC7B8BnC,EAAA0L,iBAAA1L,EAAA0L,mBAAAvJ,KD8B7B,EC7BDvB,EAAAA,EAAAA,IAkBM,MAlBNlB,GAkBM,CAjBOM,EAAA2L,YD+BNjL,EAAAA,EAAAA,OC/BLC,EAAAA,EAAAA,IAAkF,OAHxFO,IAAA,EAG6B0K,IAAK5L,EAAA2L,UAAYE,IAAK7L,EAAApB,MAAMsK,SAAUvJ,MAAM,gBDoC5D,KAAM,ECvCnBC,ODwCWc,EAAAA,EAAAA,OCpCLC,EAAAA,EAAAA,IASM,MATNd,GASMI,EAAA,KAAAA,EAAA,KAbZ6L,EAAAA,EAAAA,IAAA,qjBAeiB9L,EAAAoL,cD6BN1K,EAAAA,EAAAA,OC7BLC,EAAAA,EAAAA,IAA+D,MAA/DiB,GAAgD,eAftDL,EAAAA,EAAAA,IAAA,OAgBiBvB,EAAApB,MAAMmN,WAAa/L,EAAApB,MAAMoN,cD+B/BtL,EAAAA,EAAAA,OC/BLC,EAAAA,EAAAA,IAEM,MAFNqD,IAEMM,EAAAA,EAAAA,IADDtE,EAAApB,MAAMmN,WAAa/L,EAAApB,MAAMoN,aAAW,KAjB/CzK,EAAAA,EAAAA,IAAA,OAmBgC,WAAfvB,EAAApB,MAAMM,OD+BZwB,EAAAA,EAAAA,OC/BLC,EAAAA,EAAAA,IAAmE,MAAnEsD,GAAuD,YAnB7D1C,EAAAA,EAAAA,IAAA,UAsBIX,EAAAA,EAAAA,IAGM,MAHNsD,GAGM,EAFJtD,EAAAA,EAAAA,IAAwE,KAAxEuD,IAAwEG,EAAAA,EAAAA,IAA7CtE,EAAApB,MAAMsK,UAAYlJ,EAAApB,MAAMuK,eAAa,IAChEvI,EAAAA,EAAAA,IAA+C,IAA/C+J,IAA+CrG,EAAAA,EAAAA,IAAlBtE,EAAAiM,aAAW,MAG1CrL,EAAAA,EAAAA,IAIS,UAJDjB,MAAM,kBAAmBsC,QAAKhC,EAAA,KAAAA,EAAA,IA3B1CiC,EAAAA,EAAAA,KD6DA,IAAIC,IClC6CnC,EAAAkM,gBAAAlM,EAAAkM,kBAAA/J,IAAc,YDmCxD,GACAzB,EAAAA,EAAAA,OCnCDC,EAAAA,EAAAA,IAEM,OAFDa,MAAM,6BAA6BC,QAAQ,YAAaC,KAAM1B,EAAAmM,aAAe,eAAiB,OAAQxK,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SD2CtL1B,EAAO,KAAOA,EAAO,GAAK,EC1C3BW,EAAAA,EAAAA,IAAmE,QAA7DwL,EAAE,qDAAmD,WD4CzD,ECzEVxB,QD4EA,C,wBClCA,UAAejG,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,YACNC,MAAO,CACLjG,MAAO,CACLM,KAAMmN,OACNpH,UAAU,GAEZmG,YAAa,CACXlM,KAAM4F,QACNC,SAAS,IAGbO,KAAAA,CAAMT,GACJ,MAAMU,GAASC,EAAAA,EAAAA,MACT8G,GAAiBC,EAAAA,GAAAA,KACjBjK,GAAUxC,EAAAA,EAAAA,KAAI,GAGdd,GAASwN,EAAAA,EAAAA,KAAS,KAAM7N,EAAAA,GAAAA,IAAekG,EAAMjG,SAC7C6N,GAAUD,EAAAA,EAAAA,KAAS,KAAMzN,EAAAA,GAAAA,IAAW8F,EAAMjG,SAG1CuN,GAAeK,EAAAA,EAAAA,KAAS,OACvBxN,EAAO4G,QAAU6G,EAAQ7G,QACvB0G,EAAeH,aAAaM,EAAQ7G,MAAO5G,EAAO4G,SAIrD+F,GAAYa,EAAAA,EAAAA,KAAS,IAErB3H,EAAMjG,MAAMW,aAAaC,KACpBqF,EAAMjG,MAAMW,YAAYC,KAE1BqF,EAAMjG,MAAMa,YAAc,OAI7BwM,GAAcO,EAAAA,EAAAA,KAAS,IACpB3H,EAAMjG,MAAMyK,KAAO,GAAGxE,EAAMjG,MAAMyK,OAAS,cAI9CqC,EAAkBA,KAClB1M,EAAO4G,OAAS6G,EAAQ7G,OAC1BL,EAAOU,KAAK,UAAUjH,EAAO4G,SAAS6G,EAAQ7G,QAChD,EAIIsG,EAAiBnG,UAGrB,GAFA2G,EAAMC,kBAED3N,EAAO4G,OAAU6G,EAAQ7G,MAA9B,CAEAtD,EAAQsD,OAAQ,EAEhB,IACE,GAAIuG,EAAavG,MAAO,CACtB,MAAMgH,EAAaN,EAAeO,cAAcJ,EAAQ7G,MAAO5G,EAAO4G,OAClEgH,SACIN,EAAeQ,eAAeF,EAExC,YACQN,EAAeS,YAAYN,EAAQ7G,MAAO5G,EAAO4G,MAE3D,CAAE,MAAOnC,GACPyC,QAAQzC,MAAM,2BAA4BA,EAC5C,CAAE,QACAnB,EAAQsD,OAAQ,CAClB,CAjB2C,CAiB3C,EAGF,MAAO,CACL5G,SACAyN,UACAN,eACAR,YACAM,cACAP,kBACAQ,iBACA5J,UAEJ,ICrHI,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,MHsDA,IAAeqC,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,WACNQ,WAAY,CACV4H,UAASA,IAEXnI,MAAO,CACLzD,MAAO,CACLlC,KAAM8F,OACNC,UAAU,GAEZhE,SAAU,CACR/B,KAAM8F,OACNC,UAAU,IAGdK,KAAAA,CAAMT,GACJ,MAAM4D,GAAS3I,EAAAA,EAAAA,IAAa,IACtBwC,GAAUxC,EAAAA,EAAAA,KAAI,GACd2D,GAAQ3D,EAAAA,EAAAA,IAAmB,MAC3BmN,GAAkBnN,EAAAA,EAAAA,IAAwB,MAC1CkL,GAAgBlL,EAAAA,EAAAA,KAAI,GACpB0L,GAAiB1L,EAAAA,EAAAA,KAAI,GACrB0D,GAAciC,EAAAA,EAAAA,KAGdwF,GAAoBnL,EAAAA,EAAAA,KAAI,GAGxBoN,GAAcpN,EAAAA,EAAAA,IAAI,GAClBuL,GAAevL,EAAAA,EAAAA,KAAI,GACnByL,GAAkBzL,EAAAA,EAAAA,KAAI,GAGtBqL,EAAapF,UACjBzD,EAAQsD,OAAQ,EAChBnC,EAAMmC,MAAQ,KAGdsH,EAAYtH,MAAQ,EACpByF,EAAazF,OAAQ,EAErB,IAEE,MAAMuH,QAAuB3J,EAAYkF,oBACvC7D,EAAM5D,SACNiM,EAAYtH,OAEd6C,EAAO7C,MAAQuH,EAKf9B,EAAazF,MAAQuH,EAAerF,QAAU,EAChD,CAAE,MAAO4B,GACPjG,EAAMmC,MAAQ,6BAChB,CAAE,QACAtD,EAAQsD,OAAQ,EAEhBC,YAAW,KACTuH,GAAqB,GACpB,IACL,GAIIhC,EAAexM,GAEZA,EAAMuC,GAAG2G,OAAS,GAAKuF,SAASzO,EAAMuC,IAAM,IAAM,EAIrDiM,EAAsBA,KAC1B,IAAKH,EAAgBrH,MAAO,OAE5B,MAAM,WAAEsF,EAAU,YAAEoC,EAAW,YAAEC,GAAgBN,EAAgBrH,MACjEoF,EAAcpF,MAAQsF,EAAa,EACnCM,EAAe5F,MAAQsF,EAAaoC,EAAcC,EAAc,GAG5DD,GAAeC,IACjB/B,EAAe5F,OAAQ,EACzB,EAGIsF,EAAaA,KACjB,IAAK+B,EAAgBrH,MAAO,OAE5B,MAAM4H,EAAmD,GAApCP,EAAgBrH,MAAM2H,YAC3CN,EAAgBrH,MAAM6H,SAAS,CAC7BC,MAAOF,EACPG,SAAU,UACV,EAGElC,EAAcA,KAClB,IAAKwB,EAAgBrH,MAAO,OAE5B,MAAM4H,EAAmD,GAApCP,EAAgBrH,MAAM2H,YAC3CN,EAAgBrH,MAAM6H,SAAS,CAC7BC,KAAMF,EACNG,SAAU,UACV,EAIEC,EAAeA,KACnBR,GAAqB,EAIjBS,EAAeA,KACnBT,GAAqB,EAIjBU,GAAShO,EAAAA,EAAAA,IAAwB,MACjCiO,GAAYjO,EAAAA,EAAAA,KAAI,IAEtBsG,EAAAA,EAAAA,KAAU,KAER,MAAM4H,EAAW,IAAIC,sBAAsBC,IACzC,MAAMC,EAAQD,EAAQ,GAClBC,EAAMC,iBAAmBL,EAAUnI,QACrCmI,EAAUnI,OAAQ,EAClBuF,IAGI2C,EAAOlI,OACToI,EAASK,UAAUP,EAAOlI,OAE9B,GACC,CACD0I,WAAY,QACZC,UAAW,KAITT,EAAOlI,OACToI,EAASQ,QAAQV,EAAOlI,OAItBqH,EAAgBrH,QAClBqH,EAAgBrH,MAAMU,iBAAiB,SAAUsH,GAEjD/H,YAAW,KACTuH,GAAqB,GACpB,MAILtD,OAAOxD,iBAAiB,SAAUuH,IAGlCtH,EAAAA,EAAAA,KAAgB,KACVuH,EAAOlI,OACToI,EAASK,UAAUP,EAAOlI,MAC5B,GACA,IAIJ,MAAM0F,EAAevF,UACnB,GAAKsF,EAAazF,QAAS2F,EAAgB3F,MAA3C,CAEA2F,EAAgB3F,OAAQ,EAExB,IAEEsH,EAAYtH,QAGZ,MAAM6I,QAAuBjL,EAAYkF,oBACvC7D,EAAM5D,SACNiM,EAAYtH,MACZ,IACA,GAIE6I,EAAe3G,OAAS,IAC1BW,EAAO7C,MAAQ,IAAI6C,EAAO7C,SAAU6I,IAItCpD,EAAazF,MAAQ6I,EAAe3G,QAAU,EAChD,CAAE,MAAO4B,GAEPwD,EAAYtH,OACd,CAAE,QACA2F,EAAgB3F,OAAQ,EAExBC,YAAW,KACTuH,GAAqB,GACpB,IACL,CAhCwD,CAgCxD,EAWF,OAPA7G,EAAAA,EAAAA,KAAgB,KACV0G,EAAgBrH,OAClBqH,EAAgBrH,MAAMY,oBAAoB,SAAUoH,GAEtD9D,OAAOtD,oBAAoB,SAAUqH,EAAa,IAG7C,CACLpF,SACAnG,UACAmB,QACAwJ,kBACAa,SACAC,YACA/C,gBACAQ,iBACAP,oBACAE,aACAC,cACAF,aACAO,cACAH,eACAD,eACAE,kBACA2B,cAEJ,IIzRI,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,MfuCA,IAAevI,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,cACNQ,WAAY,CACVsJ,UAAS,EACTC,eAAc,EACdC,SAAQA,IAEVtJ,KAAAA,GACE,MAAMgH,GAAiBC,EAAAA,GAAAA,KACjBsC,GAAkB/O,EAAAA,EAAAA,IAAwB,MAChD,IAAIkO,EAAwC,KAG5C,MAAMc,EAA6B,CACjC,CAAE3N,GAAI,EAAGC,MAAO,aAAcH,SAAU0H,EAAAA,EAAcC,SACtD,CAAEzH,GAAI,EAAGC,MAAO,UAAWH,SAAU0H,EAAAA,EAAcoG,KACnD,CAAE5N,GAAI,EAAGC,MAAO,WAAYH,SAAU0H,EAAAA,EAAcqG,UACpD,CAAE7N,GAAI,EAAGC,MAAO,QAASH,SAAU0H,EAAAA,EAAcsG,OACjD,CAAE9N,GAAI,EAAGC,MAAO,UAAWH,SAAU0H,EAAAA,EAAcuG,QACnD,CAAE/N,GAAI,EAAGC,MAAO,aAAcH,SAAU0H,EAAAA,EAAcwG,OACtD,CAAEhO,GAAI,EAAGC,MAAO,YAAaH,SAAU0H,EAAAA,EAAcyG,YAIjDC,EAA2B,EAG3BrO,GAAoBlB,EAAAA,EAAAA,IACxBgP,EAAWQ,MAAM,EAAGD,IAIhB/N,GAAuBxB,EAAAA,EAAAA,IAC3BgP,EAAWQ,MAAMD,IAIbhO,GAA2BvB,EAAAA,EAAAA,KAAI,GAG/ByP,EAA2BA,KAE/BlO,EAAyBuE,OAAQ,EAG7BoI,IACFA,EAASwB,aACTxB,EAAW,KACb,EAgCF,OA7BA5H,EAAAA,EAAAA,KAAUL,gBAEFuG,EAAemD,iBAGrBzB,EAAW,IAAIC,sBAAsBC,IACnC,MAAMC,EAAQD,EAAQ,GAClBC,EAAMC,gBACRmB,GACF,GACC,CACDjB,WAAY,QACZC,UAAW,KAITM,EAAgBjJ,OAClBoI,EAASQ,QAAQK,EAAgBjJ,MACnC,KAGFW,EAAAA,EAAAA,KAAgB,KAEVyH,IACFA,EAASwB,aACTxB,EAAW,KACb,IAGK,CACLhN,oBACAM,uBACAD,2BACAwN,kBAEJ,IgB/HI,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAAS9O,GAAQ,CAAC,YAAY,qBAEzF,K,6CC0CY4I,E,UAAZ,SAAYA,GACVA,EAAA,qBACAA,EAAA,aACAA,EAAA,uBACAA,EAAA,iBACAA,EAAA,mBACAA,EAAA,iBACAA,EAAA,wBACD,EARD,CAAYA,IAAAA,EAAa,KAUzB,MAAM+G,EACIC,QAERC,WAAAA,GAEEC,KAAKF,QAAU,yCACjB,CAGA,yBAAMjH,CAAoBzH,EAAyB6O,EAAe,EAAGC,EAAgB,IAEnF,MAAMC,QAAiBC,EAAAA,EAAIC,IAAI,GAAGL,KAAKF,2BAA2B1O,IAAY,CAC5EkP,OAAQ,CACNJ,QACAD,UAGJ,OAAOE,EAASI,IAClB,CAGA,cAAMC,CAASlP,GACb,MAAM6O,QAAiBC,EAAAA,EAAIC,IAAI,GAAGL,KAAKF,kBAAkBxO,KACzD,OAAO6O,EAASI,IAClB,CAGA,iBAAME,CAAYC,GAChB,MAAMP,QAAiBC,EAAAA,EAAIC,IAAI,GAAGL,KAAKF,iBAAkB,CACvDQ,OAAQ,CAAEI,SAEZ,OAAOP,EAASI,IAClB,EAGF,YAAmBV,C,gEC9EZ,MAAMjK,GAAiB+K,EAAAA,EAAAA,IAAY,SAAU,CAClDC,MAAOA,KAAA,CACLC,aAAc,KACdpO,SAAS,EACTmB,MAAO,KAEP0J,eAAgB,CAAC,EACjBwD,gBAAiB,CAAC,EAClBC,cAAe,CAAC,EAEhBC,gBAAiB,CAAC,IAGpBC,QAAS,CACP,oBAAMC,CAAe/R,EAAgBmC,GAMnC,GALA0O,KAAKvN,SAAU,EACfuN,KAAKpM,MAAQ,KACboM,KAAKa,aAAe,MAGf5G,OAAOkH,WAAalH,OAAOkH,SAASC,OAGvC,OAFApB,KAAKpM,MAAQ,wCACboM,KAAKvN,SAAU,GAIjB,IACE,MAAM0N,QAAiBC,EAAAA,EAAIC,IAAI,iBAAiB/O,IAAM,CACpDgP,OAAQ,CAAEnR,YAGRgR,EAASI,KACXP,KAAKa,aAAeV,EAASI,KAE7BP,KAAKpM,MAAQ,sBAEjB,CAAE,MAAOA,GACPoM,KAAKpM,MAAQA,EAAMuM,UAAUI,MAAMxM,SAAWH,EAAMG,SAAW,eAEjE,CAAE,QACAiM,KAAKvN,SAAU,CACjB,CACF,EAEA,iBAAM0D,CAAYuK,GAMhB,GALAV,KAAKvN,SAAU,EACfuN,KAAKpM,MAAQ,KACboM,KAAKa,aAAe,MAGf5G,OAAOkH,WAAalH,OAAOkH,SAASC,OAGvC,OAFApB,KAAKpM,MAAQ,mCACboM,KAAKvN,SAAU,EACR,KAGT,IACE,MAAM0N,QAAiBC,EAAAA,EAAIC,IAAI,gBAAiB,CAC9CC,OAAQ,CAAEI,SAGZ,OAAIP,EAASI,KAAKc,SAChBrB,KAAKa,aAAeV,EAASI,KAAKA,KAC3BJ,EAASI,KAAKA,OAErBP,KAAKpM,MAAQuM,EAASI,KAAKxM,SAAW,yBAC/B,KAEX,CAAE,MAAOH,GAGP,OAFAoM,KAAKpM,MAAQA,EAAMuM,UAAUI,MAAMxM,SAAWH,EAAMG,SAAW,gBAExD,IACT,CAAE,QACAiM,KAAKvN,SAAU,CACjB,CACF,EAEA6O,cAAAA,CAAeC,GAEblL,QAAQmL,IAAI,oBAAqBD,EACnC,EAEAE,iBAAAA,GACEzB,KAAKa,aAAe,IACtB,EAGA,2BAAMa,CAAsBtQ,EAAyB6O,EAAe,EAAGC,EAAgB,IACrF,MAAMyB,EAAa,GAAGvQ,KAAY6O,KAAQC,IAG1CF,KAAKc,gBAAkB,IAAKd,KAAKc,gBAAiB,CAAC1P,IAAW,GAC9D4O,KAAKe,cAAgB,IAAKf,KAAKe,cAAe,CAAC3P,GAAW,MAE1D,IACE,MAAM+O,QAAiByB,EAAAA,EAAa/I,oBAAoBzH,EAAU6O,EAAMC,GAGxE,OAAIC,EAASI,MAAMtI,OAAS,GAGb,IAATgI,IACFD,KAAK1C,eAAelM,GAAY+O,EAASI,MAEpCJ,EAASI,OAGhBP,KAAKe,cAAc3P,GAAY,4CACxB,GAEX,CAAE,MAAOwC,GAGP,OADAoM,KAAKe,cAAc3P,GAAYwC,EAAMG,SAAW,8BACzC,EACT,CAAE,QAEAiM,KAAKc,gBAAgB1P,IAAY,SAC1B4O,KAAKgB,gBAAgBW,EAC9B,CACF,EAGA,yBAAM9I,CACJzH,EACA6O,EAAe,EACfC,EAAgB,GAChB2B,GAAoB,GAEpB,MAAMF,EAAa,GAAGvQ,KAAY6O,KAAQC,IAG1C,GAAI2B,GAAqB,IAAT5B,GAAcD,KAAK1C,eAAelM,IAAW6G,OAAS,EACpE,OAAO+H,KAAK1C,eAAelM,GAI7B,GAAI4O,KAAKgB,gBAAgBW,GACvB,aAAa3B,KAAKgB,gBAAgBW,GAIpC,MAAMG,EAAU9B,KAAK0B,sBAAsBtQ,EAAU6O,EAAMC,GAG3D,OAFAF,KAAKgB,gBAAgBW,GAAcG,QAEtBA,CACf,I,uDC3IG,MAAMpF,GAAoBiE,EAAAA,EAAAA,IAAY,YAAa,CACxDC,MAAOA,KAAA,CACLmB,UAAW,GACXtP,SAAS,EACTmB,MAAO,KACPoO,cAAe,IAGjBf,QAAS,CACP,oBAAMrB,CAAeqC,GAAe,GAElC,MAAMC,EAAMC,KAAKD,MACXE,EAAc,IAEpB,MAAKH,GACDjC,KAAK+B,UAAU9J,OAAS,GACxBiK,EAAMlC,KAAKgC,cAAgBI,GAF/B,CAMApC,KAAKvN,SAAU,EACfuN,KAAKpM,MAAQ,KAEb,IACE,MAAMuM,QAAiBC,EAAAA,EAAIC,IAAI,cAE3BF,EAASI,KAAKc,SAChBrB,KAAK+B,UAAY5B,EAASI,KAAKA,KAC/BP,KAAKgC,cAAgBE,GAErBlC,KAAKpM,MAAQuM,EAASI,KAAKxM,SAAW,0BAE1C,CAAE,MAAOH,GACPoM,KAAKpM,MAAQA,EAAMuM,UAAUI,MAAMxM,SAAWH,EAAMG,SAAW,gBAC/DsC,QAAQzC,MAAM,4BAA6BA,EAC7C,CAAE,QACAoM,KAAKvN,SAAU,CACjB,CAnBA,CAoBF,EAEA,iBAAMyK,CAAYmF,EAAkBlT,GAClC6Q,KAAKvN,SAAU,EACfuN,KAAKpM,MAAQ,KAEb,IACE,MAAMuM,QAAiBC,EAAAA,EAAIkC,KAAK,aAAc,CAAED,WAAUlT,WAE1D,GAAIgR,EAASI,KAAKc,QAAS,CAEzB,IAAIkB,EACJ,IACE,GAAe,OAAXpT,EAAiB,CAEnB,MAAMqT,QAAsBpC,EAAAA,EAAIC,IAAI,iBAAiBgC,eACrDE,EAAeC,EAAcjC,IAC/B,KAAO,CACL,MAAMiC,QAAsBpC,EAAAA,EAAIC,IAAI,iBAAiBgC,iBACrDE,EAAeC,EAAcjC,IAC/B,CAGAP,KAAK+B,UAAUU,QAAQ,CACrBC,IAAKvC,EAASI,KAAKA,KAAKmC,IACxBvT,OAAQA,EACRwT,QAASJ,GAEb,CAAE,MAAOK,GACPvM,QAAQzC,MAAM,gCAAiCgP,GAE/C5C,KAAK+B,UAAUU,QAAQ,CACrBC,IAAKvC,EAASI,KAAKA,KAAKmC,IACxBvT,OAAQA,EACRwT,QAAS,MAEb,CAEA,OAAO,CACT,CAEE,OADA3C,KAAKpM,MAAQuM,EAASI,KAAKxM,SAAW,0BAC/B,CAEX,CAAE,MAAOH,GAGP,OAFAoM,KAAKpM,MAAQA,EAAMuM,UAAUI,MAAMxM,SAAWH,EAAMG,SAAW,gBAC/DsC,QAAQzC,MAAM,yBAA0BA,IACjC,CACT,CAAE,QACAoM,KAAKvN,SAAU,CACjB,CACF,EAEA,oBAAMwK,CAAe3L,GACnB0O,KAAKvN,SAAU,EACfuN,KAAKpM,MAAQ,KAEb,IACE,MAAMuM,QAAiBC,EAAAA,EAAIyC,OAAO,cAAcvR,KAEhD,OAAI6O,EAASI,KAAKc,SAEhBrB,KAAK+B,UAAY/B,KAAK+B,UAAU9I,QAAO6J,GAAYA,EAASJ,MAAQpR,KAC7D,IAEP0O,KAAKpM,MAAQuM,EAASI,KAAKxM,SAAW,6BAC/B,EAEX,CAAE,MAAOH,GAGP,OAFAoM,KAAKpM,MAAQA,EAAMuM,UAAUI,MAAMxM,SAAWH,EAAMG,SAAW,gBAC/DsC,QAAQzC,MAAM,2BAA4BA,IACnC,CACT,CAAE,QACAoM,KAAKvN,SAAU,CACjB,CACF,EAEA6J,YAAAA,CAAa+F,EAAkBlT,GAC7B,OAAO6Q,KAAK+B,UAAUgB,MAAKD,KACpBA,EAASH,UAEC,OAAXxT,EACK2T,EAASH,QAAQ3T,QAAUqT,GAAYS,EAAS3T,SAAWA,EAC9C,SAAXA,IACF2T,EAASH,QAAQ1T,UAAYoT,GAAYS,EAAS3T,SAAWA,KAK1E,EAEA6N,aAAAA,CAAcqF,EAAkBlT,GAC9B,MAAM2T,EAAW9C,KAAK+B,UAAUiB,MAAKF,KAC9BA,EAASH,UAEC,OAAXxT,EACK2T,EAASH,QAAQ3T,QAAUqT,GAAYS,EAAS3T,SAAWA,EAC9C,SAAXA,IACF2T,EAASH,QAAQ1T,UAAYoT,GAAYS,EAAS3T,SAAWA,MAMxE,OAAO2T,EAAWA,EAASJ,IAAM,IACnC,I", "sources": ["webpack://cinema-bot-frontend/./src/utils/movieUtils.ts", "webpack://cinema-bot-frontend/./src/views/CatalogView.vue?1ce8", "webpack://cinema-bot-frontend/./src/views/CatalogView.vue", "webpack://cinema-bot-frontend/./src/components/SearchBar.vue?a2bd", "webpack://cinema-bot-frontend/./src/components/SearchBar.vue", "webpack://cinema-bot-frontend/./src/components/ErrorMessage.vue?45ec", "webpack://cinema-bot-frontend/./src/components/ErrorMessage.vue", "webpack://cinema-bot-frontend/./src/components/ErrorMessage.vue?5a16", "webpack://cinema-bot-frontend/./src/components/SearchBar.vue?fbac", "webpack://cinema-bot-frontend/./src/components/catalog/BannerCarousel.vue?6d66", "webpack://cinema-bot-frontend/./src/components/catalog/BannerCarousel.vue", "webpack://cinema-bot-frontend/./src/components/catalog/BannerCarousel.vue?d8ed", "webpack://cinema-bot-frontend/./src/components/catalog/MovieRow.vue?f820", "webpack://cinema-bot-frontend/./src/components/catalog/MovieRow.vue", "webpack://cinema-bot-frontend/./src/components/catalog/MovieTile.vue?a20a", "webpack://cinema-bot-frontend/./src/components/catalog/MovieTile.vue", "webpack://cinema-bot-frontend/./src/components/catalog/MovieTile.vue?c906", "webpack://cinema-bot-frontend/./src/components/catalog/MovieRow.vue?af4a", "webpack://cinema-bot-frontend/./src/views/CatalogView.vue?c1a6", "webpack://cinema-bot-frontend/./src/services/vibixService.ts", "webpack://cinema-bot-frontend/./src/store/movies.ts", "webpack://cinema-bot-frontend/./src/store/bookmarks.ts"], "sourcesContent": ["// Импорт не используется, но оставлен для документации\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport { Movie } from '@/services/vibixService';\n\n/**\n * Определяет источник фильма (Кинопоиск или IMDB)\n * @param movie Объект фильма\n * @returns 'kp', 'imdb' или null, если источник не определен\n */\nexport function getMovieSource(movie: any | null): 'kp' | 'imdb' | null {\n  if (!movie) return null;\n\n  if (movie.kp_id) return 'kp';\n  if (movie.imdb_id) return 'imdb';\n\n  return null;\n}\n\n/**\n * Получает ID фильма в зависимости от источника\n * @param movie Объект фильма\n * @returns ID фильма или null, если ID не найден\n */\nexport function getMovieId(movie: any | null): string | null {\n  if (!movie) return null;\n\n  const source = getMovieSource(movie);\n\n  if (source === 'kp') return movie.kp_id || null;\n  if (source === 'imdb') return movie.imdb_id || null;\n\n  return null;\n}\n\n/**\n * Форматирует тип фильма для отображения\n * @param type Тип фильма\n * @returns Отформатированный тип фильма\n */\nexport function formatMovieType(type: string): string {\n  const types: Record<string, string> = {\n    movie: 'Фильм',\n    tv: 'Сериал',\n    anime: 'Аниме',\n    'tv-series': 'Сериал',\n    'anime-series': 'Аниме-сериал'\n  };\n\n  return types[type] || type;\n}\n\n/**\n * Получает URL постера фильма с приоритетом TMDB\n * @param movie Объект фильма\n * @returns URL постера или null, если постер отсутствует\n */\nexport function getMoviePosterUrl(movie: any | null): string | null {\n  if (!movie) return null;\n\n  // Приоритет: TMDB w500 > оригинальный poster_url > null\n  if (movie.poster_urls?.w500) {\n    return movie.poster_urls.w500;\n  }\n\n  return movie.poster_url || null;\n}\n", "import { resolveComponent as _resolveComponent, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"catalog-view\" }\nconst _hoisted_2 = { class: \"container\" }\nconst _hoisted_3 = {\n  ref: \"lazyLoadTrigger\",\n  class: \"lazy-load-trigger\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_BannerCarousel = _resolveComponent(\"BannerCarousel\")!\n  const _component_MovieRow = _resolveComponent(\"MovieRow\")!\n  const _component_SearchBar = _resolveComponent(\"SearchBar\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createVNode(_component_BannerCarousel),\n      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.visibleCategories, (category) => {\n        return (_openBlock(), _createElementBlock(\"div\", {\n          key: category.id\n        }, [\n          _createVNode(_component_MovieRow, {\n            title: category.title,\n            category: category.category\n          }, null, 8, [\"title\", \"category\"])\n        ]))\n      }), 128)),\n      _createElementVNode(\"div\", _hoisted_3, null, 512),\n      (_ctx.showAdditionalCategories)\n        ? (_openBlock(true), _createElementBlock(_Fragment, { key: 0 }, _renderList(_ctx.additionalCategories, (category) => {\n            return (_openBlock(), _createElementBlock(\"div\", {\n              key: category.id\n            }, [\n              _createVNode(_component_MovieRow, {\n                title: category.title,\n                category: category.category\n              }, null, 8, [\"title\", \"category\"])\n            ]))\n          }), 128))\n        : _createCommentVNode(\"\", true)\n    ]),\n    _createVNode(_component_SearchBar)\n  ]))\n}", "<template>\n  <div class=\"catalog-view\">\n    <div class=\"container\">\n      <!-- Banner Carousel -->\n      <BannerCarousel />\n\n      <!-- Первые три категории (всегда видимы) -->\n      <div v-for=\"category in visibleCategories\" :key=\"category.id\">\n        <MovieRow\n          :title=\"category.title\"\n          :category=\"category.category\"\n        />\n      </div>\n\n      <!-- Триггер для загрузки дополнительных категорий -->\n      <div ref=\"lazyLoadTrigger\" class=\"lazy-load-trigger\"></div>\n\n      <!-- Дополнительные категории (загружаются и рендерятся только при прокрутке) -->\n      <template v-if=\"showAdditionalCategories\">\n        <div v-for=\"category in additionalCategories\" :key=\"category.id\">\n          <MovieRow\n            :title=\"category.title\"\n            :category=\"category.category\"\n          />\n        </div>\n      </template>\n    </div>\n\n    <!-- Search Bar (floating) -->\n    <SearchBar />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, onMounted, ref, onBeforeUnmount } from 'vue';\nimport SearchBar from '@/components/SearchBar.vue';\nimport BannerCarousel from '@/components/catalog/BannerCarousel.vue';\nimport MovieRow from '@/components/catalog/MovieRow.vue';\nimport { useBookmarksStore } from '@/store/bookmarks';\nimport { MovieCategory } from '@/services/vibixService';\n\n// Определяем интерфейс для категории\ninterface CategoryItem {\n  id: number;\n  title: string;\n  category: MovieCategory;\n}\n\nexport default defineComponent({\n  name: 'CatalogView',\n  components: {\n    SearchBar,\n    BannerCarousel,\n    MovieRow\n  },\n  setup() {\n    const bookmarksStore = useBookmarksStore();\n    const lazyLoadTrigger = ref<HTMLElement | null>(null);\n    let observer: IntersectionObserver | null = null;\n\n    // Определение категорий\n    const CATEGORIES: CategoryItem[] = [\n      { id: 1, title: 'Популярные', category: MovieCategory.POPULAR },\n      { id: 2, title: 'Новинки', category: MovieCategory.NEW },\n      { id: 3, title: 'Триллеры', category: MovieCategory.THRILLER },\n      { id: 4, title: 'Драмы', category: MovieCategory.DRAMA },\n      { id: 5, title: 'Боевики', category: MovieCategory.ACTION },\n      { id: 6, title: 'Фантастика', category: MovieCategory.SCIFI },\n      { id: 7, title: 'Детективы', category: MovieCategory.DETECTIVE }\n    ];\n\n    // Количество категорий, которые показываются сразу\n    const INITIAL_CATEGORIES_COUNT = 3;\n\n    // Видимые категории (изначально только первые три)\n    const visibleCategories = ref<CategoryItem[]>(\n      CATEGORIES.slice(0, INITIAL_CATEGORIES_COUNT)\n    );\n\n    // Дополнительные категории (заполняем сразу, но не показываем)\n    const additionalCategories = ref<CategoryItem[]>(\n      CATEGORIES.slice(INITIAL_CATEGORIES_COUNT)\n    );\n\n    // Флаг для отображения дополнительных категорий\n    const showAdditionalCategories = ref(false);\n\n    // Функция для загрузки дополнительных категорий\n    const loadAdditionalCategories = () => {\n      // Показываем дополнительные категории\n      showAdditionalCategories.value = true;\n\n      // Отключаем observer после загрузки всех категорий\n      if (observer) {\n        observer.disconnect();\n        observer = null;\n      }\n    };\n\n    onMounted(async () => {\n      // Fetch bookmarks to know which movies are bookmarked\n      await bookmarksStore.fetchBookmarks();\n\n      // Создаем и настраиваем Intersection Observer для ленивой загрузки\n      observer = new IntersectionObserver((entries) => {\n        const entry = entries[0];\n        if (entry.isIntersecting) {\n          loadAdditionalCategories();\n        }\n      }, {\n        rootMargin: '200px', // Начинаем загрузку, когда до элемента остается 200px\n        threshold: 0.1\n      });\n\n      // Наблюдаем за триггером\n      if (lazyLoadTrigger.value) {\n        observer.observe(lazyLoadTrigger.value);\n      }\n    });\n\n    onBeforeUnmount(() => {\n      // Очищаем observer при размонтировании компонента\n      if (observer) {\n        observer.disconnect();\n        observer = null;\n      }\n    });\n\n    return {\n      visibleCategories,\n      additionalCategories,\n      showAdditionalCategories,\n      lazyLoadTrigger\n    };\n  }\n});\n</script>\n\n<style scoped>\n.catalog-view {\n  padding-bottom: 80px;\n}\n\n.container {\n  padding: var(--spacing-md) 16px 0 16px;\n}\n\n.lazy-load-trigger {\n  height: 10px;\n  margin: 10px 0;\n  /* Невидимый элемент */\n  opacity: 0;\n}\n</style>\n", "import { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, withModifiers as _withModifiers, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = [\"disabled\"]\nconst _hoisted_2 = {\n  key: 0,\n  class: \"loading-spinner main\"\n}\nconst _hoisted_3 = {\n  key: 1,\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  \"stroke-width\": \"2\",\n  \"stroke-linecap\": \"round\",\n  \"stroke-linejoin\": \"round\"\n}\nconst _hoisted_4 = {\n  key: 2,\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  \"stroke-width\": \"2\",\n  \"stroke-linecap\": \"round\",\n  \"stroke-linejoin\": \"round\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_ErrorMessage = _resolveComponent(\"ErrorMessage\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", null, [\n    _createElementVNode(\"div\", {\n      class: _normalizeClass([\"search-container\", { expanded: _ctx.isExpanded }]),\n      onClick: _cache[4] || (_cache[4] = _withModifiers(() => {}, [\"stop\"]))\n    }, [\n      _createElementVNode(\"button\", {\n        class: \"search-toggle\",\n        onClick: _cache[0] || (_cache[0] = _withModifiers(\n//@ts-ignore\n(...args) => (_ctx.handleMainButtonClick && _ctx.handleMainButtonClick(...args)), [\"stop\"])),\n        disabled: _ctx.loading\n      }, [\n        (_ctx.loading)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2))\n          : (_ctx.searchQuery.trim())\n            ? (_openBlock(), _createElementBlock(\"svg\", _hoisted_3, _cache[5] || (_cache[5] = [\n                _createElementVNode(\"line\", {\n                  x1: \"5\",\n                  y1: \"12\",\n                  x2: \"19\",\n                  y2: \"12\"\n                }, null, -1),\n                _createElementVNode(\"polyline\", { points: \"12 5 19 12 12 19\" }, null, -1)\n              ])))\n            : (_openBlock(), _createElementBlock(\"svg\", _hoisted_4, _cache[6] || (_cache[6] = [\n                _createElementVNode(\"circle\", {\n                  cx: \"11\",\n                  cy: \"11\",\n                  r: \"8\"\n                }, null, -1),\n                _createElementVNode(\"line\", {\n                  x1: \"21\",\n                  y1: \"21\",\n                  x2: \"16.65\",\n                  y2: \"16.65\"\n                }, null, -1)\n              ])))\n      ], 8, _hoisted_1),\n      _createElementVNode(\"div\", {\n        class: \"search-form\",\n        onClick: _cache[3] || (_cache[3] = _withModifiers(() => {}, [\"stop\"]))\n      }, [\n        _withDirectives(_createElementVNode(\"input\", {\n          ref: \"searchInput\",\n          type: \"text\",\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.searchQuery) = $event)),\n          placeholder: \"Ссылка на кинопоиск или imdb\",\n          onKeyup: _cache[2] || (_cache[2] = _withKeys(\n//@ts-ignore\n(...args) => (_ctx.search && _ctx.search(...args)), [\"enter\"]))\n        }, null, 544), [\n          [_vModelText, _ctx.searchQuery]\n        ])\n      ])\n    ], 2),\n    (_ctx.moviesStore.error && _ctx.isExpanded)\n      ? (_openBlock(), _createBlock(_component_ErrorMessage, {\n          key: 0,\n          show: true,\n          message: _ctx.moviesStore.error,\n          inline: true,\n          \"show-close\": true,\n          onClose: _ctx.clearError,\n          class: \"search-error\"\n        }, null, 8, [\"message\", \"onClose\"]))\n      : _createCommentVNode(\"\", true)\n  ]))\n}", "<template>\n  <div>\n    <div class=\"search-container\" :class=\"{ expanded: isExpanded }\" @click.stop>\n      <button class=\"search-toggle\" @click.stop=\"handleMainButtonClick\" :disabled=\"loading\">\n        <div v-if=\"loading\" class=\"loading-spinner main\"></div>\n        <!-- Стрелка, когда есть текст для поиска -->\n        <svg v-else-if=\"searchQuery.trim()\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\n          <polyline points=\"12 5 19 12 12 19\"></polyline>\n        </svg>\n        <!-- Лупа по умолчанию -->\n        <svg v-else xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\n          <line x1=\"21\" y1=\"21\" x2=\"16.65\" y2=\"16.65\"></line>\n        </svg>\n      </button>\n\n      <div class=\"search-form\" @click.stop>\n        <input\n          ref=\"searchInput\"\n          type=\"text\"\n          v-model=\"searchQuery\"\n          placeholder=\"Ссылка на кинопоиск или imdb\"\n          @keyup.enter=\"search\"\n        />\n      </div>\n    </div>\n\n    <!-- Показываем ошибку поиска -->\n    <ErrorMessage\n      v-if=\"moviesStore.error && isExpanded\"\n      :show=\"true\"\n      :message=\"moviesStore.error\"\n      :inline=\"true\"\n      :show-close=\"true\"\n      @close=\"clearError\"\n      class=\"search-error\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { useMoviesStore } from '@/store/movies';\nimport ErrorMessage from './ErrorMessage.vue';\n\nexport default defineComponent({\n  name: 'SearchBar',\n  components: {\n    ErrorMessage\n  },\n  setup() {\n    const router = useRouter();\n    const moviesStore = useMoviesStore();\n    const searchQuery = ref('');\n    const isExpanded = ref(false);\n    const loading = ref(false);\n    const searchInput = ref<HTMLInputElement | null>(null);\n\n    // Clear error\n    const clearError = () => {\n      moviesStore.error = null;\n    };\n\n    // Toggle search form visibility\n    const toggleSearch = () => {\n      isExpanded.value = !isExpanded.value;\n      if (isExpanded.value && searchInput.value) {\n        // Фокусируемся на поле ввода при раскрытии\n        setTimeout(() => {\n          searchInput.value?.focus();\n        }, 300);\n      }\n    };\n\n    // Handle main button click (search or toggle)\n    const handleMainButtonClick = () => {\n      if (searchQuery.value.trim()) {\n        // Если есть текст - выполняем поиск\n        search();\n      } else {\n        // Если нет текста - переключаем видимость\n        toggleSearch();\n      }\n    };\n\n    // Search for movie\n    const search = async () => {\n      if (!searchQuery.value.trim() || loading.value) return;\n\n      loading.value = true;\n      // Очищаем предыдущие ошибки при новом поиске\n      clearError();\n\n      try {\n        const movie = await moviesStore.searchMovie(searchQuery.value);\n\n        if (movie) {\n          // Determine source and ID\n          let source = 'kp';\n          let id = movie.kp_id;\n\n          if (!id && movie.imdb_id) {\n            source = 'imdb';\n            id = movie.imdb_id;\n          }\n\n          if (source && id) {\n            // Navigate to movie page\n            router.push(`/movie/${source}/${id}`);\n\n            // Reset search completely\n            searchQuery.value = '';\n            isExpanded.value = false;\n            clearError();\n          }\n        }\n      } catch (error) {\n        console.error('Error searching movie:', error);\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // Close search when clicking outside\n    const handleClickOutside = () => {\n      if (isExpanded.value) {\n        // Закрываем поиск и очищаем содержимое\n        isExpanded.value = false;\n        searchQuery.value = '';\n        clearError();\n      }\n    };\n\n    // Setup click outside listener\n    onMounted(() => {\n      document.addEventListener('click', handleClickOutside);\n    });\n\n    onBeforeUnmount(() => {\n      document.removeEventListener('click', handleClickOutside);\n    });\n\n    return {\n      searchQuery,\n      isExpanded,\n      loading,\n      searchInput,\n      moviesStore,\n      toggleSearch,\n      handleMainButtonClick,\n      search,\n      clearError\n    };\n  }\n});\n</script>\n\n<style scoped>\n.search-container {\n  position: fixed;\n  top: var(--spacing-md);\n  right: var(--spacing-md);\n  z-index: 10;\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n}\n\n.search-toggle {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  box-shadow: var(--shadow-md);\n  z-index: 2;\n}\n\n.search-toggle svg {\n  width: 24px;\n  height: 24px;\n}\n\n.search-toggle:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.search-form {\n  position: absolute;\n  right: 0;\n  top: 0;\n  background-color: var(--tg-theme-secondary-bg-color);\n  border-radius: 24px;\n  display: flex;\n  align-items: center;\n  padding: 6px;\n  width: 0;\n  overflow: hidden;\n  transition: width 0.3s ease, opacity 0.3s ease;\n  opacity: 0;\n  box-shadow: var(--shadow-sm);\n}\n\n.expanded .search-form {\n  width: calc(100vw - 32px);\n  max-width: 500px;\n  opacity: 1;\n}\n\n.search-form input {\n  flex: 1;\n  border: none;\n  background: none;\n  padding: 8px 12px;\n  font-size: var(--font-size-sm);\n  color: var(--tg-theme-text-color);\n  outline: none;\n}\n\n\n\n.loading-spinner {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top-color: white;\n  animation: spin 1s ease-in-out infinite;\n}\n\n.loading-spinner.main {\n  width: 20px;\n  height: 20px;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n.search-error {\n  position: fixed;\n  top: 64px;\n  left: var(--spacing-md);\n  right: var(--spacing-md);\n  z-index: 9;\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n}\n</style>\n", "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass } from \"vue\"\n\nconst _hoisted_1 = { class: \"error-content\" }\nconst _hoisted_2 = {\n  key: 0,\n  class: \"error-icon\"\n}\nconst _hoisted_3 = { class: \"error-text\" }\nconst _hoisted_4 = {\n  key: 0,\n  class: \"error-title\"\n}\nconst _hoisted_5 = { class: \"error-description\" }\nconst _hoisted_6 = [\"disabled\"]\nconst _hoisted_7 = {\n  key: 0,\n  class: \"loading-spinner\"\n}\nconst _hoisted_8 = { key: 1 }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_ctx.show)\n    ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: _normalizeClass([\"error-message\", { 'error-message--inline': _ctx.inline }])\n      }, [\n        _createElementVNode(\"div\", _hoisted_1, [\n          (!_ctx.inline)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, _cache[2] || (_cache[2] = [\n                _createElementVNode(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  \"stroke-width\": \"2\",\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\"\n                }, [\n                  _createElementVNode(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\"\n                  }),\n                  _createElementVNode(\"line\", {\n                    x1: \"12\",\n                    y1: \"8\",\n                    x2: \"12\",\n                    y2: \"12\"\n                  }),\n                  _createElementVNode(\"line\", {\n                    x1: \"12\",\n                    y1: \"16\",\n                    x2: \"12.01\",\n                    y2: \"16\"\n                  })\n                ], -1)\n              ])))\n            : _createCommentVNode(\"\", true),\n          _createElementVNode(\"div\", _hoisted_3, [\n            (_ctx.title && !_ctx.inline)\n              ? (_openBlock(), _createElementBlock(\"h3\", _hoisted_4, _toDisplayString(_ctx.title), 1))\n              : _createCommentVNode(\"\", true),\n            _createElementVNode(\"p\", _hoisted_5, _toDisplayString(_ctx.message), 1)\n          ]),\n          (_ctx.showRetry)\n            ? (_openBlock(), _createElementBlock(\"button\", {\n                key: 1,\n                onClick: _cache[0] || (_cache[0] = ($event: any) => (_ctx.$emit('retry'))),\n                class: \"retry-button\",\n                disabled: _ctx.loading\n              }, [\n                (_ctx.loading)\n                  ? (_openBlock(), _createElementBlock(\"span\", _hoisted_7))\n                  : (_openBlock(), _createElementBlock(\"span\", _hoisted_8, _toDisplayString(_ctx.retryText), 1))\n              ], 8, _hoisted_6))\n            : _createCommentVNode(\"\", true),\n          (_ctx.showClose)\n            ? (_openBlock(), _createElementBlock(\"button\", {\n                key: 2,\n                onClick: _cache[1] || (_cache[1] = ($event: any) => (_ctx.$emit('close'))),\n                class: \"close-button\",\n                \"aria-label\": \"Закрыть\"\n              }, _cache[3] || (_cache[3] = [\n                _createElementVNode(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  \"stroke-width\": \"2\",\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\"\n                }, [\n                  _createElementVNode(\"line\", {\n                    x1: \"18\",\n                    y1: \"6\",\n                    x2: \"6\",\n                    y2: \"18\"\n                  }),\n                  _createElementVNode(\"line\", {\n                    x1: \"6\",\n                    y1: \"6\",\n                    x2: \"18\",\n                    y2: \"18\"\n                  })\n                ], -1)\n              ])))\n            : _createCommentVNode(\"\", true)\n        ])\n      ], 2))\n    : _createCommentVNode(\"\", true)\n}", "<template>\n  <div v-if=\"show\" class=\"error-message\" :class=\"{ 'error-message--inline': inline }\">\n    <div class=\"error-content\">\n      <div class=\"error-icon\" v-if=\"!inline\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n          <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n          <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\n        </svg>\n      </div>\n\n      <div class=\"error-text\">\n        <h3 v-if=\"title && !inline\" class=\"error-title\">{{ title }}</h3>\n        <p class=\"error-description\">{{ message }}</p>\n      </div>\n\n      <button\n        v-if=\"showRetry\"\n        @click=\"$emit('retry')\"\n        class=\"retry-button\"\n        :disabled=\"loading\"\n      >\n        <span v-if=\"loading\" class=\"loading-spinner\"></span>\n        <span v-else>{{ retryText }}</span>\n      </button>\n\n      <button\n        v-if=\"showClose\"\n        @click=\"$emit('close')\"\n        class=\"close-button\"\n        aria-label=\"Закрыть\"\n      >\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n          <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n        </svg>\n      </button>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from 'vue';\n\nexport default defineComponent({\n  name: 'ErrorMessage',\n  props: {\n    show: {\n      type: Boolean,\n      default: true\n    },\n    message: {\n      type: String,\n      required: true\n    },\n    title: {\n      type: String,\n      default: 'Ошибка'\n    },\n    inline: {\n      type: Boolean,\n      default: false\n    },\n    showRetry: {\n      type: Boolean,\n      default: false\n    },\n    showClose: {\n      type: Boolean,\n      default: false\n    },\n    retryText: {\n      type: String,\n      default: 'Повторить'\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['retry', 'close']\n});\n</script>\n\n<style scoped>\n.error-message {\n  padding: 1rem;\n  border-radius: 8px;\n  background-color: #fef2f2 !important;\n  border: 2px solid #ef4444 !important;\n  color: #dc2626 !important;\n  margin: 1rem 0;\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15) !important;\n}\n\n.error-message--inline {\n  padding: 0.75rem 1rem;\n  margin: 0.5rem 0;\n  background-color: #fef2f2 !important;\n  border: 2px solid #ef4444 !important;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15) !important;\n  color: #dc2626 !important;\n}\n\n.error-content {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  position: relative;\n}\n\n.error-icon {\n  flex-shrink: 0;\n  color: #dc2626;\n}\n\n.error-text {\n  flex: 1;\n}\n\n.error-title {\n  margin: 0 0 0.25rem 0;\n  font-size: 1rem;\n  font-weight: 600;\n  color: #dc2626;\n}\n\n.error-description {\n  margin: 0;\n  font-size: 0.875rem;\n  line-height: 1.4;\n  color: #991b1b;\n}\n\n.retry-button {\n  padding: 0.5rem 1rem;\n  background-color: #dc2626;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-top: 0.5rem;\n}\n\n.retry-button:hover:not(:disabled) {\n  background-color: #b91c1c;\n}\n\n.retry-button:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.close-button {\n  position: absolute;\n  top: -0.25rem;\n  right: -0.25rem;\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0.25rem;\n  border-radius: 4px;\n  transition: color 0.2s;\n}\n\n.close-button:hover {\n  color: #374151;\n}\n\n.loading-spinner {\n  width: 14px;\n  height: 14px;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Telegram theme support с автоматической поддержкой темной темы */\n.error-message {\n  background-color: var(--tg-theme-secondary-bg-color, #fef2f2) !important;\n  border-color: #ef4444 !important;\n  color: var(--tg-theme-text-color, #dc2626) !important;\n}\n\n.error-message--inline {\n  background-color: var(--tg-theme-secondary-bg-color, #fef2f2) !important;\n  border-color: #ef4444 !important;\n  color: var(--tg-theme-text-color, #dc2626) !important;\n}\n\n.error-title {\n  color: var(--tg-theme-text-color, #dc2626) !important;\n}\n\n.error-description {\n  color: var(--tg-theme-subtitle-text-color, #991b1b) !important;\n}\n\n.retry-button {\n  background-color: var(--tg-theme-button-color, #dc2626) !important;\n  color: var(--tg-theme-button-text-color, #ffffff) !important;\n}\n\n.retry-button:hover:not(:disabled) {\n  opacity: 0.9;\n}\n\n/* Fallback для темной темы когда Telegram переменные недоступны */\n@media (prefers-color-scheme: dark) {\n  .error-message {\n    background-color: var(--tg-theme-secondary-bg-color, #450a0a) !important;\n    color: var(--tg-theme-text-color, #fca5a5) !important;\n    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.25) !important;\n  }\n\n  .error-message--inline {\n    background-color: var(--tg-theme-secondary-bg-color, #450a0a) !important;\n    color: var(--tg-theme-text-color, #fca5a5) !important;\n    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.25) !important;\n  }\n\n  .error-description {\n    color: var(--tg-theme-subtitle-text-color, #f87171) !important;\n  }\n}\n</style>\n", "import { render } from \"./ErrorMessage.vue?vue&type=template&id=7b2fd338&scoped=true&ts=true\"\nimport script from \"./ErrorMessage.vue?vue&type=script&lang=ts\"\nexport * from \"./ErrorMessage.vue?vue&type=script&lang=ts\"\n\nimport \"./ErrorMessage.vue?vue&type=style&index=0&id=7b2fd338&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-7b2fd338\"]])\n\nexport default __exports__", "import { render } from \"./SearchBar.vue?vue&type=template&id=43d0478e&scoped=true&ts=true\"\nimport script from \"./SearchBar.vue?vue&type=script&lang=ts\"\nexport * from \"./SearchBar.vue?vue&type=script&lang=ts\"\n\nimport \"./SearchBar.vue?vue&type=style&index=0&id=43d0478e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-43d0478e\"]])\n\nexport default __exports__", "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, normalizeStyle as _normalizeStyle, normalizeClass as _normalizeClass } from \"vue\"\n\nconst _hoisted_1 = { class: \"banner-carousel\" }\nconst _hoisted_2 = {\n  class: \"carousel-container\",\n  ref: \"carouselContainer\"\n}\nconst _hoisted_3 = [\"onClick\"]\nconst _hoisted_4 = { class: \"slide-overlay\" }\nconst _hoisted_5 = { class: \"slide-title\" }\nconst _hoisted_6 = { class: \"slide-description\" }\nconst _hoisted_7 = { class: \"carousel-indicators\" }\nconst _hoisted_8 = [\"onClick\"]\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.slides, (slide, index) => {\n        return (_openBlock(), _createElementBlock(\"div\", {\n          key: index,\n          class: _normalizeClass([\"carousel-slide\", { active: _ctx.currentSlide === index }]),\n          style: _normalizeStyle([{ transform: `translateX(${100 * (index - _ctx.currentSlide)}%)` }, {\"cursor\":\"pointer\"}]),\n          onClick: ($event: any) => (_ctx.handleSlideClick(slide))\n        }, [\n          _createElementVNode(\"div\", {\n            class: \"slide-content\",\n            style: _normalizeStyle({ backgroundImage: `url(${slide.imageUrl})` })\n          }, [\n            _createElementVNode(\"div\", _hoisted_4, [\n              _createElementVNode(\"h2\", _hoisted_5, _toDisplayString(slide.title), 1),\n              _createElementVNode(\"p\", _hoisted_6, _toDisplayString(slide.description), 1)\n            ])\n          ], 4)\n        ], 14, _hoisted_3))\n      }), 128))\n    ], 512),\n    _createElementVNode(\"div\", _hoisted_7, [\n      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.slides, (slide, index) => {\n        return (_openBlock(), _createElementBlock(\"button\", {\n          key: index,\n          class: _normalizeClass([\"indicator-dot\", { active: _ctx.currentSlide === index }]),\n          onClick: ($event: any) => (_ctx.goToSlide(index))\n        }, null, 10, _hoisted_8))\n      }), 128))\n    ]),\n    _createElementVNode(\"button\", {\n      class: \"carousel-control prev\",\n      onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.prevSlide && _ctx.prevSlide(...args)))\n    }, _cache[2] || (_cache[2] = [\n      _createElementVNode(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        \"stroke-width\": \"2\",\n        \"stroke-linecap\": \"round\",\n        \"stroke-linejoin\": \"round\"\n      }, [\n        _createElementVNode(\"polyline\", { points: \"15 18 9 12 15 6\" })\n      ], -1)\n    ])),\n    _createElementVNode(\"button\", {\n      class: \"carousel-control next\",\n      onClick: _cache[1] || (_cache[1] = \n//@ts-ignore\n(...args) => (_ctx.nextSlide && _ctx.nextSlide(...args)))\n    }, _cache[3] || (_cache[3] = [\n      _createElementVNode(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        \"stroke-width\": \"2\",\n        \"stroke-linecap\": \"round\",\n        \"stroke-linejoin\": \"round\"\n      }, [\n        _createElementVNode(\"polyline\", { points: \"9 18 15 12 9 6\" })\n      ], -1)\n    ]))\n  ]))\n}", "<template>\n  <div class=\"banner-carousel\">\n    <div class=\"carousel-container\" ref=\"carouselContainer\">\n      <div\n        v-for=\"(slide, index) in slides\"\n        :key=\"index\"\n        class=\"carousel-slide\"\n        :class=\"{ active: currentSlide === index }\"\n        :style=\"{ transform: `translateX(${100 * (index - currentSlide)}%)` }\"\n        @click=\"handleSlideClick(slide)\"\n        style=\"cursor: pointer;\"\n      >\n        <div class=\"slide-content\" :style=\"{ backgroundImage: `url(${slide.imageUrl})` }\">\n          <div class=\"slide-overlay\">\n            <h2 class=\"slide-title\">{{ slide.title }}</h2>\n            <p class=\"slide-description\">{{ slide.description }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"carousel-indicators\">\n      <button\n        v-for=\"(slide, index) in slides\"\n        :key=\"index\"\n        class=\"indicator-dot\"\n        :class=\"{ active: currentSlide === index }\"\n        @click=\"goToSlide(index)\"\n      ></button>\n    </div>\n\n    <button class=\"carousel-control prev\" @click=\"prevSlide\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n        <polyline points=\"15 18 9 12 15 6\"></polyline>\n      </svg>\n    </button>\n\n    <button class=\"carousel-control next\" @click=\"nextSlide\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n        <polyline points=\"9 18 15 12 9 6\"></polyline>\n      </svg>\n    </button>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { MovieCategory } from '@/services/vibixService';\nimport { useMoviesStore } from '@/store/movies';\n\ninterface CarouselSlide {\n  title: string;\n  description: string;\n  imageUrl: string;\n  link?: string;\n  isAd?: boolean;\n}\n\nexport default defineComponent({\n  name: 'BannerCarousel',\n  setup() {\n    // Функция для выбора n случайных элементов из массива\n    function getRandomItems<T>(arr: T[], n: number): T[] {\n      const result: T[] = [];\n      const used = new Set<number>();\n      while (result.length < n && used.size < arr.length) {\n        const idx = Math.floor(Math.random() * arr.length);\n        if (!used.has(idx)) {\n          result.push(arr[idx]);\n          used.add(idx);\n        }\n      }\n      return result;\n    }\n\n    const slides = ref<CarouselSlide[]>([]);\n    const currentSlide = ref(0);\n    const carouselContainer = ref<HTMLElement | null>(null);\n    const autoplayInterval = ref<number | null>(null);\n    const loading = ref(true);\n    const error = ref<string | null>(null);\n    const moviesStore = useMoviesStore();\n    const router = useRouter();\n\n    // Load featured movies for carousel\n    const loadFeaturedMovies = async () => {\n      loading.value = true;\n      error.value = null;\n\n      try {\n        // Получаем популярные фильмы из хранилища (или загружаем, если их нет)\n        const movies = await moviesStore.getMoviesByCategory(MovieCategory.POPULAR, 1, 20);\n\n        // Фильтруем только фильмы с картинкой\n        const filtered = (movies || []).filter(movie => movie.poster_urls?.w500);\n\n        // Берём 3 случайных фильма\n        const randomMovies = getRandomItems(filtered, 3);\n\n        // Формируем слайды\n        const movieSlides: CarouselSlide[] = randomMovies.map(movie => ({\n          title: movie.name_rus || movie.name_original || '',\n          description: movie.description_short || `${movie.type === 'serial' ? 'Сериал' : 'Фильм'} ${movie.year}`,\n          imageUrl: movie.poster_urls?.w500 || '',\n          link: `/movie/${movie.kp_id ? 'kp' : 'imdb'}/${movie.kp_id || movie.imdb_id}`\n        }));\n\n        // Add advertisement slide\n        const adSlide: CarouselSlide = {\n          title: 'Реклама',\n          description: 'Специальное предложение для наших пользователей',\n          imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',\n          isAd: true\n        };\n\n        // Вставляем рекламу на вторую позицию (index 1)\n        if (movieSlides.length >= 2) {\n          movieSlides.splice(1, 0, adSlide);\n        } else {\n          movieSlides.push(adSlide);\n        }\n\n        slides.value = movieSlides;\n      } catch (err) {\n        console.error('Error loading featured movies for carousel:', err);\n        error.value = 'Не удалось загрузить фильмы для карусели';\n\n        // Use placeholder slides if API fails\n        slides.value = [\n          {\n            title: 'Популярные фильмы',\n            description: 'Смотрите лучшие фильмы и сериалы',\n            imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkmM9QDwADgQF/Vry1FAAAAABJRU5ErkJggg=='\n          },\n          {\n            title: 'Новинки',\n            description: 'Самые свежие релизы',\n            imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkWM9QDwADCAGEFYi4zQAAAABJRU5ErkJggg=='\n          },\n          {\n            title: 'Реклама',\n            description: 'Специальное предложение для наших пользователей',\n            imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',\n            isAd: true\n          },\n          {\n            title: 'Эксклюзивы',\n            description: 'Только у нас',\n            imageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPivDwADCQGAjPbM3QAAAABJRU5ErkJggg=='\n          }\n        ];\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // Navigation functions\n    const nextSlide = () => {\n      currentSlide.value = (currentSlide.value + 1) % slides.value.length;\n      resetAutoplay();\n    };\n\n    const prevSlide = () => {\n      currentSlide.value = (currentSlide.value - 1 + slides.value.length) % slides.value.length;\n      resetAutoplay();\n    };\n\n    const goToSlide = (index: number) => {\n      currentSlide.value = index;\n      resetAutoplay();\n    };\n\n    // Autoplay functions\n    const startAutoplay = () => {\n      stopAutoplay();\n      autoplayInterval.value = window.setInterval(() => {\n        nextSlide();\n      }, 5000);\n    };\n\n    const stopAutoplay = () => {\n      if (autoplayInterval.value !== null) {\n        clearInterval(autoplayInterval.value);\n        autoplayInterval.value = null;\n      }\n    };\n\n    const resetAutoplay = () => {\n      stopAutoplay();\n      startAutoplay();\n    };\n\n    // Touch handling\n    let touchStartX = 0;\n    let touchEndX = 0;\n\n    const handleTouchStart = (e: TouchEvent) => {\n      touchStartX = e.changedTouches[0].screenX;\n    };\n\n    const handleTouchEnd = (e: TouchEvent) => {\n      touchEndX = e.changedTouches[0].screenX;\n      handleSwipe();\n    };\n\n    const handleSwipe = () => {\n      const swipeThreshold = 50;\n      if (touchEndX < touchStartX - swipeThreshold) {\n        nextSlide(); // Swipe left\n      } else if (touchEndX > touchStartX + swipeThreshold) {\n        prevSlide(); // Swipe right\n      }\n    };\n\n    // Переход по клику на слайд (только если это не реклама)\n    const handleSlideClick = (slide: CarouselSlide) => {\n      if (slide.isAd) return;\n      if (slide.link) {\n        router.push(slide.link);\n      }\n    };\n\n    onMounted(() => {\n      loadFeaturedMovies();\n      startAutoplay();\n\n      // Add touch event listeners\n      if (carouselContainer.value) {\n        carouselContainer.value.addEventListener('touchstart', handleTouchStart, { passive: true });\n        carouselContainer.value.addEventListener('touchend', handleTouchEnd, { passive: true });\n      }\n    });\n\n    onBeforeUnmount(() => {\n      stopAutoplay();\n\n      // Remove touch event listeners\n      if (carouselContainer.value) {\n        carouselContainer.value.removeEventListener('touchstart', handleTouchStart);\n        carouselContainer.value.removeEventListener('touchend', handleTouchEnd);\n      }\n    });\n\n    return {\n      slides,\n      currentSlide,\n      carouselContainer,\n      nextSlide,\n      prevSlide,\n      goToSlide,\n      handleSlideClick\n    };\n  }\n});\n</script>\n\n<style scoped>\n.banner-carousel {\n  position: relative;\n  width: 100%;\n  height: 250px;\n  overflow: hidden;\n  margin-bottom: 20px;\n  border-radius: 12px;\n}\n\n.carousel-container {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n.carousel-slide {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  transition: transform 0.5s ease;\n}\n\n.slide-content {\n  width: 100%;\n  height: 100%;\n  background-size: cover;\n  background-position: center;\n  position: relative;\n}\n\n.slide-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 20px;\n  background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);\n  color: white;\n}\n\n.slide-title {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0 0 8px 0;\n}\n\n.slide-description {\n  font-size: 14px;\n  margin: 0;\n  opacity: 0.8;\n}\n\n.carousel-indicators {\n  position: absolute;\n  bottom: 10px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  gap: 8px;\n}\n\n.indicator-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: rgba(255, 255, 255, 0.5);\n  border: none;\n  padding: 0;\n  cursor: pointer;\n}\n\n.indicator-dot.active {\n  background-color: white;\n}\n\n.carousel-control {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: white;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 2;\n}\n\n.carousel-control.prev {\n  left: 10px;\n}\n\n.carousel-control.next {\n  right: 10px;\n}\n\n.carousel-control svg {\n  width: 20px;\n  height: 20px;\n}\n</style>\n", "import { render } from \"./BannerCarousel.vue?vue&type=template&id=3f9111b2&scoped=true&ts=true\"\nimport script from \"./BannerCarousel.vue?vue&type=script&lang=ts\"\nexport * from \"./BannerCarousel.vue?vue&type=script&lang=ts\"\n\nimport \"./BannerCarousel.vue?vue&type=style&index=0&id=3f9111b2&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-3f9111b2\"]])\n\nexport default __exports__", "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, resolveComponent as _resolveComponent, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = {\n  class: \"movie-row\",\n  ref: \"rowRef\"\n}\nconst _hoisted_2 = { class: \"category-title\" }\nconst _hoisted_3 = { class: \"movies-container\" }\nconst _hoisted_4 = {\n  class: \"movies-scroll\",\n  ref: \"scrollContainer\"\n}\nconst _hoisted_5 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_6 = {\n  key: 1,\n  class: \"error-container\"\n}\nconst _hoisted_7 = {\n  key: 2,\n  class: \"empty-container\"\n}\nconst _hoisted_8 = {\n  key: 3,\n  class: \"movies-list\"\n}\nconst _hoisted_9 = {\n  key: 0,\n  class: \"next-page-button-container\"\n}\nconst _hoisted_10 = [\"disabled\"]\nconst _hoisted_11 = {\n  key: 0,\n  class: \"loading-spinner small\"\n}\nconst _hoisted_12 = { key: 1 }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_MovieTile = _resolveComponent(\"MovieTile\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"h2\", _hoisted_2, _toDisplayString(_ctx.title), 1),\n    _createElementVNode(\"div\", _hoisted_3, [\n      (_ctx.canScrollLeft && _ctx.showScrollButtons)\n        ? (_openBlock(), _createElementBlock(\"button\", {\n            key: 0,\n            class: \"scroll-button left\",\n            onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.scrollLeft && _ctx.scrollLeft(...args)))\n          }, _cache[4] || (_cache[4] = [\n            _createElementVNode(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              \"stroke-width\": \"2\",\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\"\n            }, [\n              _createElementVNode(\"polyline\", { points: \"15 18 9 12 15 6\" })\n            ], -1)\n          ])))\n        : _createCommentVNode(\"\", true),\n      _createElementVNode(\"div\", _hoisted_4, [\n        (_ctx.loading)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _cache[5] || (_cache[5] = [\n              _createElementVNode(\"div\", { class: \"loading-spinner\" }, null, -1)\n            ])))\n          : (_ctx.error)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n                _createElementVNode(\"p\", null, _toDisplayString(_ctx.error), 1),\n                _createElementVNode(\"button\", {\n                  onClick: _cache[1] || (_cache[1] = \n//@ts-ignore\n(...args) => (_ctx.loadMovies && _ctx.loadMovies(...args))),\n                  class: \"retry-button\"\n                }, \"Повторить\")\n              ]))\n            : (_ctx.movies.length === 0)\n              ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, _cache[6] || (_cache[6] = [\n                  _createElementVNode(\"p\", null, \"Фильмы не найдены\", -1)\n                ])))\n              : (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n                  (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.movies, (movie) => {\n                    return (_openBlock(), _createBlock(_component_MovieTile, {\n                      key: movie.id,\n                      movie: movie,\n                      \"is-exclusive\": _ctx.isExclusive(movie)\n                    }, null, 8, [\"movie\", \"is-exclusive\"]))\n                  }), 128)),\n                  (_ctx.hasMorePages)\n                    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [\n                        _createElementVNode(\"button\", {\n                          class: \"next-page-button\",\n                          onClick: _cache[2] || (_cache[2] = \n//@ts-ignore\n(...args) => (_ctx.loadNextPage && _ctx.loadNextPage(...args))),\n                          disabled: _ctx.loadingNextPage\n                        }, [\n                          (_ctx.loadingNextPage)\n                            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11))\n                            : (_openBlock(), _createElementBlock(\"span\", _hoisted_12, \"Ещё\"))\n                        ], 8, _hoisted_10)\n                      ]))\n                    : _createCommentVNode(\"\", true)\n                ]))\n      ], 512),\n      (_ctx.canScrollRight && _ctx.showScrollButtons)\n        ? (_openBlock(), _createElementBlock(\"button\", {\n            key: 1,\n            class: \"scroll-button right\",\n            onClick: _cache[3] || (_cache[3] = \n//@ts-ignore\n(...args) => (_ctx.scrollRight && _ctx.scrollRight(...args)))\n          }, _cache[7] || (_cache[7] = [\n            _createElementVNode(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              \"stroke-width\": \"2\",\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\"\n            }, [\n              _createElementVNode(\"polyline\", { points: \"9 18 15 12 9 6\" })\n            ], -1)\n          ])))\n        : _createCommentVNode(\"\", true)\n    ])\n  ], 512))\n}", "<template>\n  <div class=\"movie-row\" ref=\"rowRef\">\n    <h2 class=\"category-title\">{{ title }}</h2>\n\n    <div class=\"movies-container\">\n      <button v-if=\"canScrollLeft && showScrollButtons\" class=\"scroll-button left\" @click=\"scrollLeft\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <polyline points=\"15 18 9 12 15 6\"></polyline>\n        </svg>\n      </button>\n\n      <div class=\"movies-scroll\" ref=\"scrollContainer\">\n        <div v-if=\"loading\" class=\"loading-container\">\n          <div class=\"loading-spinner\"></div>\n        </div>\n\n        <div v-else-if=\"error\" class=\"error-container\">\n          <p>{{ error }}</p>\n          <button @click=\"loadMovies\" class=\"retry-button\">Повторить</button>\n        </div>\n\n        <div v-else-if=\"movies.length === 0\" class=\"empty-container\">\n          <p>Фильмы не найдены</p>\n        </div>\n\n        <div v-else class=\"movies-list\">\n          <MovieTile\n            v-for=\"movie in movies\"\n            :key=\"movie.id\"\n            :movie=\"movie\"\n            :is-exclusive=\"isExclusive(movie)\"\n          />\n\n          <!-- Кнопка \"Следующая страница\" -->\n          <div v-if=\"hasMorePages\" class=\"next-page-button-container\">\n            <button\n              class=\"next-page-button\"\n              @click=\"loadNextPage\"\n              :disabled=\"loadingNextPage\"\n            >\n              <div v-if=\"loadingNextPage\" class=\"loading-spinner small\"></div>\n              <span v-else>Ещё</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <button v-if=\"canScrollRight && showScrollButtons\" class=\"scroll-button right\" @click=\"scrollRight\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <polyline points=\"9 18 15 12 9 6\"></polyline>\n        </svg>\n      </button>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted, onBeforeUnmount, PropType } from 'vue';\nimport MovieTile from './MovieTile.vue';\nimport { Movie, MovieCategory } from '@/services/vibixService';\nimport { useMoviesStore } from '@/store/movies';\n\n\nexport default defineComponent({\n  name: 'MovieRow',\n  components: {\n    MovieTile\n  },\n  props: {\n    title: {\n      type: String,\n      required: true\n    },\n    category: {\n      type: String as PropType<MovieCategory>,\n      required: true\n    }\n  },\n  setup(props) {\n    const movies = ref<Movie[]>([]);\n    const loading = ref(true);\n    const error = ref<string | null>(null);\n    const scrollContainer = ref<HTMLElement | null>(null);\n    const canScrollLeft = ref(false);\n    const canScrollRight = ref(true); // По умолчанию показываем правую стрелку\n    const moviesStore = useMoviesStore();\n\n    // Показываем кнопки прокрутки по умолчанию\n    const showScrollButtons = ref(true);\n\n    // Состояние для пагинации\n    const currentPage = ref(1);\n    const hasMorePages = ref(true);\n    const loadingNextPage = ref(false);\n\n    // Load movies for the category\n    const loadMovies = async () => {\n      loading.value = true;\n      error.value = null;\n\n      // Сбрасываем состояние пагинации\n      currentPage.value = 1;\n      hasMorePages.value = true;\n\n      try {\n        // Используем хранилище для получения фильмов (первая страница)\n        const categoryMovies = await moviesStore.getMoviesByCategory(\n          props.category as MovieCategory,\n          currentPage.value\n        );\n        movies.value = categoryMovies;\n\n\n\n        // Проверяем, есть ли еще страницы (предполагаем, что если получили меньше 20 фильмов, то больше нет)\n        hasMorePages.value = categoryMovies.length >= 20;\n      } catch (err) {\n        error.value = 'Не удалось загрузить фильмы';\n      } finally {\n        loading.value = false;\n        // Обновляем кнопки прокрутки с небольшой задержкой для корректного рендеринга\n        setTimeout(() => {\n          updateScrollButtons();\n        }, 100);\n      }\n    };\n\n    // Check if movie should be marked as exclusive\n    const isExclusive = (movie: Movie) => {\n      // For demo purposes, mark every 5th movie as exclusive\n      return movie.id.length > 0 && parseInt(movie.id) % 5 === 0;\n    };\n\n    // Scroll functions\n    const updateScrollButtons = () => {\n      if (!scrollContainer.value) return;\n\n      const { scrollLeft, scrollWidth, clientWidth } = scrollContainer.value;\n      canScrollLeft.value = scrollLeft > 0;\n      canScrollRight.value = scrollLeft < scrollWidth - clientWidth - 10; // 10px buffer\n\n      // Если контент помещается в контейнер, скрываем правую стрелку\n      if (scrollWidth <= clientWidth) {\n        canScrollRight.value = false;\n      }\n    };\n\n    const scrollLeft = () => {\n      if (!scrollContainer.value) return;\n\n      const scrollAmount = scrollContainer.value.clientWidth * 0.8;\n      scrollContainer.value.scrollBy({\n        left: -scrollAmount,\n        behavior: 'smooth'\n      });\n    };\n\n    const scrollRight = () => {\n      if (!scrollContainer.value) return;\n\n      const scrollAmount = scrollContainer.value.clientWidth * 0.8;\n      scrollContainer.value.scrollBy({\n        left: scrollAmount,\n        behavior: 'smooth'\n      });\n    };\n\n    // Watch for scroll events\n    const handleScroll = () => {\n      updateScrollButtons();\n    };\n\n    // Watch for window resize to update scroll buttons\n    const handleResize = () => {\n      updateScrollButtons();\n    };\n\n    // Use Intersection Observer to load movies only when the row becomes visible\n    const rowRef = ref<HTMLElement | null>(null);\n    const isVisible = ref(false);\n\n    onMounted(() => {\n      // Создаем Intersection Observer для ленивой загрузки\n      const observer = new IntersectionObserver((entries) => {\n        const entry = entries[0];\n        if (entry.isIntersecting && !isVisible.value) {\n          isVisible.value = true;\n          loadMovies();\n\n          // После загрузки отключаем observer\n          if (rowRef.value) {\n            observer.unobserve(rowRef.value);\n          }\n        }\n      }, {\n        rootMargin: '200px', // Load a bit before it becomes visible\n        threshold: 0.1\n      });\n\n      // Observe the row element\n      if (rowRef.value) {\n        observer.observe(rowRef.value);\n      }\n\n      // Set up scroll event listener\n      if (scrollContainer.value) {\n        scrollContainer.value.addEventListener('scroll', handleScroll);\n        // Обновляем кнопки прокрутки с задержкой для корректного рендеринга\n        setTimeout(() => {\n          updateScrollButtons();\n        }, 200);\n      }\n\n      // Set up window resize listener\n      window.addEventListener('resize', handleResize);\n\n      // Clean up observer on unmount\n      onBeforeUnmount(() => {\n        if (rowRef.value) {\n          observer.unobserve(rowRef.value);\n        }\n      });\n    });\n\n    // Загрузка следующей страницы фильмов\n    const loadNextPage = async () => {\n      if (!hasMorePages.value || loadingNextPage.value) return;\n\n      loadingNextPage.value = true;\n\n      try {\n        // Увеличиваем номер страницы\n        currentPage.value++;\n\n        // Загружаем следующую страницу\n        const nextPageMovies = await moviesStore.getMoviesByCategory(\n          props.category as MovieCategory,\n          currentPage.value,\n          20,\n          false // Параметр, указывающий не использовать кэш\n        );\n\n        // Добавляем новые фильмы к существующим\n        if (nextPageMovies.length > 0) {\n          movies.value = [...movies.value, ...nextPageMovies];\n        }\n\n        // Проверяем, есть ли еще страницы\n        hasMorePages.value = nextPageMovies.length >= 20;\n      } catch (err) {\n        // В случае ошибки возвращаем номер страницы назад\n        currentPage.value--;\n      } finally {\n        loadingNextPage.value = false;\n        // Обновляем состояние кнопок прокрутки с небольшой задержкой\n        setTimeout(() => {\n          updateScrollButtons();\n        }, 100);\n      }\n    };\n\n    // Clean up event listeners\n    onBeforeUnmount(() => {\n      if (scrollContainer.value) {\n        scrollContainer.value.removeEventListener('scroll', handleScroll);\n      }\n      window.removeEventListener('resize', handleResize);\n    });\n\n    return {\n      movies,\n      loading,\n      error,\n      scrollContainer,\n      rowRef,\n      isVisible,\n      canScrollLeft,\n      canScrollRight,\n      showScrollButtons,\n      loadMovies,\n      isExclusive,\n      scrollLeft,\n      scrollRight,\n      loadNextPage,\n      hasMorePages,\n      loadingNextPage,\n      currentPage\n    };\n  }\n});\n</script>\n\n<style scoped>\n.movie-row {\n  margin-bottom: 30px;\n}\n\n.category-title {\n  font-size: 20px;\n  font-weight: bold;\n  margin-bottom: 16px;\n  padding-left: 16px;\n}\n\n.movies-container {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.movies-scroll {\n  flex: 1;\n  overflow-x: auto;\n  overflow-y: hidden;\n  white-space: nowrap;\n  padding: 0 16px;\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n  scroll-behavior: smooth;\n}\n\n.movies-scroll::-webkit-scrollbar {\n  display: none; /* Chrome, Safari, Opera */\n}\n\n.movies-list {\n  display: inline-flex;\n  padding: 4px 0;\n}\n\n.scroll-button {\n  position: absolute;\n  z-index: 2;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  box-shadow: var(--shadow-md);\n}\n\n.scroll-button.left {\n  left: 8px;\n}\n\n.scroll-button.right {\n  right: 8px;\n}\n\n.scroll-button svg {\n  width: 20px;\n  height: 20px;\n}\n\n/* Временно отключаем CSS медиа-запросы для отладки */\n/*\n@media (max-width: 768px) {\n  .scroll-button {\n    display: none !important;\n  }\n}\n\n@media (pointer: coarse) {\n  .scroll-button {\n    display: none !important;\n  }\n}\n*/\n\n.loading-container, .error-container, .empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  width: 100%;\n  color: var(--tg-theme-hint-color);\n}\n\n.loading-spinner {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  border: 2px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top-color: var(--tg-theme-button-color);\n  animation: spin 1s ease-in-out infinite;\n}\n\n.retry-button {\n  margin-top: 10px;\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  border: none;\n  border-radius: var(--border-radius);\n  padding: 8px 16px;\n  cursor: pointer;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n.next-page-button-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 140px;\n  height: 200px;\n  margin-right: 10px;\n}\n\n.next-page-button {\n  width: 100px;\n  height: 40px;\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  border: none;\n  border-radius: var(--border-radius, 8px);\n  font-size: 14px;\n  font-weight: bold;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.next-page-button:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.loading-spinner.small {\n  width: 16px;\n  height: 16px;\n  border-width: 2px;\n}\n</style>\n", "import { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"movie-poster\" }\nconst _hoisted_2 = [\"src\", \"alt\"]\nconst _hoisted_3 = {\n  key: 1,\n  class: \"poster-placeholder\"\n}\nconst _hoisted_4 = {\n  key: 2,\n  class: \"exclusive-badge\"\n}\nconst _hoisted_5 = {\n  key: 3,\n  class: \"movie-rating\"\n}\nconst _hoisted_6 = {\n  key: 4,\n  class: \"movie-type\"\n}\nconst _hoisted_7 = { class: \"movie-info\" }\nconst _hoisted_8 = { class: \"movie-title\" }\nconst _hoisted_9 = { class: \"movie-subtitle\" }\nconst _hoisted_10 = [\"fill\"]\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", {\n    class: \"movie-tile\",\n    onClick: _cache[1] || (_cache[1] = \n//@ts-ignore\n(...args) => (_ctx.navigateToMovie && _ctx.navigateToMovie(...args)))\n  }, [\n    _createElementVNode(\"div\", _hoisted_1, [\n      (_ctx.posterUrl)\n        ? (_openBlock(), _createElementBlock(\"img\", {\n            key: 0,\n            src: _ctx.posterUrl,\n            alt: _ctx.movie.name_rus,\n            class: \"poster-image\"\n          }, null, 8, _hoisted_2))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[2] || (_cache[2] = [\n            _createStaticVNode(\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"1\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" data-v-c5191ef2><rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"20\\\" rx=\\\"2.18\\\" ry=\\\"2.18\\\" data-v-c5191ef2></rect><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\" data-v-c5191ef2></circle><line x1=\\\"8\\\" y1=\\\"4\\\" x2=\\\"16\\\" y2=\\\"4\\\" data-v-c5191ef2></line><line x1=\\\"4\\\" y1=\\\"8\\\" x2=\\\"4\\\" y2=\\\"16\\\" data-v-c5191ef2></line><line x1=\\\"20\\\" y1=\\\"8\\\" x2=\\\"20\\\" y2=\\\"16\\\" data-v-c5191ef2></line><line x1=\\\"8\\\" y1=\\\"20\\\" x2=\\\"16\\\" y2=\\\"20\\\" data-v-c5191ef2></line></svg>\", 1)\n          ]))),\n      (_ctx.isExclusive)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \"эксклюзив\"))\n        : _createCommentVNode(\"\", true),\n      (_ctx.movie.kp_rating || _ctx.movie.imdb_rating)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _toDisplayString(_ctx.movie.kp_rating || _ctx.movie.imdb_rating), 1))\n        : _createCommentVNode(\"\", true),\n      (_ctx.movie.type === 'serial')\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, \"сериал\"))\n        : _createCommentVNode(\"\", true)\n    ]),\n    _createElementVNode(\"div\", _hoisted_7, [\n      _createElementVNode(\"h3\", _hoisted_8, _toDisplayString(_ctx.movie.name_rus || _ctx.movie.name_original), 1),\n      _createElementVNode(\"p\", _hoisted_9, _toDisplayString(_ctx.getSubtitle), 1)\n    ]),\n    _createElementVNode(\"button\", {\n      class: \"bookmark-button\",\n      onClick: _cache[0] || (_cache[0] = _withModifiers(\n//@ts-ignore\n(...args) => (_ctx.toggleBookmark && _ctx.toggleBookmark(...args)), [\"stop\"]))\n    }, [\n      (_openBlock(), _createElementBlock(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: _ctx.isBookmarked ? 'currentColor' : 'none',\n        stroke: \"currentColor\",\n        \"stroke-width\": \"2\",\n        \"stroke-linecap\": \"round\",\n        \"stroke-linejoin\": \"round\"\n      }, _cache[3] || (_cache[3] = [\n        _createElementVNode(\"path\", { d: \"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\" }, null, -1)\n      ]), 8, _hoisted_10))\n    ])\n  ]))\n}", "<template>\n  <div class=\"movie-tile\" @click=\"navigateToMovie\">\n    <div class=\"movie-poster\">\n      <img v-if=\"posterUrl\" :src=\"posterUrl\" :alt=\"movie.name_rus\" class=\"poster-image\">\n      <div v-else class=\"poster-placeholder\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" rx=\"2.18\" ry=\"2.18\"></rect>\n          <circle cx=\"12\" cy=\"12\" r=\"4\"></circle>\n          <line x1=\"8\" y1=\"4\" x2=\"16\" y2=\"4\"></line>\n          <line x1=\"4\" y1=\"8\" x2=\"4\" y2=\"16\"></line>\n          <line x1=\"20\" y1=\"8\" x2=\"20\" y2=\"16\"></line>\n          <line x1=\"8\" y1=\"20\" x2=\"16\" y2=\"20\"></line>\n        </svg>\n      </div>\n\n      <div v-if=\"isExclusive\" class=\"exclusive-badge\">эксклюзив</div>\n      <div v-if=\"movie.kp_rating || movie.imdb_rating\" class=\"movie-rating\">\n        {{ movie.kp_rating || movie.imdb_rating }}\n      </div>\n      <div v-if=\"movie.type === 'serial'\" class=\"movie-type\">сериал</div>\n    </div>\n\n    <div class=\"movie-info\">\n      <h3 class=\"movie-title\">{{ movie.name_rus || movie.name_original }}</h3>\n      <p class=\"movie-subtitle\">{{ getSubtitle }}</p>\n    </div>\n\n    <button class=\"bookmark-button\" @click.stop=\"toggleBookmark\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" :fill=\"isBookmarked ? 'currentColor' : 'none'\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n        <path d=\"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\"></path>\n      </svg>\n    </button>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, computed, ref, PropType } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { useBookmarksStore } from '@/store/bookmarks';\nimport { Movie } from '@/services/vibixService';\nimport { getMovieSource, getMovieId } from '@/utils/movieUtils';\n\nexport default defineComponent({\n  name: 'MovieTile',\n  props: {\n    movie: {\n      type: Object as PropType<Movie>,\n      required: true\n    },\n    isExclusive: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props) {\n    const router = useRouter();\n    const bookmarksStore = useBookmarksStore();\n    const loading = ref(false);\n\n    // Determine source and ID for the movie using utility functions\n    const source = computed(() => getMovieSource(props.movie));\n    const movieId = computed(() => getMovieId(props.movie));\n\n    // Check if movie is bookmarked\n    const isBookmarked = computed(() => {\n      if (!source.value || !movieId.value) return false;\n      return bookmarksStore.isBookmarked(movieId.value, source.value);\n    });\n\n    // Get best poster URL (TMDB first, then fallback)\n    const posterUrl = computed(() => {\n      // Приоритет: TMDB w500 > оригинальный poster_url > null\n      if (props.movie.poster_urls?.w500) {\n        return props.movie.poster_urls.w500;\n      }\n      return props.movie.poster_url || null;\n    });\n\n    // Get subtitle (year or \"Бесплатно\")\n    const getSubtitle = computed(() => {\n      return props.movie.year ? `${props.movie.year}` : 'Бесплатно';\n    });\n\n    // Navigate to movie details\n    const navigateToMovie = () => {\n      if (source.value && movieId.value) {\n        router.push(`/movie/${source.value}/${movieId.value}`);\n      }\n    };\n\n    // Toggle bookmark\n    const toggleBookmark = async (event: Event) => {\n      event.stopPropagation();\n\n      if (!source.value || !movieId.value) return;\n\n      loading.value = true;\n\n      try {\n        if (isBookmarked.value) {\n          const bookmarkId = bookmarksStore.getBookmarkId(movieId.value, source.value);\n          if (bookmarkId) {\n            await bookmarksStore.deleteBookmark(bookmarkId);\n          }\n        } else {\n          await bookmarksStore.addBookmark(movieId.value, source.value);\n        }\n      } catch (error) {\n        console.error('Error toggling bookmark:', error);\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    return {\n      source,\n      movieId,\n      isBookmarked,\n      posterUrl,\n      getSubtitle,\n      navigateToMovie,\n      toggleBookmark,\n      loading\n    };\n  }\n});\n</script>\n\n<style scoped>\n.movie-tile {\n  position: relative;\n  width: 140px;\n  min-width: 140px;\n  margin-right: 10px;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.movie-tile:hover {\n  transform: scale(1.05);\n}\n\n.movie-poster {\n  position: relative;\n  width: 100%;\n  height: 200px;\n  border-radius: 8px;\n  overflow: hidden;\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.poster-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.poster-placeholder {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  color: var(--tg-theme-hint-color);\n}\n\n.exclusive-badge {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  background-color: #ff5722;\n  color: white;\n  font-size: 10px;\n  font-weight: bold;\n  text-transform: uppercase;\n  padding: 3px 6px;\n  border-radius: 4px;\n}\n\n.movie-rating {\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  background-color: rgba(255, 193, 7, 0.9);\n  color: black;\n  font-size: 12px;\n  font-weight: bold;\n  padding: 2px 6px;\n  border-radius: 4px;\n}\n\n.movie-type {\n  position: absolute;\n  bottom: 8px;\n  left: 8px;\n  background-color: rgba(0, 0, 0, 0.7);\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 4px;\n}\n\n.movie-info {\n  padding: 8px 0;\n}\n\n.movie-title {\n  font-size: 14px;\n  font-weight: bold;\n  margin: 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.movie-subtitle {\n  font-size: 12px;\n  color: var(--tg-theme-hint-color);\n  margin: 4px 0 0 0;\n}\n\n.bookmark-button {\n  position: absolute;\n  top: 8px;\n  left: 8px;\n  background: rgba(0, 0, 0, 0.5);\n  border: none;\n  border-radius: 50%;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  padding: 0;\n  cursor: pointer;\n}\n\n.bookmark-button svg {\n  width: 16px;\n  height: 16px;\n}\n</style>\n", "import { render } from \"./MovieTile.vue?vue&type=template&id=c5191ef2&scoped=true&ts=true\"\nimport script from \"./MovieTile.vue?vue&type=script&lang=ts\"\nexport * from \"./MovieTile.vue?vue&type=script&lang=ts\"\n\nimport \"./MovieTile.vue?vue&type=style&index=0&id=c5191ef2&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-c5191ef2\"]])\n\nexport default __exports__", "import { render } from \"./MovieRow.vue?vue&type=template&id=2848cd6b&scoped=true&ts=true\"\nimport script from \"./MovieRow.vue?vue&type=script&lang=ts\"\nexport * from \"./MovieRow.vue?vue&type=script&lang=ts\"\n\nimport \"./MovieRow.vue?vue&type=style&index=0&id=2848cd6b&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-2848cd6b\"]])\n\nexport default __exports__", "import { render } from \"./CatalogView.vue?vue&type=template&id=58f37f06&scoped=true&ts=true\"\nimport script from \"./CatalogView.vue?vue&type=script&lang=ts\"\nexport * from \"./CatalogView.vue?vue&type=script&lang=ts\"\n\nimport \"./CatalogView.vue?vue&type=style&index=0&id=58f37f06&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-58f37f06\"]])\n\nexport default __exports__", "import api from './api';\n\n// Define movie interface based on Vibix API response\nexport interface Movie {\n  id: string;\n  name_rus: string;\n  name_original: string;\n  iframe_url: string;\n  type: string;\n  year: number;\n  kp_id?: string;\n  imdb_id?: string;\n  kp_rating?: number;\n  imdb_rating?: number;\n  description_short?: string;\n  poster_url?: string;\n  // Новые поля от TMDB кеширования\n  poster_urls?: {\n    w500: string;\n    original: string;\n  };\n  has_poster?: boolean;\n}\n\n// Define pagination interface\nexport interface PaginationLinks {\n  first: string;\n  last: string;\n  prev: string | null;\n  next: string | null;\n}\n\n// Define Vibix API response interface\nexport interface VibixResponse {\n  data: Movie[];\n  links: PaginationLinks;\n  meta: {\n    current_page: number;\n    from: number;\n    last_page: number;\n    path: string;\n    per_page: number;\n    to: number;\n    total: number;\n  };\n  success: boolean;\n  message: string;\n  status: number;\n}\n\n// Define movie categories\nexport enum MovieCategory {\n  POPULAR = 'popular',\n  NEW = 'new',\n  THRILLER = 'thriller',\n  DRAMA = 'drama',\n  ACTION = 'action',\n  SCIFI = 'scifi',\n  DETECTIVE = 'detective'\n}\n\nclass VibixService {\n  private baseUrl: string;\n\n  constructor() {\n    // Используем полный URL бэкенда для запросов\n    this.baseUrl = 'https://v2test.appkinobot.com/api/vibix';\n  }\n\n  // Get movies by category\n  async getMoviesByCategory(category: MovieCategory, page: number = 1, limit: number = 20): Promise<VibixResponse> {\n    // Используем правильный эндпоинт для бэкенда\n    const response = await api.get(`${this.baseUrl}/movies/category/${category}`, {\n      params: {\n        limit,\n        page\n      }\n    });\n    return response.data;\n  }\n\n  // Get movie by ID\n  async getMovie(id: string): Promise<Movie> {\n    const response = await api.get(`${this.baseUrl}/movies/${id}`);\n    return response.data;\n  }\n\n  // Search movies by URL\n  async searchByUrl(url: string): Promise<Movie | null> {\n    const response = await api.get(`${this.baseUrl}/search`, {\n      params: { url }\n    });\n    return response.data;\n  }\n}\n\nexport default new VibixService();\n", "import { defineStore } from 'pinia';\nimport api from '@/services/api';\nimport vibixService, { Movie as VibixMovie, MovieCategory } from '@/services/vibixService';\n\ninterface Movie {\n  id: string;\n  name_rus: string;\n  name_original: string;\n  iframe_url: string;\n  type: string;\n  year: number;\n  kp_id?: string;\n  imdb_id?: string;\n  kp_rating?: number;\n  imdb_rating?: number;\n  description_short?: string;\n}\n\nexport const useMoviesStore = defineStore('movies', {\n  state: () => ({\n    currentMovie: null as Movie | null,\n    loading: false,\n    error: null as string | null,\n    // Добавляем хранилище для категорий фильмов\n    categoryMovies: {} as Record<MovieCategory, VibixMovie[]>,\n    categoryLoading: {} as Record<MovieCategory, boolean>,\n    categoryError: {} as Record<MovieCategory, string | null>,\n    // Добавляем отслеживание запросов в процессе выполнения\n    pendingRequests: {} as Record<string, Promise<VibixMovie[]> | null>\n  }),\n\n  actions: {\n    async fetchMovieById(source: string, id: string) {\n      this.loading = true;\n      this.error = null;\n      this.currentMovie = null;\n\n      // Проверяем наличие Telegram WebApp\n      if (!window.Telegram || !window.Telegram.WebApp) {\n        this.error = 'Telegram WebApp is not available';\n        this.loading = false;\n        return;\n      }\n\n      try {\n        const response = await api.get(`/vibix/movies/${id}`, {\n          params: { source }\n        });\n\n        if (response.data) {\n          this.currentMovie = response.data;\n        } else {\n          this.error = 'Failed to load movie';\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async searchMovie(url: string) {\n      this.loading = true;\n      this.error = null;\n      this.currentMovie = null;\n\n      // Проверяем наличие Telegram WebApp\n      if (!window.Telegram || !window.Telegram.WebApp) {\n        this.error = 'Telegram WebApp is not available';\n        this.loading = false;\n        return null;\n      }\n\n      try {\n        const response = await api.get('/vibix/search', {\n          params: { url }\n        });\n\n        if (response.data.success) {\n          this.currentMovie = response.data.data;\n          return response.data.data;\n        } else {\n          this.error = response.data.message || 'Failed to search movie';\n          return null;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n\n        return null;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    setSearchQuery(query: string) {\n      // Функция оставлена для совместимости, но searchQuery больше не используется\n      console.log('Search query set:', query);\n    },\n\n    clearCurrentMovie() {\n      this.currentMovie = null;\n    },\n\n    // Метод для загрузки фильмов по категории\n    async fetchMoviesByCategory(category: MovieCategory, page: number = 1, limit: number = 20) {\n      const requestKey = `${category}_${page}_${limit}`;\n\n      // Устанавливаем состояние загрузки для категории\n      this.categoryLoading = { ...this.categoryLoading, [category]: true };\n      this.categoryError = { ...this.categoryError, [category]: null };\n\n      try {\n        const response = await vibixService.getMoviesByCategory(category, page, limit);\n\n        // Проверяем наличие данных\n        if (response.data?.length > 0) {\n          // Если это первая страница, заменяем существующие фильмы\n          // Если не первая, то не обновляем кэш категории, так как он содержит только первую страницу\n          if (page === 1) {\n            this.categoryMovies[category] = response.data;\n          }\n          return response.data;\n        } else {\n          // Устанавливаем ошибку, если данных нет\n          this.categoryError[category] = 'Не удалось загрузить фильмы для категории';\n          return [];\n        }\n      } catch (error: any) {\n        // Обрабатываем ошибку\n        this.categoryError[category] = error.message || 'Ошибка при загрузке фильмов';\n        return [];\n      } finally {\n        // Завершаем загрузку и удаляем запрос из отслеживаемых\n        this.categoryLoading[category] = false;\n        delete this.pendingRequests[requestKey];\n      }\n    },\n\n    // Получить фильмы для категории (из кэша или загрузить)\n    async getMoviesByCategory(\n      category: MovieCategory,\n      page: number = 1,\n      limit: number = 20,\n      useCache: boolean = true\n    ) {\n      const requestKey = `${category}_${page}_${limit}`;\n\n      // Если фильмы для этой категории уже загружены и нужно использовать кэш\n      if (useCache && page === 1 && this.categoryMovies[category]?.length > 0) {\n        return this.categoryMovies[category];\n      }\n\n      // Если запрос уже выполняется, ждем его завершения\n      if (this.pendingRequests[requestKey]) {\n        return await this.pendingRequests[requestKey]!;\n      }\n\n      // Создаем новый запрос и сохраняем его\n      const request = this.fetchMoviesByCategory(category, page, limit);\n      this.pendingRequests[requestKey] = request;\n\n      return await request;\n    }\n  }\n});\n", "import { defineStore } from 'pinia';\nimport api from '@/services/api';\n\ninterface MovieDetails {\n  id: string;\n  name_rus: string;\n  name_original: string;\n  iframe_url: string;\n  type: string;\n  year: number;\n  kp_id?: string;\n  imdb_id?: string;\n  kp_rating?: number;\n  imdb_rating?: number;\n  description_short?: string;\n  poster_url?: string;\n}\n\ninterface Bookmark {\n  _id: string;\n  source: 'kp' | 'imdb';\n  details: MovieDetails | null;\n}\n\nexport const useBookmarksStore = defineStore('bookmarks', {\n  state: () => ({\n    bookmarks: [] as Bookmark[],\n    loading: false,\n    error: null as string | null,\n    lastFetchTime: 0 // Время последней загрузки закладок\n  }),\n\n  actions: {\n    async fetchBookmarks(forceRefresh = false) {\n      // Если закладки уже загружены и прошло менее 5 минут с последней загрузки, не загружаем их снова\n      const now = Date.now();\n      const fiveMinutes = 5 * 60 * 1000; // 5 минут в миллисекундах\n\n      if (!forceRefresh &&\n          this.bookmarks.length > 0 &&\n          now - this.lastFetchTime < fiveMinutes) {\n        return;\n      }\n\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.get('/bookmarks');\n\n        if (response.data.success) {\n          this.bookmarks = response.data.data;\n          this.lastFetchTime = now;\n        } else {\n          this.error = response.data.message || 'Failed to load bookmarks';\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error fetching bookmarks:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async addBookmark(movie_id: string, source: 'kp' | 'imdb') {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.post('/bookmarks', { movie_id, source });\n\n        if (response.data.success) {\n          // Получаем детали фильма для добавления в state\n          let movieDetails;\n          try {\n            if (source === 'kp') {\n              // Используем существующий API для получения деталей фильма\n              const movieResponse = await api.get(`/vibix/movies/${movie_id}?source=kp`);\n              movieDetails = movieResponse.data;\n            } else {\n              const movieResponse = await api.get(`/vibix/movies/${movie_id}?source=imdb`);\n              movieDetails = movieResponse.data;\n            }\n\n            // Добавляем новую закладку в state\n            this.bookmarks.unshift({\n              _id: response.data.data._id,\n              source: source,\n              details: movieDetails\n            });\n          } catch (movieError) {\n            console.error('Error fetching movie details:', movieError);\n            // Если не удалось получить детали фильма, все равно добавляем закладку\n            this.bookmarks.unshift({\n              _id: response.data.data._id,\n              source: source,\n              details: null\n            });\n          }\n\n          return true;\n        } else {\n          this.error = response.data.message || 'Failed to add bookmark';\n          return false;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error adding bookmark:', error);\n        return false;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async deleteBookmark(id: string) {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.delete(`/bookmarks/${id}`);\n\n        if (response.data.success) {\n          // Remove bookmark from state\n          this.bookmarks = this.bookmarks.filter(bookmark => bookmark._id !== id);\n          return true;\n        } else {\n          this.error = response.data.message || 'Failed to delete bookmark';\n          return false;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error deleting bookmark:', error);\n        return false;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    isBookmarked(movie_id: string, source: 'kp' | 'imdb'): boolean {\n      return this.bookmarks.some(bookmark => {\n        if (!bookmark.details) return false;\n\n        if (source === 'kp') {\n          return bookmark.details.kp_id === movie_id && bookmark.source === source;\n        } else if (source === 'imdb') {\n          return bookmark.details.imdb_id === movie_id && bookmark.source === source;\n        }\n\n        return false;\n      });\n    },\n\n    getBookmarkId(movie_id: string, source: 'kp' | 'imdb'): string | null {\n      const bookmark = this.bookmarks.find(bookmark => {\n        if (!bookmark.details) return false;\n\n        if (source === 'kp') {\n          return bookmark.details.kp_id === movie_id && bookmark.source === source;\n        } else if (source === 'imdb') {\n          return bookmark.details.imdb_id === movie_id && bookmark.source === source;\n        }\n\n        return false;\n      });\n\n      return bookmark ? bookmark._id : null;\n    }\n  }\n});\n"], "names": ["getMovieSource", "movie", "kp_id", "imdb_id", "getMovieId", "source", "formatMovieType", "type", "types", "tv", "anime", "getMoviePosterUrl", "poster_urls", "w500", "poster_url", "_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "ref", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_BannerCarousel", "_resolveComponent", "_component_MovieRow", "_component_SearchBar", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "_Fragment", "_renderList", "visibleCategories", "category", "key", "id", "title", "showAdditionalCategories", "additionalCategories", "_createCommentVNode", "xmlns", "viewBox", "fill", "stroke", "_hoisted_4", "_component_ErrorMessage", "_normalizeClass", "expanded", "isExpanded", "onClick", "_withModifiers", "args", "handleMainButtonClick", "disabled", "loading", "searchQuery", "trim", "x1", "y1", "x2", "y2", "points", "cx", "cy", "r", "_withDirectives", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "search", "_vModelText", "moviesStore", "error", "_createBlock", "show", "message", "inline", "onClose", "clearError", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "width", "height", "_toDisplayString", "showRetry", "$emit", "retryText", "showClose", "defineComponent", "name", "props", "Boolean", "default", "String", "required", "emits", "__exports__", "components", "ErrorMessage", "setup", "router", "useRouter", "useMoviesStore", "searchInput", "toggleSearch", "value", "setTimeout", "focus", "async", "searchMovie", "push", "console", "handleClickOutside", "onMounted", "document", "addEventListener", "onBeforeUnmount", "removeEventListener", "slides", "slide", "index", "active", "currentSlide", "style", "_normalizeStyle", "transform", "handleSlideClick", "backgroundImage", "imageUrl", "description", "goToSlide", "prevSlide", "nextSlide", "getRandomItems", "arr", "n", "result", "used", "Set", "length", "size", "idx", "Math", "floor", "random", "has", "add", "carouselContainer", "autoplayInterval", "loadFeaturedMovies", "movies", "getMoviesByCategory", "MovieCategory", "POPULAR", "filtered", "filter", "randomMovies", "movieSlides", "map", "name_rus", "name_original", "description_short", "year", "link", "adSlide", "isAd", "splice", "err", "resetAutoplay", "startAutoplay", "stopAutoplay", "window", "setInterval", "clearInterval", "touchStartX", "touchEndX", "handleTouchStart", "e", "changedTouches", "screenX", "handleTouchEnd", "handleSwipe", "swipe<PERSON><PERSON><PERSON><PERSON>", "passive", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_component_MovieTile", "canScrollLeft", "showScrollButtons", "scrollLeft", "loadMovies", "isExclusive", "hasMorePages", "loadNextPage", "loadingNextPage", "canScrollRight", "scrollRight", "navigateToMovie", "posterUrl", "src", "alt", "_createStaticVNode", "kp_rating", "imdb_rating", "getSubtitle", "toggleBookmark", "isBookmarked", "d", "Object", "bookmarksStore", "useBookmarksStore", "computed", "movieId", "event", "stopPropagation", "bookmarkId", "getBookmarkId", "deleteBookmark", "addBookmark", "MovieTile", "scrollContainer", "currentPage", "categoryMovies", "updateScrollButtons", "parseInt", "scrollWidth", "clientWidth", "scrollAmount", "scrollBy", "left", "behavior", "handleScroll", "handleResize", "rowRef", "isVisible", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "unobserve", "rootMargin", "threshold", "observe", "nextPageMovies", "SearchBar", "BannerCarousel", "MovieRow", "lazyLoadTrigger", "CATEGORIES", "NEW", "THRILLER", "DRAMA", "ACTION", "SCIFI", "DETECTIVE", "INITIAL_CATEGORIES_COUNT", "slice", "loadAdditionalCategories", "disconnect", "fetchBookmarks", "VibixService", "baseUrl", "constructor", "this", "page", "limit", "response", "api", "get", "params", "data", "getMovie", "searchByUrl", "url", "defineStore", "state", "currentMovie", "categoryLoading", "categoryError", "pendingRequests", "actions", "fetchMovieById", "Telegram", "WebApp", "success", "setSearch<PERSON>uery", "query", "log", "clearCurrentMovie", "fetchMoviesByCategory", "request<PERSON>ey", "vibixService", "useCache", "request", "bookmarks", "lastFetchTime", "forceRefresh", "now", "Date", "fiveMinutes", "movie_id", "post", "movieDetails", "movieResponse", "unshift", "_id", "details", "movieError", "delete", "bookmark", "some", "find"], "sourceRoot": ""}