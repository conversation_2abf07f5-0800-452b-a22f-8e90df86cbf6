<template>
  <div class="task-card" @click="openTask" :class="{ loading: loading }">
    <div class="task-logo">
      <img v-if="task.logo_url" :src="task.logo_url" :alt="task.title">
      <div v-else class="logo-placeholder">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="16"></line>
          <line x1="8" y1="12" x2="16" y2="12"></line>
        </svg>
      </div>
    </div>
    <div class="task-info">
      <h3 class="task-title">{{ task.title }}</h3>
      <div class="task-points">{{ task.points }} {{ pointsText }}</div>
    </div>
    <div class="task-action">
      <div v-if="loading" class="loading-spinner"></div>
      <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron-icon">
        <polyline points="9 18 15 12 9 6"></polyline>
      </svg>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import { useTasksStore } from '@/store/tasks';

export default defineComponent({
  name: 'TaskCard',
  props: {
    task: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const tasksStore = useTasksStore();
    const loading = ref(false);

    // Format points text based on count
    const pointsText = computed(() => {
      const points = props.task.points;
      if (points % 10 === 1 && points % 100 !== 11) {
        return 'балл';
      } else if ([2, 3, 4].includes(points % 10) && ![12, 13, 14].includes(points % 100)) {
        return 'балла';
      } else {
        return 'баллов';
      }
    });

    // Open task link and mark as completed
    const openTask = async () => {
      loading.value = true;

      try {
        // Open the link in Telegram browser
        tasksStore.openTaskLink(props.task.link);

        // Mark task as completed
        await tasksStore.completeTask(props.task._id);
      } catch (error) {
        console.error('Error completing task:', error);
      } finally {
        loading.value = false;
      }
    };

    return {
      pointsText,
      openTask,
      loading
    };
  }
});
</script>

<style scoped>
.task-card {
  display: flex;
  align-items: center;
  background-color: var(--tg-theme-secondary-bg-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.task-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.task-card:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.task-card.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.task-logo {
  width: 48px;
  height: 48px;
  min-width: 48px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--tg-theme-bg-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.logo-placeholder {
  width: 24px;
  height: 24px;
  color: var(--tg-theme-hint-color);
}

.task-info {
  flex: 1;
  margin: 0 var(--spacing-md);
  min-width: 0; /* Allows text to truncate */
}

.task-title {
  font-size: 18px;
  margin: 0 0 6px 0;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;
}

.task-points {
  font-size: 15px;
  color: var(--tg-theme-hint-color);
  font-weight: 500;
}

.task-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  min-width: 24px;
}

.chevron-icon {
  width: 20px;
  height: 20px;
  color: var(--tg-theme-hint-color);
  transition: transform 0.2s ease;
}

.task-card:hover .chevron-icon {
  transform: translateX(2px);
  color: var(--tg-theme-button-color);
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--tg-theme-hint-color);
  border-radius: 50%;
  border-top-color: var(--tg-theme-button-color);
  animation: spin 1s ease-in-out infinite;
}



@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
