/**
 * Утилиты для работы с платформой Telegram WebApp
 */

/**
 * Проверяет, является ли текущая платформа мобильной (iOS или Android)
 * @returns true если платформа iOS или Android, false в остальных случаях
 */
export const isMobilePlatform = (): boolean => {
  if (window.Telegram?.WebApp?.platform) {
    const platform = window.Telegram.WebApp.platform.toLowerCase();
    return platform === 'ios' || platform === 'android';
  }
  return false;
};
