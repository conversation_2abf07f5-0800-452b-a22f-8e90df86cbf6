/**
 * Утилиты для работы с платформой Telegram WebApp
 */

/**
 * Проверяет, является ли текущая платформа мобильной (iOS или Android)
 * @returns true если платформа iOS или Android, false в остальных случаях
 */
export const isMobilePlatform = (): boolean => {
  if (window.Telegram?.WebApp?.platform) {
    const platform = window.Telegram.WebApp.platform.toLowerCase();
    return platform === 'ios' || platform === 'android';
  }
  return false;
};

/**
 * Получает название текущей платформы
 * @returns строка с названием платформы или 'unknown' если не определена
 */
export const getPlatform = (): string => {
  return window.Telegram?.WebApp?.platform || 'unknown';
};

/**
 * Проверяет, доступен ли Telegram WebApp
 * @returns true если Telegram WebApp доступен
 */
export const isTelegramWebAppAvailable = (): boolean => {
  return !!(window.Telegram?.WebApp);
};
