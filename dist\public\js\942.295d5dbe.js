"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[942],{522:(e,o,a)=>{function r(e){return e?e.kp_id?"kp":e.imdb_id?"imdb":null:null}function s(e){if(!e)return null;const o=r(e);return"kp"===o?e.kp_id||null:"imdb"===o&&e.imdb_id||null}function t(e){const o={movie:"Фильм",tv:"Сериал",anime:"Аниме","tv-series":"Сериал","anime-series":"Аниме-сериал"};return o[e]||e}function i(e){return e?e.poster_urls?.w500?e.poster_urls.w500:e.poster_url||null:null}a.d(o,{GO:()=>s,Ib:()=>r,nR:()=>i,uF:()=>t})},938:(e,o,a)=>{a.d(o,{a:()=>t});var r=a(657),s=a(526);const t=(0,r.nY)("bookmarks",{state:()=>({bookmarks:[],loading:!1,error:null,lastFetchTime:0}),actions:{async fetchBookmarks(e=!1){const o=Date.now(),a=3e5;if(!(!e&&this.bookmarks.length>0&&o-this.lastFetchTime<a)){this.loading=!0,this.error=null;try{const e=await s.A.get("/bookmarks");e.data.success?(this.bookmarks=e.data.data,this.lastFetchTime=o):this.error=e.data.message||"Failed to load bookmarks"}catch(r){this.error=r.response?.data?.message||r.message||"Unknown error",console.error("Error fetching bookmarks:",r)}finally{this.loading=!1}}},async addBookmark(e,o){this.loading=!0,this.error=null;try{const r=await s.A.post("/bookmarks",{movie_id:e,source:o});if(r.data.success){let t;try{if("kp"===o){const o=await s.A.get(`/vibix/movies/${e}?source=kp`);t=o.data}else{const o=await s.A.get(`/vibix/movies/${e}?source=imdb`);t=o.data}this.bookmarks.unshift({_id:r.data.data._id,source:o,details:t})}catch(a){console.error("Error fetching movie details:",a),this.bookmarks.unshift({_id:r.data.data._id,source:o,details:null})}return!0}return this.error=r.data.message||"Failed to add bookmark",!1}catch(r){return this.error=r.response?.data?.message||r.message||"Unknown error",console.error("Error adding bookmark:",r),!1}finally{this.loading=!1}},async deleteBookmark(e){this.loading=!0,this.error=null;try{const o=await s.A.delete(`/bookmarks/${e}`);return o.data.success?(this.bookmarks=this.bookmarks.filter((o=>o._id!==e)),!0):(this.error=o.data.message||"Failed to delete bookmark",!1)}catch(o){return this.error=o.response?.data?.message||o.message||"Unknown error",console.error("Error deleting bookmark:",o),!1}finally{this.loading=!1}},isBookmarked(e,o){return this.bookmarks.some((a=>!!a.details&&("kp"===o?a.details.kp_id===e&&a.source===o:"imdb"===o&&(a.details.imdb_id===e&&a.source===o))))},getBookmarkId(e,o){const a=this.bookmarks.find((a=>!!a.details&&("kp"===o?a.details.kp_id===e&&a.source===o:"imdb"===o&&(a.details.imdb_id===e&&a.source===o))));return a?a._id:null}}})},942:(e,o,a)=>{a.r(o),a.d(o,{default:()=>U});var r=a(768),s=a(232);const t={class:"bookmarks-view"},i={class:"container"},n={key:0,class:"loading-container"},l={key:1,class:"error-container"},k={key:2,class:"empty-container"},d={key:3,class:"bookmarks-list"},c={key:1,class:"bookmark-error"},m=["onClick"];function u(e,o,a,u,v,g){const b=(0,r.g2)("router-link"),p=(0,r.g2)("MovieCard");return(0,r.uX)(),(0,r.CE)("div",t,[(0,r.Lk)("div",i,[o[6]||(o[6]=(0,r.Lk)("h1",{class:"page-title"},"Закладки",-1)),o[7]||(o[7]=(0,r.Lk)("p",{class:"page-description"},"Сохраненные фильмы",-1)),e.loading?((0,r.uX)(),(0,r.CE)("div",n,o[1]||(o[1]=[(0,r.Lk)("div",{class:"loading-spinner large"},null,-1),(0,r.Lk)("p",null,"Загрузка закладок...",-1)]))):e.error?((0,r.uX)(),(0,r.CE)("div",l,[(0,r.Lk)("p",null,(0,s.v_)(e.error),1),(0,r.Lk)("button",{onClick:o[0]||(o[0]=(...o)=>e.fetchBookmarks&&e.fetchBookmarks(...o)),class:"retry-button"},"Повторить")])):0===e.bookmarks.length?((0,r.uX)(),(0,r.CE)("div",k,[o[3]||(o[3]=(0,r.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"empty-icon"},[(0,r.Lk)("path",{d:"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"})],-1)),o[4]||(o[4]=(0,r.Lk)("p",null,"У вас пока нет закладок",-1)),(0,r.bF)(b,{to:"/catalog",class:"browse-button"},{default:(0,r.k6)((()=>o[2]||(o[2]=[(0,r.eW)("Перейти в каталог")]))),_:1})])):((0,r.uX)(),(0,r.CE)("div",d,[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.bookmarks,(a=>((0,r.uX)(),(0,r.CE)("div",{key:a._id,class:"bookmark-item"},[a.details?((0,r.uX)(),(0,r.Wv)(p,{key:0,movie:a.details,isInBookmarksView:!0},null,8,["movie"])):((0,r.uX)(),(0,r.CE)("div",c,[o[5]||(o[5]=(0,r.Lk)("p",null,"Не удалось загрузить информацию о фильме",-1)),(0,r.Lk)("button",{onClick:o=>e.removeBookmark(a._id),class:"remove-button"},"Удалить закладку",8,m)]))])))),128))]))])])}var v=a(130);const g={class:"movie-poster"},b=["src","alt"],p={key:1,class:"poster-placeholder"},h={class:"movie-year"},f={key:2,class:"movie-rating"},w={class:"movie-info"},y={class:"movie-title"},_={class:"movie-original-title"},B={class:"movie-actions"},C=["fill"];function E(e,o,a,t,i,n){return(0,r.uX)(),(0,r.CE)("div",{class:"movie-card",onClick:o[1]||(o[1]=(...o)=>e.navigateToMovie&&e.navigateToMovie(...o))},[(0,r.Lk)("div",g,[e.posterUrl?((0,r.uX)(),(0,r.CE)("img",{key:0,src:e.posterUrl,alt:e.movie.name_rus,class:"poster-image"},null,8,b)):((0,r.uX)(),(0,r.CE)("div",p,o[2]||(o[2]=[(0,r.Fv)('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" data-v-2f2b267e><rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18" data-v-2f2b267e></rect><circle cx="12" cy="12" r="4" data-v-2f2b267e></circle><line x1="8" y1="4" x2="16" y2="4" data-v-2f2b267e></line><line x1="4" y1="8" x2="4" y2="16" data-v-2f2b267e></line><line x1="20" y1="8" x2="20" y2="16" data-v-2f2b267e></line><line x1="8" y1="20" x2="16" y2="20" data-v-2f2b267e></line></svg>',1)]))),(0,r.Lk)("div",h,(0,s.v_)(e.movie.year),1),e.movie.kp_rating||e.movie.imdb_rating?((0,r.uX)(),(0,r.CE)("div",f,(0,s.v_)(e.movie.kp_rating||e.movie.imdb_rating),1)):(0,r.Q3)("",!0)]),(0,r.Lk)("div",w,[(0,r.Lk)("h3",y,(0,s.v_)(e.movie.name_rus),1),(0,r.Lk)("p",_,(0,s.v_)(e.movie.name_original),1),(0,r.Lk)("div",B,[(0,r.Lk)("button",{class:"bookmark-button",onClick:o[0]||(o[0]=(0,v.D$)(((...o)=>e.toggleBookmark&&e.toggleBookmark(...o)),["stop"]))},[((0,r.uX)(),(0,r.CE)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:e.isBookmarked?"currentColor":"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},o[3]||(o[3]=[(0,r.Lk)("path",{d:"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"},null,-1)]),8,C))])])])])}var L=a(144),x=a(387),X=a(938),I=a(522);const F=(0,r.pM)({name:"MovieCard",props:{movie:{type:Object,required:!0},isInBookmarksView:{type:Boolean,default:!1}},setup(e){const o=(0,x.rd)(),a=(0,X.a)(),s=(0,L.KR)(!1),t=(0,r.EW)((()=>(0,I.Ib)(e.movie))),i=(0,r.EW)((()=>(0,I.GO)(e.movie))),n=(0,r.EW)((()=>!!e.isInBookmarksView||!(!t.value||!i.value)&&a.isBookmarked(i.value,t.value))),l=(0,r.EW)((()=>(0,I.nR)(e.movie))),k=()=>{t.value&&i.value&&o.push(`/movie/${t.value}/${i.value}`)},d=async o=>{if(o.stopPropagation(),t.value&&i.value){s.value=!0;try{if(e.isInBookmarksView){const e=a.getBookmarkId(i.value,t.value);e&&await a.deleteBookmark(e)}else if(n.value){const e=a.getBookmarkId(i.value,t.value);e&&await a.deleteBookmark(e)}else await a.addBookmark(i.value,t.value)}catch(r){console.error("Error toggling bookmark:",r)}finally{s.value=!1}}};return{source:t,movieId:i,isBookmarked:n,posterUrl:l,navigateToMovie:k,toggleBookmark:d,loading:s}}});var M=a(241);const W=(0,M.A)(F,[["render",E],["__scopeId","data-v-2f2b267e"]]),V=W,A=(0,r.pM)({name:"BookmarksView",components:{MovieCard:V},setup(){const e=(0,X.a)(),o=(0,r.EW)((()=>e.bookmarks)),a=(0,r.EW)((()=>e.loading)),s=(0,r.EW)((()=>e.error)),t=async()=>{await e.fetchBookmarks()},i=async o=>{await e.deleteBookmark(o)};return(0,r.sV)((()=>{t()})),{bookmarks:o,loading:a,error:s,fetchBookmarks:t,removeBookmark:i}}}),T=(0,M.A)(A,[["render",u],["__scopeId","data-v-5fc778cc"]]),U=T}}]);
//# sourceMappingURL=942.295d5dbe.js.map