<template>
  <div>
    <div class="search-container" :class="{ expanded: isExpanded }" @click.stop>
      <button class="search-toggle" @click.stop="handleMainButtonClick" :disabled="loading">
        <div v-if="loading" class="loading-spinner main"></div>
        <!-- Стрелка, когда есть текст для поиска -->
        <svg v-else-if="searchQuery.trim()" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
        <!-- Лупа по умолчанию -->
        <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
        </svg>
      </button>

      <div class="search-form" @click.stop>
        <input
          ref="searchInput"
          type="text"
          v-model="searchQuery"
          placeholder="Ссылка на кинопоиск или imdb"
          @keyup.enter="search"
        />
      </div>
    </div>

    <!-- Показываем ошибку поиска -->
    <ErrorMessage
      v-if="moviesStore.error && isExpanded"
      :show="true"
      :message="moviesStore.error"
      :inline="true"
      :show-close="true"
      @close="clearError"
      class="search-error"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { useMoviesStore } from '@/store/movies';
import ErrorMessage from './ErrorMessage.vue';

export default defineComponent({
  name: 'SearchBar',
  components: {
    ErrorMessage
  },
  setup() {
    const router = useRouter();
    const moviesStore = useMoviesStore();
    const searchQuery = ref('');
    const isExpanded = ref(false);
    const loading = ref(false);
    const searchInput = ref<HTMLInputElement | null>(null);

    // Clear error
    const clearError = () => {
      moviesStore.error = null;
    };

    // Toggle search form visibility
    const toggleSearch = () => {
      isExpanded.value = !isExpanded.value;
      if (isExpanded.value && searchInput.value) {
        // Фокусируемся на поле ввода при раскрытии
        setTimeout(() => {
          searchInput.value?.focus();
        }, 300);
      }
    };

    // Handle main button click (search or toggle)
    const handleMainButtonClick = () => {
      if (searchQuery.value.trim()) {
        // Если есть текст - выполняем поиск
        search();
      } else {
        // Если нет текста - переключаем видимость
        toggleSearch();
      }
    };

    // Search for movie
    const search = async () => {
      if (!searchQuery.value.trim() || loading.value) return;

      loading.value = true;
      // Очищаем предыдущие ошибки при новом поиске
      clearError();

      try {
        const movie = await moviesStore.searchMovie(searchQuery.value);

        if (movie) {
          // Determine source and ID
          let source = 'kp';
          let id = movie.kp_id;

          if (!id && movie.imdb_id) {
            source = 'imdb';
            id = movie.imdb_id;
          }

          if (source && id) {
            // Navigate to movie page
            router.push(`/movie/${source}/${id}`);

            // Reset search completely
            searchQuery.value = '';
            isExpanded.value = false;
            clearError();
          }
        }
      } catch (error) {
        console.error('Error searching movie:', error);
      } finally {
        loading.value = false;
      }
    };

    // Close search when clicking outside
    const handleClickOutside = () => {
      if (isExpanded.value) {
        // Закрываем поиск и очищаем содержимое
        isExpanded.value = false;
        searchQuery.value = '';
        clearError();
      }
    };

    // Setup click outside listener
    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
    });

    onBeforeUnmount(() => {
      document.removeEventListener('click', handleClickOutside);
    });

    return {
      searchQuery,
      isExpanded,
      loading,
      searchInput,
      moviesStore,
      toggleSearch,
      handleMainButtonClick,
      search,
      clearError
    };
  }
});
</script>

<style scoped>
.search-container {
  position: fixed;
  top: var(--spacing-md);
  right: var(--spacing-md);
  z-index: 10;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.search-toggle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--tg-theme-button-color);
  color: var(--tg-theme-button-text-color);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  z-index: 2;
}

.search-toggle svg {
  width: 24px;
  height: 24px;
}

.search-toggle:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.search-form {
  position: absolute;
  right: 0;
  top: 0;
  background-color: var(--tg-theme-secondary-bg-color);
  border-radius: 24px;
  display: flex;
  align-items: center;
  padding: 6px;
  width: 0;
  overflow: hidden;
  transition: width 0.3s ease, opacity 0.3s ease;
  opacity: 0;
  box-shadow: var(--shadow-sm);
}

.expanded .search-form {
  width: calc(100vw - 32px);
  max-width: 500px;
  opacity: 1;
}

.search-form input {
  flex: 1;
  border: none;
  background: none;
  padding: 8px 12px;
  font-size: var(--font-size-sm);
  color: var(--tg-theme-text-color);
  outline: none;
}



.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 1.5px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

.loading-spinner.main {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.search-error {
  position: fixed;
  top: 64px;
  left: var(--spacing-md);
  right: var(--spacing-md);
  z-index: 9;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}
</style>
