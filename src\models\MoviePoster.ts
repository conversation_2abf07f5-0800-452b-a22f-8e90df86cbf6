import mongoose, { Document, Schema } from 'mongoose';

export interface IMoviePoster extends Document {
  vibix_id: string;
  imdb_id?: string;
  kp_id?: string;
  tmdb_id?: number;
  tmdb_poster_path?: string;
  poster_urls: {
    w500: string;
    original: string;
  };
  cache_status: 'pending' | 'found' | 'not_found' | 'error';
  expires_at: Date;
  retry_count: number;
  error_message?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

const MoviePosterSchema: Schema = new Schema({
  vibix_id: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  imdb_id: {
    type: String,
    sparse: true,
    index: true
  },
  kp_id: {
    type: String,
    sparse: true,
    index: true
  },
  tmdb_id: {
    type: Number,
    sparse: true,
    index: true
  },
  tmdb_poster_path: {
    type: String,
    sparse: true
  },
  poster_urls: {
    w500: {
      type: String,
      default: ''
    },
    original: {
      type: String,
      default: ''
    }
  },
  cache_status: {
    type: String,
    enum: ['pending', 'found', 'not_found', 'error'],
    default: 'pending',
    index: true
  },
  expires_at: {
    type: Date,
    default: () => new Date(Date.now() + 60 * 24 * 60 * 60 * 1000)
  },
  retry_count: {
    type: Number,
    default: 0,
    min: 0,
    max: 3
  },
  error_message: {
    type: String,
    sparse: true
  }
}, {
  timestamps: true
});

// Оптимизированные индексы
MoviePosterSchema.index({ cache_status: 1, expires_at: 1 });
MoviePosterSchema.index({ cache_status: 1, retry_count: 1 });
MoviePosterSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 });

export default mongoose.model<IMoviePoster>('MoviePoster', MoviePosterSchema);
