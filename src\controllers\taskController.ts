import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Task, { ITask } from '../models/Task';
import CompletedTask from '../models/CompletedTask';
import User from '../models/User';

// Функция для проверки валидности ObjectId
const isValidObjectId = (id: string): boolean => {
  return mongoose.Types.ObjectId.isValid(id);
};

// Get active tasks for user
export const getTasks = async (req: Request, res: Response) => {
  try {
    const userId = req.user.chat_id;

    // Get all active tasks
    const activeTasks = await Task.find({ is_active: true });

    // Get tasks completed by the user
    const completedTasks = await CompletedTask.find({ user_id: userId });
    const completedTaskIds = completedTasks.map(task => task.task_id.toString());

    // Filter out completed tasks
    const availableTasks = activeTasks.filter((task: ITask) =>
      !completedTaskIds.includes(task._id.toString())
    );

    res.json({
      success: true,
      data: availableTasks
    });
  } catch (error) {
    console.error('Error in getTasks controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tasks'
    });
  }
};

// Complete a task
export const completeTask = async (req: Request, res: Response) => {
  try {
    const userId = req.user.chat_id;
    const { task_id } = req.body;

    if (!task_id) {
      return res.status(400).json({
        success: false,
        message: 'Task ID is required'
      });
    }

    if (!isValidObjectId(task_id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid task ID format'
      });
    }

    // Check if task exists and is active
    const task = await Task.findOne({ _id: task_id, is_active: true });

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found or inactive'
      });
    }

    // Check if task is already completed
    const existingCompletion = await CompletedTask.findOne({ user_id: userId, task_id });

    if (existingCompletion) {
      return res.status(400).json({
        success: false,
        message: 'Task already completed'
      });
    }

    // Create completed task record
    const completedTask = new CompletedTask({
      user_id: userId,
      task_id
    });

    await completedTask.save();

    // Update user's task points
    await User.updateOne(
      { chat_id: userId },
      { $inc: { task_points: task.points } }
    );

    res.status(201).json({
      success: true,
      data: {
        task_id: task._id,
        points: task.points,
        completed_at: completedTask.created_at
      }
    });
  } catch (error) {
    console.error('Error in completeTask controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to complete task'
    });
  }
};

// Get completed tasks
export const getCompletedTasks = async (req: Request, res: Response) => {
  try {
    const userId = req.user.chat_id;

    // Get completed tasks with task details
    const completedTasks = await CompletedTask.find({ user_id: userId })
      .populate('task_id')
      .sort({ completed_at: -1 });

    res.json({
      success: true,
      data: completedTasks
    });
  } catch (error) {
    console.error('Error in getCompletedTasks controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch completed tasks'
    });
  }
};
