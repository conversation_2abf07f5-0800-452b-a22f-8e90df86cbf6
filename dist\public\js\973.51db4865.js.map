{"version": 3, "file": "js/973.51db4865.js", "mappings": "8HASM,SAAUA,EAAeC,GAC7B,OAAKA,EAEDA,EAAMC,MAAc,KACpBD,EAAME,QAAgB,OAEnB,KALY,IAMrB,CAOM,SAAUC,EAAWH,GACzB,IAAKA,EAAO,OAAO,KAEnB,MAAMI,EAASL,EAAeC,GAE9B,MAAe,OAAXI,EAAwBJ,EAAMC,OAAS,KAC5B,SAAXG,GAA0BJ,EAAME,SAE7B,IACT,CAOM,SAAUG,EAAgBC,GAC9B,MAAMC,EAAgC,CACpCP,MAAO,QACPQ,GAAI,SACJC,MAAO,QACP,YAAa,SACb,eAAgB,gBAGlB,OAAOF,EAAMD,IAASA,CACxB,CAOM,SAAUI,EAAkBV,GAChC,OAAKA,EAGDA,EAAMW,aAAaC,KACdZ,EAAMW,YAAYC,KAGpBZ,EAAMa,YAAc,KAPR,IAQrB,C,yFCdYC,E,UAAZ,SAAYA,GACVA,EAAA,qBACAA,EAAA,aACAA,EAAA,uBACAA,EAAA,iBACAA,EAAA,mBACAA,EAAA,iBACAA,EAAA,wBACD,EARD,CAAYA,IAAAA,EAAa,KAUzB,MAAMC,EACIC,QAERC,WAAAA,GAEEC,KAAKF,QAAU,yCACjB,CAGA,yBAAMG,CAAoBC,EAAyBC,EAAe,EAAGC,EAAgB,IAEnF,MAAMC,QAAiBC,EAAAA,EAAIC,IAAI,GAAGP,KAAKF,2BAA2BI,IAAY,CAC5EM,OAAQ,CACNJ,QACAD,UAGJ,OAAOE,EAASI,IAClB,CAGA,cAAMC,CAASC,GACb,MAAMN,QAAiBC,EAAAA,EAAIC,IAAI,GAAGP,KAAKF,kBAAkBa,KACzD,OAAON,EAASI,IAClB,CAGA,iBAAMG,CAAYC,GAChB,MAAMR,QAAiBC,EAAAA,EAAIC,IAAI,GAAGP,KAAKF,iBAAkB,CACvDU,OAAQ,CAAEK,SAEZ,OAAOR,EAASI,IAClB,EAGF,YAAmBZ,C,gEC9EZ,MAAMiB,GAAiBC,EAAAA,EAAAA,IAAY,SAAU,CAClDC,MAAOA,KAAA,CACLC,aAAc,KACdC,SAAS,EACTC,MAAO,KAEPC,eAAgB,CAAC,EACjBC,gBAAiB,CAAC,EAClBC,cAAe,CAAC,EAEhBC,gBAAiB,CAAC,IAGpBC,QAAS,CACP,oBAAMC,CAAevC,EAAgByB,GAMnC,GALAX,KAAKkB,SAAU,EACflB,KAAKmB,MAAQ,KACbnB,KAAKiB,aAAe,MAGfS,OAAOC,WAAaD,OAAOC,SAASC,OAGvC,OAFA5B,KAAKmB,MAAQ,wCACbnB,KAAKkB,SAAU,GAIjB,IACE,MAAMb,QAAiBC,EAAAA,EAAIC,IAAI,iBAAiBI,IAAM,CACpDH,OAAQ,CAAEtB,YAGRmB,EAASI,KACXT,KAAKiB,aAAeZ,EAASI,KAE7BT,KAAKmB,MAAQ,sBAEjB,CAAE,MAAOA,GACPnB,KAAKmB,MAAQA,EAAMd,UAAUI,MAAMoB,SAAWV,EAAMU,SAAW,eAEjE,CAAE,QACA7B,KAAKkB,SAAU,CACjB,CACF,EAEA,iBAAMY,CAAYjB,GAMhB,GALAb,KAAKkB,SAAU,EACflB,KAAKmB,MAAQ,KACbnB,KAAKiB,aAAe,MAGfS,OAAOC,WAAaD,OAAOC,SAASC,OAGvC,OAFA5B,KAAKmB,MAAQ,mCACbnB,KAAKkB,SAAU,EACR,KAGT,IACE,MAAMb,QAAiBC,EAAAA,EAAIC,IAAI,gBAAiB,CAC9CC,OAAQ,CAAEK,SAGZ,OAAIR,EAASI,KAAKsB,SAChB/B,KAAKiB,aAAeZ,EAASI,KAAKA,KAC3BJ,EAASI,KAAKA,OAErBT,KAAKmB,MAAQd,EAASI,KAAKoB,SAAW,yBAC/B,KAEX,CAAE,MAAOV,GAGP,OAFAnB,KAAKmB,MAAQA,EAAMd,UAAUI,MAAMoB,SAAWV,EAAMU,SAAW,gBAExD,IACT,CAAE,QACA7B,KAAKkB,SAAU,CACjB,CACF,EAEAc,cAAAA,CAAeC,GAEbC,QAAQC,IAAI,oBAAqBF,EACnC,EAEAG,iBAAAA,GACEpC,KAAKiB,aAAe,IACtB,EAGA,2BAAMoB,CAAsBnC,EAAyBC,EAAe,EAAGC,EAAgB,IACrF,MAAMkC,EAAa,GAAGpC,KAAYC,KAAQC,IAG1CJ,KAAKqB,gBAAkB,IAAKrB,KAAKqB,gBAAiB,CAACnB,IAAW,GAC9DF,KAAKsB,cAAgB,IAAKtB,KAAKsB,cAAe,CAACpB,GAAW,MAE1D,IACE,MAAMG,QAAiBkC,EAAAA,EAAatC,oBAAoBC,EAAUC,EAAMC,GAGxE,OAAIC,EAASI,MAAM+B,OAAS,GAGb,IAATrC,IACFH,KAAKoB,eAAelB,GAAYG,EAASI,MAEpCJ,EAASI,OAGhBT,KAAKsB,cAAcpB,GAAY,4CACxB,GAEX,CAAE,MAAOiB,GAGP,OADAnB,KAAKsB,cAAcpB,GAAYiB,EAAMU,SAAW,8BACzC,EACT,CAAE,QAEA7B,KAAKqB,gBAAgBnB,IAAY,SAC1BF,KAAKuB,gBAAgBe,EAC9B,CACF,EAGA,yBAAMrC,CACJC,EACAC,EAAe,EACfC,EAAgB,GAChBqC,GAAoB,GAEpB,MAAMH,EAAa,GAAGpC,KAAYC,KAAQC,IAG1C,GAAIqC,GAAqB,IAATtC,GAAcH,KAAKoB,eAAelB,IAAWsC,OAAS,EACpE,OAAOxC,KAAKoB,eAAelB,GAI7B,GAAIF,KAAKuB,gBAAgBe,GACvB,aAAatC,KAAKuB,gBAAgBe,GAIpC,MAAMI,EAAU1C,KAAKqC,sBAAsBnC,EAAUC,EAAMC,GAG3D,OAFAJ,KAAKuB,gBAAgBe,GAAcI,QAEtBA,CACf,I,uDC3IG,MAAMC,GAAoB5B,EAAAA,EAAAA,IAAY,YAAa,CACxDC,MAAOA,KAAA,CACL4B,UAAW,GACX1B,SAAS,EACTC,MAAO,KACP0B,cAAe,IAGjBrB,QAAS,CACP,oBAAMsB,CAAeC,GAAe,GAElC,MAAMC,EAAMC,KAAKD,MACXE,EAAc,IAEpB,MAAKH,GACD/C,KAAK4C,UAAUJ,OAAS,GACxBQ,EAAMhD,KAAK6C,cAAgBK,GAF/B,CAMAlD,KAAKkB,SAAU,EACflB,KAAKmB,MAAQ,KAEb,IACE,MAAMd,QAAiBC,EAAAA,EAAIC,IAAI,cAE3BF,EAASI,KAAKsB,SAChB/B,KAAK4C,UAAYvC,EAASI,KAAKA,KAC/BT,KAAK6C,cAAgBG,GAErBhD,KAAKmB,MAAQd,EAASI,KAAKoB,SAAW,0BAE1C,CAAE,MAAOV,GACPnB,KAAKmB,MAAQA,EAAMd,UAAUI,MAAMoB,SAAWV,EAAMU,SAAW,gBAC/DK,QAAQf,MAAM,4BAA6BA,EAC7C,CAAE,QACAnB,KAAKkB,SAAU,CACjB,CAnBA,CAoBF,EAEA,iBAAMiC,CAAYC,EAAkBlE,GAClCc,KAAKkB,SAAU,EACflB,KAAKmB,MAAQ,KAEb,IACE,MAAMd,QAAiBC,EAAAA,EAAI+C,KAAK,aAAc,CAAED,WAAUlE,WAE1D,GAAImB,EAASI,KAAKsB,QAAS,CAEzB,IAAIuB,EACJ,IACE,GAAe,OAAXpE,EAAiB,CAEnB,MAAMqE,QAAsBjD,EAAAA,EAAIC,IAAI,iBAAiB6C,eACrDE,EAAeC,EAAc9C,IAC/B,KAAO,CACL,MAAM8C,QAAsBjD,EAAAA,EAAIC,IAAI,iBAAiB6C,iBACrDE,EAAeC,EAAc9C,IAC/B,CAGAT,KAAK4C,UAAUY,QAAQ,CACrBC,IAAKpD,EAASI,KAAKA,KAAKgD,IACxBvE,OAAQA,EACRwE,QAASJ,GAEb,CAAE,MAAOK,GACPzB,QAAQf,MAAM,gCAAiCwC,GAE/C3D,KAAK4C,UAAUY,QAAQ,CACrBC,IAAKpD,EAASI,KAAKA,KAAKgD,IACxBvE,OAAQA,EACRwE,QAAS,MAEb,CAEA,OAAO,CACT,CAEE,OADA1D,KAAKmB,MAAQd,EAASI,KAAKoB,SAAW,0BAC/B,CAEX,CAAE,MAAOV,GAGP,OAFAnB,KAAKmB,MAAQA,EAAMd,UAAUI,MAAMoB,SAAWV,EAAMU,SAAW,gBAC/DK,QAAQf,MAAM,yBAA0BA,IACjC,CACT,CAAE,QACAnB,KAAKkB,SAAU,CACjB,CACF,EAEA,oBAAM0C,CAAejD,GACnBX,KAAKkB,SAAU,EACflB,KAAKmB,MAAQ,KAEb,IACE,MAAMd,QAAiBC,EAAAA,EAAIuD,OAAO,cAAclD,KAEhD,OAAIN,EAASI,KAAKsB,SAEhB/B,KAAK4C,UAAY5C,KAAK4C,UAAUkB,QAAOC,GAAYA,EAASN,MAAQ9C,KAC7D,IAEPX,KAAKmB,MAAQd,EAASI,KAAKoB,SAAW,6BAC/B,EAEX,CAAE,MAAOV,GAGP,OAFAnB,KAAKmB,MAAQA,EAAMd,UAAUI,MAAMoB,SAAWV,EAAMU,SAAW,gBAC/DK,QAAQf,MAAM,2BAA4BA,IACnC,CACT,CAAE,QACAnB,KAAKkB,SAAU,CACjB,CACF,EAEA8C,YAAAA,CAAaZ,EAAkBlE,GAC7B,OAAOc,KAAK4C,UAAUqB,MAAKF,KACpBA,EAASL,UAEC,OAAXxE,EACK6E,EAASL,QAAQ3E,QAAUqE,GAAYW,EAAS7E,SAAWA,EAC9C,SAAXA,IACF6E,EAASL,QAAQ1E,UAAYoE,GAAYW,EAAS7E,SAAWA,KAK1E,EAEAgF,aAAAA,CAAcd,EAAkBlE,GAC9B,MAAM6E,EAAW/D,KAAK4C,UAAUuB,MAAKJ,KAC9BA,EAASL,UAEC,OAAXxE,EACK6E,EAASL,QAAQ3E,QAAUqE,GAAYW,EAAS7E,SAAWA,EAC9C,SAAXA,IACF6E,EAASL,QAAQ1E,UAAYoE,GAAYW,EAAS7E,SAAWA,MAMxE,OAAO6E,EAAWA,EAASN,IAAM,IACnC,I,oECpKJ,MAAMW,EAAa,CCDZC,MAAM,cDEPC,EAAa,CCDVD,MAAM,aDETE,EAAa,CCJnBC,IAAA,EAG0BH,MAAM,qBDK1BI,EAAa,CCRnBD,IAAA,EAQ6BH,MAAM,mBDI7BK,EAAa,CCZnBF,IAAA,EAc6BH,MAAM,iBDE7BM,EAAa,CCDNN,MAAM,gBDEbO,ECjBN,aDkBMC,EAAa,CClBnBL,IAAA,EAwBwCH,MAAM,oBDFxCS,ECtBN,SDuBMC,EAAc,CCQRV,MAAM,eDPZW,EAAc,CCQTX,MAAM,wBDPXY,EAAc,CCSPZ,MAAM,cDRba,EAAc,CCSLb,MAAM,aDRfc,EAAc,CCUFd,MAAM,cDTlBe,EAAc,CC5BpBZ,IAAA,EAwC2DH,MAAM,aDR3DgB,EAAc,CCUFhB,MAAM,qBDTlBiB,EAAc,CCjCpBd,IAAA,EAkDiCH,MAAM,aDbjCkB,EAAc,CCeFlB,MAAM,cDdlBmB,EAAc,CCtCpBhB,IAAA,EAwD0CH,MAAM,qBDd1CoB,EAAc,CCkBPpB,MAAM,oBDjBbqB,EAAc,CC3CpBlB,IAAA,EA6DoCH,MAAM,kBDdpCsB,EC/CN,QDgDMC,EAAc,CC0BPvB,MAAM,iBDxBb,SAAUwB,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAEjD,OAAQC,EAAAA,EAAAA,OCpDRC,EAAAA,EAAAA,IA2FM,MA3FNnC,EA2FM,EA1FJoC,EAAAA,EAAAA,IAyFM,MAzFNlC,EAyFM,CAxFOwB,EAAA5E,UDqDNoF,EAAAA,EAAAA,OCrDLC,EAAAA,EAAAA,IAGM,MAHNhC,EAGMwB,EAAA,KAAAA,EAAA,KAFJS,EAAAA,EAAAA,IAAyC,OAApCnC,MAAM,yBAAuB,UAClCmC,EAAAA,EAAAA,IAAyB,SAAtB,sBAAkB,OAGPV,EAAA3E,QDqDTmF,EAAAA,EAAAA,OCrDPC,EAAAA,EAAAA,IAIM,MAJN9B,EAIM,EAHJ+B,EAAAA,EAAAA,IAAkB,UAAAC,EAAAA,EAAAA,IAAZX,EAAA3E,OAAK,IACXqF,EAAAA,EAAAA,IAAmE,UAA1DE,QAAKX,EAAA,KAAAA,EAAA,GDwDtB,IAAIY,ICxDoBb,EAAAc,YAAAd,EAAAc,cAAAD,IAAYtC,MAAM,gBAAe,cACjDwC,EAAAA,EAAAA,IAAgFT,EAAA,CAAnEU,GAAG,WAAWzC,MAAM,eD6DxB,CCxEjB0C,SAAAC,EAAAA,EAAAA,KAWuD,IAAmBjB,EAAA,KAAAA,EAAA,KAX1EkB,EAAAA,EAAAA,IAWuD,2BAXvDC,EAAA,OAcsBpB,EAAAhH,QDkEPwH,EAAAA,EAAAA,OClETC,EAAAA,EAAAA,IA4EM,MA5EN7B,EA4EM,EA3EJ8B,EAAAA,EAAAA,IAcM,MAdN7B,EAcM,EAbJ6B,EAAAA,EAAAA,IAKS,UALDnC,MAAM,cAAeqC,QAAKX,EAAA,KAAAA,EAAA,GDsE5C,IAAIY,ICtE0Cb,EAAAqB,QAAArB,EAAAqB,UAAAR,KDuEzBZ,EAAO,KAAOA,EAAO,GAAK,ECtEnCS,EAAAA,EAAAA,IAEM,OAFDY,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SD+E3I,EC9ETf,EAAAA,EAAAA,IAA8C,YAApCgB,OAAO,sBDgFP,IClGxBP,EAAAA,EAAAA,IAmBkB,eAIRT,EAAAA,EAAAA,IAKS,UALDnC,MAAM,kBAAmBqC,QAAKX,EAAA,KAAAA,EAAA,GDkFhD,IAAIY,IClF8Cb,EAAA2B,gBAAA3B,EAAA2B,kBAAAd,IAAiBe,SAAU5B,EAAA6B,iBDoFxD,CCnFE7B,EAAA6B,kBDqFErB,EAAAA,EAAAA,OCrFbC,EAAAA,EAAAA,IAA2D,MAA3D1B,MDsFayB,EAAAA,EAAAA,OCrFbC,EAAAA,EAAAA,IAEM,OA3BlB/B,IAAA,EAyBwB4C,MAAM,6BAA6BC,QAAQ,YAAaC,KAAMxB,EAAA9B,aAAe,eAAiB,OAAQuD,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SD8FjLxB,EAAO,KAAOA,EAAO,GAAK,EC7FvCS,EAAAA,EAAAA,IAAmE,QAA7DoB,EAAE,qDAAmD,WD+F7C,ECzH5B9C,KD0HqB,EC1HrBF,MA+BQ4B,EAAAA,EAAAA,IAAiD,KAAjDzB,GAAiD0B,EAAAA,EAAAA,IAAtBX,EAAAhH,MAAM+I,UAAQ,IACzCrB,EAAAA,EAAAA,IAA6D,IAA7DxB,GAA6DyB,EAAAA,EAAAA,IAA1BX,EAAAhH,MAAMgJ,eAAa,IAEtDtB,EAAAA,EAAAA,IAoBM,MApBNvB,EAoBM,EAnBJuB,EAAAA,EAAAA,IAGM,MAHNtB,EAGM,CD0FIa,EAAO,MAAQA,EAAO,KC5F9BS,EAAAA,EAAAA,IAAoC,QAA9BnC,MAAM,cAAa,QAAI,KAC7BmC,EAAAA,EAAAA,IAAgD,OAAhDrB,GAAgDsB,EAAAA,EAAAA,IAApBX,EAAAhH,MAAMiJ,MAAI,KAG7BjC,EAAAhH,MAAMkJ,WAAalC,EAAAhH,MAAMmJ,cD4FvB3B,EAAAA,EAAAA,OC5FbC,EAAAA,EAAAA,IAQM,MARNnB,EAQM,CDqFQW,EAAO,MAAQA,EAAO,KC5FlCS,EAAAA,EAAAA,IAAwC,QAAlCnC,MAAM,cAAa,YAAQ,KACjCmC,EAAAA,EAAAA,IAKO,OALPnB,EAKO,EA/CnB4B,EAAAA,EAAAA,KAAAR,EAAAA,EAAAA,IA2CiBX,EAAAhH,MAAMkJ,WAAalC,EAAAhH,MAAMmJ,aAAc,IAC1C,GD4FYlC,EAAO,MAAQA,EAAO,KC5FlCS,EAAAA,EAAAA,IAEM,OAFDY,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,eAAeC,OAAO,OAAOlD,MAAM,aDkGrF,ECjGbmC,EAAAA,EAAAA,IAA2H,WAAlHgB,OAAO,qGDmGF,UChJ9BU,EAAAA,EAAAA,IAAA,OAkDqBpC,EAAAhH,MAAMM,ODmGJkH,EAAAA,EAAAA,OCnGbC,EAAAA,EAAAA,IAGM,MAHNjB,EAGM,CDiGQS,EAAO,MAAQA,EAAO,KCnGlCS,EAAAA,EAAAA,IAAoC,QAA9BnC,MAAM,cAAa,QAAI,KAC7BmC,EAAAA,EAAAA,IAA4D,OAA5DjB,GAA4DkB,EAAAA,EAAAA,IAAhCX,EAAAqC,WAAWrC,EAAAhH,MAAMM,OAAI,OApD7D8I,EAAAA,EAAAA,IAAA,SAwDiBpC,EAAAhH,MAAMsJ,oBDoGF9B,EAAAA,EAAAA,OCpGbC,EAAAA,EAAAA,IAEI,IAFJf,GAEIiB,EAAAA,EAAAA,IADCX,EAAAhH,MAAMsJ,mBAAiB,KAzDpCF,EAAAA,EAAAA,IAAA,QA4DQ1B,EAAAA,EAAAA,IAYM,MAZNf,EAYM,CAXOK,EAAAuC,gBDmGE/B,EAAAA,EAAAA,OCnGbC,EAAAA,EAAAA,IAGM,MAHNb,EAGMK,EAAA,MAAAA,EAAA,MAFJS,EAAAA,EAAAA,IAAmC,OAA9BnC,MAAM,mBAAiB,UAC5BmC,EAAAA,EAAAA,IAAyB,SAAtB,sBAAkB,QA/DjC0B,EAAAA,EAAAA,IAAA,OAkEkBpC,EAAAhH,MAAMwJ,aDoGDhC,EAAAA,EAAAA,OCrGbC,EAAAA,EAAAA,IAMU,UAvEpB/B,IAAA,EAmEa+D,IAAKzC,EAAAhH,MAAMwJ,WACZE,YAAY,IACZC,gBAAA,GACCC,OAAI3C,EAAA,KAAAA,EAAA,GDuGjB,IAAIY,ICvGeb,EAAA6C,cAAA7C,EAAA6C,gBAAAhC,KDwGM,KAAM,GC9K/BhB,KAAAuC,EAAAA,EAAAA,IAAA,UA0EQ1B,EAAAA,EAAAA,IAeM,MAfNZ,EAeM,EAdJY,EAAAA,EAAAA,IAES,UAFDnC,MAAM,eAAgBqC,QAAKX,EAAA,KAAAA,EAAA,GD2G7C,IAAIY,IC3G2Cb,EAAA8C,gBAAA9C,EAAA8C,kBAAAjC,KAAgB,yBAIrDH,EAAAA,EAAAA,IASS,UATDnC,MAAM,eAAgBqC,QAAKX,EAAA,KAAAA,EAAA,GD6G7C,IAAIY,IC7G2Cb,EAAA+C,YAAA/C,EAAA+C,cAAAlC,KD8G1BZ,EAAO,MAAQA,EAAO,IAAM,EC7LjD+C,EAAAA,EAAAA,IAAA,igBAAAZ,EAAAA,EAAAA,IAAA,UDqMA,C,iDC/FA,SAAea,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,YACNC,KAAAA,GACE,MAAMC,GAAQC,EAAAA,EAAAA,MACRC,GAASC,EAAAA,EAAAA,MACTC,GAAcxI,EAAAA,EAAAA,KACdyI,GAAiB5G,EAAAA,EAAAA,KACjB0F,GAAgBmB,EAAAA,EAAAA,KAAI,GACpB7B,GAAkB6B,EAAAA,EAAAA,KAAI,GAGtBtK,GAASuK,EAAAA,EAAAA,KAAS,IAAMP,EAAM1I,OAAOtB,SACrCyB,GAAK8I,EAAAA,EAAAA,KAAS,IAAMP,EAAM1I,OAAOG,KAGjC7B,GAAQ2K,EAAAA,EAAAA,KAAS,IAAMH,EAAYrI,eACnCC,GAAUuI,EAAAA,EAAAA,KAAS,IAAMH,EAAYpI,UACrCC,GAAQsI,EAAAA,EAAAA,KAAS,IAAMH,EAAYnI,QAGnC6C,GAAeyF,EAAAA,EAAAA,KAAS,KAC5B,IAAK3K,EAAM4K,MAAO,OAAO,EAGzB,MAAMC,EAA2B,OAAjBzK,EAAOwK,MAAiB5K,EAAM4K,MAAM3K,MAAQD,EAAM4K,MAAM1K,QAExE,SAAKE,EAAOwK,QAAUC,IACfJ,EAAevF,aAAa2F,EAASzK,EAAOwK,MAAuB,IAItE9C,EAAagD,UACb1K,EAAOwK,OAAS/I,EAAG+I,aACfJ,EAAY7H,eAAevC,EAAOwK,MAAO/I,EAAG+I,MACpD,EAMIjC,EAAiBmC,UACrB,IAAK9K,EAAM4K,QAAUxK,EAAOwK,MAAO,OAGnC,MAAMC,EAA2B,OAAjBzK,EAAOwK,MAAiB5K,EAAM4K,MAAM3K,MAAQD,EAAM4K,MAAM1K,QAExE,GAAK2K,EAAL,CAEAhC,EAAgB+B,OAAQ,EAExB,IACE,GAAI1F,EAAa0F,MAAO,CACtB,MAAMG,EAAaN,EAAerF,cAAcyF,EAASzK,EAAOwK,OAC5DG,SACIN,EAAe3F,eAAeiG,EAExC,YACQN,EAAepG,YAAYwG,EAASzK,EAAOwK,MAErD,CAAE,MAAOvI,GACPe,QAAQf,MAAM,2BAA4BA,EAC5C,CAAE,QACAwG,EAAgB+B,OAAQ,CAC1B,CAjBoB,CAiBpB,EAIId,EAAiBA,KAChB9J,EAAM4K,OAAOpB,aAGd5G,OAAOC,UAAUC,QAAQkI,SAC3BpI,OAAOC,SAASC,OAAOkI,SAAShL,EAAM4K,MAAMpB,YAE5C5G,OAAOqI,KAAKjL,EAAM4K,MAAMpB,WAAY,UACtC,EAIIO,EAAaA,KACjB,IAAK/J,EAAM4K,MAAO,OAElB,MAAMM,EAAcC,iBACdC,EAAW,gBAAgBF,kBAA4B9K,EAAOwK,SAAS/I,EAAG+I,QAC1ES,EAAY,sBAAsBrL,EAAM4K,MAAM7B,WAE9CuC,EAAmB,8BAA8BC,mBAAmBH,WAAkBG,mBAAmBF,KAG3GzI,OAAOC,UAAUC,QAAQkI,SAC3BpI,OAAOC,SAASC,OAAOkI,SAASM,GAEhC1I,OAAOqI,KAAKK,EAAkB,SAChC,EAIIjD,EAASA,KACbiC,EAAOkB,MAAM,EAIT3B,EAAeA,KACnBN,EAAcqB,OAAQ,CAAK,EAY7B,OATAa,EAAAA,EAAAA,KAAUX,gBAGFL,EAAezG,uBAGf8D,GAAY,IAGb,CACL9H,QACAoC,UACAC,QACA6C,eACAqE,gBACAV,kBACAf,aACAuB,WAAYhJ,EAAAA,GACZsI,iBACAmB,iBACAC,aACA1B,SACAwB,eAEJ,I,aCjOF,MAAM6B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS3E,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://cinema-bot-frontend/./src/utils/movieUtils.ts", "webpack://cinema-bot-frontend/./src/services/vibixService.ts", "webpack://cinema-bot-frontend/./src/store/movies.ts", "webpack://cinema-bot-frontend/./src/store/bookmarks.ts", "webpack://cinema-bot-frontend/./src/views/MovieView.vue?e088", "webpack://cinema-bot-frontend/./src/views/MovieView.vue", "webpack://cinema-bot-frontend/./src/views/MovieView.vue?fdef"], "sourcesContent": ["// Импорт не используется, но оставлен для документации\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport { Movie } from '@/services/vibixService';\n\n/**\n * Определяет источник фильма (Кинопоиск или IMDB)\n * @param movie Объект фильма\n * @returns 'kp', 'imdb' или null, если источник не определен\n */\nexport function getMovieSource(movie: any | null): 'kp' | 'imdb' | null {\n  if (!movie) return null;\n\n  if (movie.kp_id) return 'kp';\n  if (movie.imdb_id) return 'imdb';\n\n  return null;\n}\n\n/**\n * Получает ID фильма в зависимости от источника\n * @param movie Объект фильма\n * @returns ID фильма или null, если ID не найден\n */\nexport function getMovieId(movie: any | null): string | null {\n  if (!movie) return null;\n\n  const source = getMovieSource(movie);\n\n  if (source === 'kp') return movie.kp_id || null;\n  if (source === 'imdb') return movie.imdb_id || null;\n\n  return null;\n}\n\n/**\n * Форматирует тип фильма для отображения\n * @param type Тип фильма\n * @returns Отформатированный тип фильма\n */\nexport function formatMovieType(type: string): string {\n  const types: Record<string, string> = {\n    movie: 'Фильм',\n    tv: 'Сериал',\n    anime: 'Аниме',\n    'tv-series': 'Сериал',\n    'anime-series': 'Аниме-сериал'\n  };\n\n  return types[type] || type;\n}\n\n/**\n * Получает URL постера фильма с приоритетом TMDB\n * @param movie Объект фильма\n * @returns URL постера или null, если постер отсутствует\n */\nexport function getMoviePosterUrl(movie: any | null): string | null {\n  if (!movie) return null;\n\n  // Приоритет: TMDB w500 > оригинальный poster_url > null\n  if (movie.poster_urls?.w500) {\n    return movie.poster_urls.w500;\n  }\n\n  return movie.poster_url || null;\n}\n", "import api from './api';\n\n// Define movie interface based on Vibix API response\nexport interface Movie {\n  id: string;\n  name_rus: string;\n  name_original: string;\n  iframe_url: string;\n  type: string;\n  year: number;\n  kp_id?: string;\n  imdb_id?: string;\n  kp_rating?: number;\n  imdb_rating?: number;\n  description_short?: string;\n  poster_url?: string;\n  // Новые поля от TMDB кеширования\n  poster_urls?: {\n    w500: string;\n    original: string;\n  };\n  has_poster?: boolean;\n}\n\n// Define pagination interface\nexport interface PaginationLinks {\n  first: string;\n  last: string;\n  prev: string | null;\n  next: string | null;\n}\n\n// Define Vibix API response interface\nexport interface VibixResponse {\n  data: Movie[];\n  links: PaginationLinks;\n  meta: {\n    current_page: number;\n    from: number;\n    last_page: number;\n    path: string;\n    per_page: number;\n    to: number;\n    total: number;\n  };\n  success: boolean;\n  message: string;\n  status: number;\n}\n\n// Define movie categories\nexport enum MovieCategory {\n  POPULAR = 'popular',\n  NEW = 'new',\n  THRILLER = 'thriller',\n  DRAMA = 'drama',\n  ACTION = 'action',\n  SCIFI = 'scifi',\n  DETECTIVE = 'detective'\n}\n\nclass VibixService {\n  private baseUrl: string;\n\n  constructor() {\n    // Используем полный URL бэкенда для запросов\n    this.baseUrl = 'https://v2test.appkinobot.com/api/vibix';\n  }\n\n  // Get movies by category\n  async getMoviesByCategory(category: MovieCategory, page: number = 1, limit: number = 20): Promise<VibixResponse> {\n    // Используем правильный эндпоинт для бэкенда\n    const response = await api.get(`${this.baseUrl}/movies/category/${category}`, {\n      params: {\n        limit,\n        page\n      }\n    });\n    return response.data;\n  }\n\n  // Get movie by ID\n  async getMovie(id: string): Promise<Movie> {\n    const response = await api.get(`${this.baseUrl}/movies/${id}`);\n    return response.data;\n  }\n\n  // Search movies by URL\n  async searchByUrl(url: string): Promise<Movie | null> {\n    const response = await api.get(`${this.baseUrl}/search`, {\n      params: { url }\n    });\n    return response.data;\n  }\n}\n\nexport default new VibixService();\n", "import { defineStore } from 'pinia';\nimport api from '@/services/api';\nimport vibixService, { Movie as VibixMovie, MovieCategory } from '@/services/vibixService';\n\ninterface Movie {\n  id: string;\n  name_rus: string;\n  name_original: string;\n  iframe_url: string;\n  type: string;\n  year: number;\n  kp_id?: string;\n  imdb_id?: string;\n  kp_rating?: number;\n  imdb_rating?: number;\n  description_short?: string;\n}\n\nexport const useMoviesStore = defineStore('movies', {\n  state: () => ({\n    currentMovie: null as Movie | null,\n    loading: false,\n    error: null as string | null,\n    // Добавляем хранилище для категорий фильмов\n    categoryMovies: {} as Record<MovieCategory, VibixMovie[]>,\n    categoryLoading: {} as Record<MovieCategory, boolean>,\n    categoryError: {} as Record<MovieCategory, string | null>,\n    // Добавляем отслеживание запросов в процессе выполнения\n    pendingRequests: {} as Record<string, Promise<VibixMovie[]> | null>\n  }),\n\n  actions: {\n    async fetchMovieById(source: string, id: string) {\n      this.loading = true;\n      this.error = null;\n      this.currentMovie = null;\n\n      // Проверяем наличие Telegram WebApp\n      if (!window.Telegram || !window.Telegram.WebApp) {\n        this.error = 'Telegram WebApp is not available';\n        this.loading = false;\n        return;\n      }\n\n      try {\n        const response = await api.get(`/vibix/movies/${id}`, {\n          params: { source }\n        });\n\n        if (response.data) {\n          this.currentMovie = response.data;\n        } else {\n          this.error = 'Failed to load movie';\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async searchMovie(url: string) {\n      this.loading = true;\n      this.error = null;\n      this.currentMovie = null;\n\n      // Проверяем наличие Telegram WebApp\n      if (!window.Telegram || !window.Telegram.WebApp) {\n        this.error = 'Telegram WebApp is not available';\n        this.loading = false;\n        return null;\n      }\n\n      try {\n        const response = await api.get('/vibix/search', {\n          params: { url }\n        });\n\n        if (response.data.success) {\n          this.currentMovie = response.data.data;\n          return response.data.data;\n        } else {\n          this.error = response.data.message || 'Failed to search movie';\n          return null;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n\n        return null;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    setSearchQuery(query: string) {\n      // Функция оставлена для совместимости, но searchQuery больше не используется\n      console.log('Search query set:', query);\n    },\n\n    clearCurrentMovie() {\n      this.currentMovie = null;\n    },\n\n    // Метод для загрузки фильмов по категории\n    async fetchMoviesByCategory(category: MovieCategory, page: number = 1, limit: number = 20) {\n      const requestKey = `${category}_${page}_${limit}`;\n\n      // Устанавливаем состояние загрузки для категории\n      this.categoryLoading = { ...this.categoryLoading, [category]: true };\n      this.categoryError = { ...this.categoryError, [category]: null };\n\n      try {\n        const response = await vibixService.getMoviesByCategory(category, page, limit);\n\n        // Проверяем наличие данных\n        if (response.data?.length > 0) {\n          // Если это первая страница, заменяем существующие фильмы\n          // Если не первая, то не обновляем кэш категории, так как он содержит только первую страницу\n          if (page === 1) {\n            this.categoryMovies[category] = response.data;\n          }\n          return response.data;\n        } else {\n          // Устанавливаем ошибку, если данных нет\n          this.categoryError[category] = 'Не удалось загрузить фильмы для категории';\n          return [];\n        }\n      } catch (error: any) {\n        // Обрабатываем ошибку\n        this.categoryError[category] = error.message || 'Ошибка при загрузке фильмов';\n        return [];\n      } finally {\n        // Завершаем загрузку и удаляем запрос из отслеживаемых\n        this.categoryLoading[category] = false;\n        delete this.pendingRequests[requestKey];\n      }\n    },\n\n    // Получить фильмы для категории (из кэша или загрузить)\n    async getMoviesByCategory(\n      category: MovieCategory,\n      page: number = 1,\n      limit: number = 20,\n      useCache: boolean = true\n    ) {\n      const requestKey = `${category}_${page}_${limit}`;\n\n      // Если фильмы для этой категории уже загружены и нужно использовать кэш\n      if (useCache && page === 1 && this.categoryMovies[category]?.length > 0) {\n        return this.categoryMovies[category];\n      }\n\n      // Если запрос уже выполняется, ждем его завершения\n      if (this.pendingRequests[requestKey]) {\n        return await this.pendingRequests[requestKey]!;\n      }\n\n      // Создаем новый запрос и сохраняем его\n      const request = this.fetchMoviesByCategory(category, page, limit);\n      this.pendingRequests[requestKey] = request;\n\n      return await request;\n    }\n  }\n});\n", "import { defineStore } from 'pinia';\nimport api from '@/services/api';\n\ninterface MovieDetails {\n  id: string;\n  name_rus: string;\n  name_original: string;\n  iframe_url: string;\n  type: string;\n  year: number;\n  kp_id?: string;\n  imdb_id?: string;\n  kp_rating?: number;\n  imdb_rating?: number;\n  description_short?: string;\n  poster_url?: string;\n}\n\ninterface Bookmark {\n  _id: string;\n  source: 'kp' | 'imdb';\n  details: MovieDetails | null;\n}\n\nexport const useBookmarksStore = defineStore('bookmarks', {\n  state: () => ({\n    bookmarks: [] as Bookmark[],\n    loading: false,\n    error: null as string | null,\n    lastFetchTime: 0 // Время последней загрузки закладок\n  }),\n\n  actions: {\n    async fetchBookmarks(forceRefresh = false) {\n      // Если закладки уже загружены и прошло менее 5 минут с последней загрузки, не загружаем их снова\n      const now = Date.now();\n      const fiveMinutes = 5 * 60 * 1000; // 5 минут в миллисекундах\n\n      if (!forceRefresh &&\n          this.bookmarks.length > 0 &&\n          now - this.lastFetchTime < fiveMinutes) {\n        return;\n      }\n\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.get('/bookmarks');\n\n        if (response.data.success) {\n          this.bookmarks = response.data.data;\n          this.lastFetchTime = now;\n        } else {\n          this.error = response.data.message || 'Failed to load bookmarks';\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error fetching bookmarks:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async addBookmark(movie_id: string, source: 'kp' | 'imdb') {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.post('/bookmarks', { movie_id, source });\n\n        if (response.data.success) {\n          // Получаем детали фильма для добавления в state\n          let movieDetails;\n          try {\n            if (source === 'kp') {\n              // Используем существующий API для получения деталей фильма\n              const movieResponse = await api.get(`/vibix/movies/${movie_id}?source=kp`);\n              movieDetails = movieResponse.data;\n            } else {\n              const movieResponse = await api.get(`/vibix/movies/${movie_id}?source=imdb`);\n              movieDetails = movieResponse.data;\n            }\n\n            // Добавляем новую закладку в state\n            this.bookmarks.unshift({\n              _id: response.data.data._id,\n              source: source,\n              details: movieDetails\n            });\n          } catch (movieError) {\n            console.error('Error fetching movie details:', movieError);\n            // Если не удалось получить детали фильма, все равно добавляем закладку\n            this.bookmarks.unshift({\n              _id: response.data.data._id,\n              source: source,\n              details: null\n            });\n          }\n\n          return true;\n        } else {\n          this.error = response.data.message || 'Failed to add bookmark';\n          return false;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error adding bookmark:', error);\n        return false;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async deleteBookmark(id: string) {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.delete(`/bookmarks/${id}`);\n\n        if (response.data.success) {\n          // Remove bookmark from state\n          this.bookmarks = this.bookmarks.filter(bookmark => bookmark._id !== id);\n          return true;\n        } else {\n          this.error = response.data.message || 'Failed to delete bookmark';\n          return false;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error deleting bookmark:', error);\n        return false;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    isBookmarked(movie_id: string, source: 'kp' | 'imdb'): boolean {\n      return this.bookmarks.some(bookmark => {\n        if (!bookmark.details) return false;\n\n        if (source === 'kp') {\n          return bookmark.details.kp_id === movie_id && bookmark.source === source;\n        } else if (source === 'imdb') {\n          return bookmark.details.imdb_id === movie_id && bookmark.source === source;\n        }\n\n        return false;\n      });\n    },\n\n    getBookmarkId(movie_id: string, source: 'kp' | 'imdb'): string | null {\n      const bookmark = this.bookmarks.find(bookmark => {\n        if (!bookmark.details) return false;\n\n        if (source === 'kp') {\n          return bookmark.details.kp_id === movie_id && bookmark.source === source;\n        } else if (source === 'imdb') {\n          return bookmark.details.imdb_id === movie_id && bookmark.source === source;\n        }\n\n        return false;\n      });\n\n      return bookmark ? bookmark._id : null;\n    }\n  }\n});\n", "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createStaticVNode as _createStaticVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"movie-view\" }\nconst _hoisted_2 = { class: \"container\" }\nconst _hoisted_3 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_4 = {\n  key: 1,\n  class: \"error-container\"\n}\nconst _hoisted_5 = {\n  key: 2,\n  class: \"movie-details\"\n}\nconst _hoisted_6 = { class: \"movie-header\" }\nconst _hoisted_7 = [\"disabled\"]\nconst _hoisted_8 = {\n  key: 0,\n  class: \"bookmark-loading\"\n}\nconst _hoisted_9 = [\"fill\"]\nconst _hoisted_10 = { class: \"movie-title\" }\nconst _hoisted_11 = { class: \"movie-original-title\" }\nconst _hoisted_12 = { class: \"movie-meta\" }\nconst _hoisted_13 = { class: \"meta-item\" }\nconst _hoisted_14 = { class: \"meta-value\" }\nconst _hoisted_15 = {\n  key: 0,\n  class: \"meta-item\"\n}\nconst _hoisted_16 = { class: \"meta-value rating\" }\nconst _hoisted_17 = {\n  key: 1,\n  class: \"meta-item\"\n}\nconst _hoisted_18 = { class: \"meta-value\" }\nconst _hoisted_19 = {\n  key: 0,\n  class: \"movie-description\"\n}\nconst _hoisted_20 = { class: \"iframe-container\" }\nconst _hoisted_21 = {\n  key: 0,\n  class: \"iframe-loading\"\n}\nconst _hoisted_22 = [\"src\"]\nconst _hoisted_23 = { class: \"movie-actions\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_router_link = _resolveComponent(\"router-link\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      (_ctx.loading)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[6] || (_cache[6] = [\n            _createElementVNode(\"div\", { class: \"loading-spinner large\" }, null, -1),\n            _createElementVNode(\"p\", null, \"Загрузка фильма...\", -1)\n          ])))\n        : (_ctx.error)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n              _createElementVNode(\"p\", null, _toDisplayString(_ctx.error), 1),\n              _createElementVNode(\"button\", {\n                onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.fetchMovie && _ctx.fetchMovie(...args))),\n                class: \"retry-button\"\n              }, \"Повторить\"),\n              _createVNode(_component_router_link, {\n                to: \"/catalog\",\n                class: \"back-button\"\n              }, {\n                default: _withCtx(() => _cache[7] || (_cache[7] = [\n                  _createTextVNode(\"Вернуться в каталог\")\n                ])),\n                _: 1\n              })\n            ]))\n          : (_ctx.movie)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                _createElementVNode(\"div\", _hoisted_6, [\n                  _createElementVNode(\"button\", {\n                    class: \"back-button\",\n                    onClick: _cache[1] || (_cache[1] = \n//@ts-ignore\n(...args) => (_ctx.goBack && _ctx.goBack(...args)))\n                  }, _cache[8] || (_cache[8] = [\n                    _createElementVNode(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      \"stroke-width\": \"2\",\n                      \"stroke-linecap\": \"round\",\n                      \"stroke-linejoin\": \"round\"\n                    }, [\n                      _createElementVNode(\"polyline\", { points: \"15 18 9 12 15 6\" })\n                    ], -1),\n                    _createTextVNode(\" Назад \")\n                  ])),\n                  _createElementVNode(\"button\", {\n                    class: \"bookmark-button\",\n                    onClick: _cache[2] || (_cache[2] = \n//@ts-ignore\n(...args) => (_ctx.toggleBookmark && _ctx.toggleBookmark(...args))),\n                    disabled: _ctx.bookmarkLoading\n                  }, [\n                    (_ctx.bookmarkLoading)\n                      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8))\n                      : (_openBlock(), _createElementBlock(\"svg\", {\n                          key: 1,\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: _ctx.isBookmarked ? 'currentColor' : 'none',\n                          stroke: \"currentColor\",\n                          \"stroke-width\": \"2\",\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\"\n                        }, _cache[9] || (_cache[9] = [\n                          _createElementVNode(\"path\", { d: \"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\" }, null, -1)\n                        ]), 8, _hoisted_9))\n                  ], 8, _hoisted_7)\n                ]),\n                _createElementVNode(\"h1\", _hoisted_10, _toDisplayString(_ctx.movie.name_rus), 1),\n                _createElementVNode(\"p\", _hoisted_11, _toDisplayString(_ctx.movie.name_original), 1),\n                _createElementVNode(\"div\", _hoisted_12, [\n                  _createElementVNode(\"div\", _hoisted_13, [\n                    _cache[10] || (_cache[10] = _createElementVNode(\"span\", { class: \"meta-label\" }, \"Год:\", -1)),\n                    _createElementVNode(\"span\", _hoisted_14, _toDisplayString(_ctx.movie.year), 1)\n                  ]),\n                  (_ctx.movie.kp_rating || _ctx.movie.imdb_rating)\n                    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [\n                        _cache[12] || (_cache[12] = _createElementVNode(\"span\", { class: \"meta-label\" }, \"Рейтинг:\", -1)),\n                        _createElementVNode(\"span\", _hoisted_16, [\n                          _createTextVNode(_toDisplayString(_ctx.movie.kp_rating || _ctx.movie.imdb_rating) + \" \", 1),\n                          _cache[11] || (_cache[11] = _createElementVNode(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"currentColor\",\n                            stroke: \"none\",\n                            class: \"star-icon\"\n                          }, [\n                            _createElementVNode(\"polygon\", { points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\" })\n                          ], -1))\n                        ])\n                      ]))\n                    : _createCommentVNode(\"\", true),\n                  (_ctx.movie.type)\n                    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [\n                        _cache[13] || (_cache[13] = _createElementVNode(\"span\", { class: \"meta-label\" }, \"Тип:\", -1)),\n                        _createElementVNode(\"span\", _hoisted_18, _toDisplayString(_ctx.formatType(_ctx.movie.type)), 1)\n                      ]))\n                    : _createCommentVNode(\"\", true)\n                ]),\n                (_ctx.movie.description_short)\n                  ? (_openBlock(), _createElementBlock(\"p\", _hoisted_19, _toDisplayString(_ctx.movie.description_short), 1))\n                  : _createCommentVNode(\"\", true),\n                _createElementVNode(\"div\", _hoisted_20, [\n                  (_ctx.iframeLoading)\n                    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, _cache[14] || (_cache[14] = [\n                        _createElementVNode(\"div\", { class: \"loading-spinner\" }, null, -1),\n                        _createElementVNode(\"p\", null, \"Загрузка плеера...\", -1)\n                      ])))\n                    : _createCommentVNode(\"\", true),\n                  (_ctx.movie.iframe_url)\n                    ? (_openBlock(), _createElementBlock(\"iframe\", {\n                        key: 1,\n                        src: _ctx.movie.iframe_url,\n                        frameborder: \"0\",\n                        allowfullscreen: \"\",\n                        onLoad: _cache[3] || (_cache[3] = \n//@ts-ignore\n(...args) => (_ctx.iframeLoaded && _ctx.iframeLoaded(...args)))\n                      }, null, 40, _hoisted_22))\n                    : _createCommentVNode(\"\", true)\n                ]),\n                _createElementVNode(\"div\", _hoisted_23, [\n                  _createElementVNode(\"button\", {\n                    class: \"watch-button\",\n                    onClick: _cache[4] || (_cache[4] = \n//@ts-ignore\n(...args) => (_ctx.openInTelegram && _ctx.openInTelegram(...args)))\n                  }, \" Открыть в браузере \"),\n                  _createElementVNode(\"button\", {\n                    class: \"share-button\",\n                    onClick: _cache[5] || (_cache[5] = \n//@ts-ignore\n(...args) => (_ctx.shareMovie && _ctx.shareMovie(...args)))\n                  }, _cache[15] || (_cache[15] = [\n                    _createStaticVNode(\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" data-v-c1e389f2><circle cx=\\\"18\\\" cy=\\\"5\\\" r=\\\"3\\\" data-v-c1e389f2></circle><circle cx=\\\"6\\\" cy=\\\"12\\\" r=\\\"3\\\" data-v-c1e389f2></circle><circle cx=\\\"18\\\" cy=\\\"19\\\" r=\\\"3\\\" data-v-c1e389f2></circle><line x1=\\\"8.59\\\" y1=\\\"13.51\\\" x2=\\\"15.42\\\" y2=\\\"17.49\\\" data-v-c1e389f2></line><line x1=\\\"15.41\\\" y1=\\\"6.51\\\" x2=\\\"8.59\\\" y2=\\\"10.49\\\" data-v-c1e389f2></line></svg> Поделиться \", 2)\n                  ]))\n                ])\n              ]))\n            : _createCommentVNode(\"\", true)\n    ])\n  ]))\n}", "<template>\n  <div class=\"movie-view\">\n    <div class=\"container\">\n      <div v-if=\"loading\" class=\"loading-container\">\n        <div class=\"loading-spinner large\"></div>\n        <p>Загрузка фильма...</p>\n      </div>\n\n      <div v-else-if=\"error\" class=\"error-container\">\n        <p>{{ error }}</p>\n        <button @click=\"fetchMovie\" class=\"retry-button\">Повторить</button>\n        <router-link to=\"/catalog\" class=\"back-button\">Вернуться в каталог</router-link>\n      </div>\n\n      <div v-else-if=\"movie\" class=\"movie-details\">\n        <div class=\"movie-header\">\n          <button class=\"back-button\" @click=\"goBack\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <polyline points=\"15 18 9 12 15 6\"></polyline>\n            </svg>\n            Назад\n          </button>\n\n          <button class=\"bookmark-button\" @click=\"toggleBookmark\" :disabled=\"bookmarkLoading\">\n            <div v-if=\"bookmarkLoading\" class=\"bookmark-loading\"></div>\n            <svg v-else xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" :fill=\"isBookmarked ? 'currentColor' : 'none'\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\"></path>\n            </svg>\n          </button>\n        </div>\n\n        <h1 class=\"movie-title\">{{ movie.name_rus }}</h1>\n        <p class=\"movie-original-title\">{{ movie.name_original }}</p>\n\n        <div class=\"movie-meta\">\n          <div class=\"meta-item\">\n            <span class=\"meta-label\">Год:</span>\n            <span class=\"meta-value\">{{ movie.year }}</span>\n          </div>\n\n          <div v-if=\"movie.kp_rating || movie.imdb_rating\" class=\"meta-item\">\n            <span class=\"meta-label\">Рейтинг:</span>\n            <span class=\"meta-value rating\">\n              {{ movie.kp_rating || movie.imdb_rating }}\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"currentColor\" stroke=\"none\" class=\"star-icon\">\n                <polygon points=\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"></polygon>\n              </svg>\n            </span>\n          </div>\n\n          <div v-if=\"movie.type\" class=\"meta-item\">\n            <span class=\"meta-label\">Тип:</span>\n            <span class=\"meta-value\">{{ formatType(movie.type) }}</span>\n          </div>\n        </div>\n\n        <p v-if=\"movie.description_short\" class=\"movie-description\">\n          {{ movie.description_short }}\n        </p>\n\n        <div class=\"iframe-container\">\n          <div v-if=\"iframeLoading\" class=\"iframe-loading\">\n            <div class=\"loading-spinner\"></div>\n            <p>Загрузка плеера...</p>\n          </div>\n          <iframe\n            v-if=\"movie.iframe_url\"\n            :src=\"movie.iframe_url\"\n            frameborder=\"0\"\n            allowfullscreen\n            @load=\"iframeLoaded\"\n          ></iframe>\n        </div>\n\n        <div class=\"movie-actions\">\n          <button class=\"watch-button\" @click=\"openInTelegram\">\n            Открыть в браузере\n          </button>\n\n          <button class=\"share-button\" @click=\"shareMovie\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <circle cx=\"18\" cy=\"5\" r=\"3\"></circle>\n              <circle cx=\"6\" cy=\"12\" r=\"3\"></circle>\n              <circle cx=\"18\" cy=\"19\" r=\"3\"></circle>\n              <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\"></line>\n              <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\"></line>\n            </svg>\n            Поделиться\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, onMounted, computed, ref } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nimport { useMoviesStore } from '@/store/movies';\nimport { useBookmarksStore } from '@/store/bookmarks';\nimport { formatMovieType } from '@/utils/movieUtils';\n\nexport default defineComponent({\n  name: 'MovieView',\n  setup() {\n    const route = useRoute();\n    const router = useRouter();\n    const moviesStore = useMoviesStore();\n    const bookmarksStore = useBookmarksStore();\n    const iframeLoading = ref(true);\n    const bookmarkLoading = ref(false);\n\n    // Get source and ID from route params\n    const source = computed(() => route.params.source as string);\n    const id = computed(() => route.params.id as string);\n\n    // Computed properties\n    const movie = computed(() => moviesStore.currentMovie);\n    const loading = computed(() => moviesStore.loading);\n    const error = computed(() => moviesStore.error);\n\n    // Check if movie is bookmarked\n    const isBookmarked = computed(() => {\n      if (!movie.value) return false;\n\n      // Используем ID из объекта фильма, а не из параметров маршрута\n      const movieId = source.value === 'kp' ? movie.value.kp_id : movie.value.imdb_id;\n\n      if (!source.value || !movieId) return false;\n      return bookmarksStore.isBookmarked(movieId, source.value as 'kp' | 'imdb');\n    });\n\n    // Fetch movie details\n    const fetchMovie = async () => {\n      if (source.value && id.value) {\n        await moviesStore.fetchMovieById(source.value, id.value);\n      }\n    };\n\n\n\n    // Toggle bookmark\n    const toggleBookmark = async () => {\n      if (!movie.value || !source.value) return;\n\n      // Используем ID из объекта фильма, а не из параметров маршрута\n      const movieId = source.value === 'kp' ? movie.value.kp_id : movie.value.imdb_id;\n\n      if (!movieId) return;\n\n      bookmarkLoading.value = true;\n\n      try {\n        if (isBookmarked.value) {\n          const bookmarkId = bookmarksStore.getBookmarkId(movieId, source.value as 'kp' | 'imdb');\n          if (bookmarkId) {\n            await bookmarksStore.deleteBookmark(bookmarkId);\n          }\n        } else {\n          await bookmarksStore.addBookmark(movieId, source.value as 'kp' | 'imdb');\n        }\n      } catch (error) {\n        console.error('Error toggling bookmark:', error);\n      } finally {\n        bookmarkLoading.value = false;\n      }\n    };\n\n    // Open movie in Telegram\n    const openInTelegram = () => {\n      if (!movie.value?.iframe_url) return;\n\n      // Open link using Telegram Web App\n      if (window.Telegram?.WebApp?.openLink) {\n        window.Telegram.WebApp.openLink(movie.value.iframe_url);\n      } else {\n        window.open(movie.value.iframe_url, '_blank');\n      }\n    };\n\n    // Share movie\n    const shareMovie = () => {\n      if (!movie.value) return;\n\n      const botUsername = process.env.VUE_APP_TELEGRAM_BOT_USERNAME || 'your_bot';\n      const shareUrl = `https://t.me/${botUsername}?startapp=see_${source.value}_${id.value}`;\n      const shareText = `Смотри бесплатно!\\n${movie.value.name_rus}`;\n\n      const telegramShareUrl = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`;\n\n      // Open share URL\n      if (window.Telegram?.WebApp?.openLink) {\n        window.Telegram.WebApp.openLink(telegramShareUrl);\n      } else {\n        window.open(telegramShareUrl, '_blank');\n      }\n    };\n\n    // Go back\n    const goBack = () => {\n      router.back();\n    };\n\n    // Iframe loaded\n    const iframeLoaded = () => {\n      iframeLoading.value = false;\n    };\n\n    onMounted(async () => {\n      // Fetch bookmarks first to know if movie is bookmarked\n      // Всегда загружаем закладки для актуальности данных\n      await bookmarksStore.fetchBookmarks();\n\n      // Then fetch movie details\n      await fetchMovie();\n    });\n\n    return {\n      movie,\n      loading,\n      error,\n      isBookmarked,\n      iframeLoading,\n      bookmarkLoading,\n      fetchMovie,\n      formatType: formatMovieType,\n      toggleBookmark,\n      openInTelegram,\n      shareMovie,\n      goBack,\n      iframeLoaded\n    };\n  }\n});\n</script>\n\n<style scoped>\n.movie-view {\n  padding-bottom: 80px;\n}\n\n.loading-container, .error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-xl) 0;\n  color: var(--tg-theme-hint-color);\n}\n\n.loading-spinner {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  border: 2px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top-color: var(--tg-theme-button-color);\n  animation: spin 1s ease-in-out infinite;\n  margin-bottom: var(--spacing-md);\n}\n\n.loading-spinner.large {\n  width: 48px;\n  height: 48px;\n  border-width: 3px;\n}\n\n.retry-button, .back-button {\n  margin-top: var(--spacing-md);\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  border: none;\n  border-radius: var(--border-radius);\n  padding: var(--spacing-sm) var(--spacing-md);\n  font-size: var(--font-size-md);\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n}\n\n.back-button {\n  margin-right: var(--spacing-md);\n}\n\n.movie-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--spacing-md);\n}\n\n.movie-header .back-button {\n  display: flex;\n  align-items: center;\n  background: none;\n  color: var(--tg-theme-text-color);\n  padding: var(--spacing-xs);\n  margin: 0;\n}\n\n.movie-header .back-button svg {\n  width: 20px;\n  height: 20px;\n  margin-right: var(--spacing-xs);\n}\n\n.bookmark-button {\n  background: none;\n  border: none;\n  color: var(--tg-theme-button-color);\n  width: 32px;\n  height: 32px;\n  padding: 0;\n  cursor: pointer;\n}\n\n.bookmark-button svg {\n  width: 100%;\n  height: 100%;\n}\n\n.bookmark-loading {\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top-color: var(--tg-theme-button-color);\n  animation: spin 1s ease-in-out infinite;\n  margin: auto;\n}\n\n.movie-title {\n  font-size: 24px;\n  font-weight: bold;\n  margin-bottom: var(--spacing-xs);\n}\n\n.movie-original-title {\n  font-size: var(--font-size-md);\n  color: var(--tg-theme-hint-color);\n  margin-bottom: var(--spacing-md);\n}\n\n.movie-meta {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: var(--spacing-md);\n}\n\n.meta-item {\n  margin-right: var(--spacing-md);\n  margin-bottom: var(--spacing-xs);\n}\n\n.meta-label {\n  color: var(--tg-theme-hint-color);\n  margin-right: var(--spacing-xs);\n}\n\n.meta-value {\n  font-weight: bold;\n}\n\n.meta-value.rating {\n  display: inline-flex;\n  align-items: center;\n  color: #ffc107;\n}\n\n.star-icon {\n  width: 16px;\n  height: 16px;\n  margin-left: 2px;\n}\n\n.movie-description {\n  margin-bottom: var(--spacing-md);\n  line-height: 1.5;\n}\n\n.iframe-container {\n  position: relative;\n  width: 100%;\n  padding-top: 56.25%; /* 16:9 Aspect Ratio */\n  margin-bottom: var(--spacing-md);\n  background-color: #000;\n  border-radius: var(--border-radius);\n  overflow: hidden;\n}\n\n.iframe-loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n.iframe-container iframe {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border: none;\n}\n\n.movie-actions {\n  display: flex;\n  justify-content: space-between;\n}\n\n.watch-button, .share-button {\n  flex: 1;\n  padding: var(--spacing-md);\n  border: none;\n  border-radius: var(--border-radius);\n  font-size: var(--font-size-md);\n  font-weight: bold;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.watch-button {\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  margin-right: var(--spacing-sm);\n}\n\n.share-button {\n  background-color: var(--tg-theme-secondary-bg-color);\n  color: var(--tg-theme-text-color);\n  margin-left: var(--spacing-sm);\n}\n\n.share-button svg {\n  width: 18px;\n  height: 18px;\n  margin-right: var(--spacing-xs);\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n</style>\n", "import { render } from \"./MovieView.vue?vue&type=template&id=c1e389f2&scoped=true&ts=true\"\nimport script from \"./MovieView.vue?vue&type=script&lang=ts\"\nexport * from \"./MovieView.vue?vue&type=script&lang=ts\"\n\nimport \"./MovieView.vue?vue&type=style&index=0&id=c1e389f2&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-c1e389f2\"]])\n\nexport default __exports__"], "names": ["getMovieSource", "movie", "kp_id", "imdb_id", "getMovieId", "source", "formatMovieType", "type", "types", "tv", "anime", "getMoviePosterUrl", "poster_urls", "w500", "poster_url", "MovieCategory", "VibixService", "baseUrl", "constructor", "this", "getMoviesByCategory", "category", "page", "limit", "response", "api", "get", "params", "data", "getMovie", "id", "searchByUrl", "url", "useMoviesStore", "defineStore", "state", "currentMovie", "loading", "error", "categoryMovies", "categoryLoading", "categoryError", "pendingRequests", "actions", "fetchMovieById", "window", "Telegram", "WebApp", "message", "searchMovie", "success", "setSearch<PERSON>uery", "query", "console", "log", "clearCurrentMovie", "fetchMoviesByCategory", "request<PERSON>ey", "vibixService", "length", "useCache", "request", "useBookmarksStore", "bookmarks", "lastFetchTime", "fetchBookmarks", "forceRefresh", "now", "Date", "fiveMinutes", "addBookmark", "movie_id", "post", "movieDetails", "movieResponse", "unshift", "_id", "details", "movieError", "deleteBookmark", "delete", "filter", "bookmark", "isBookmarked", "some", "getBookmarkId", "find", "_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "key", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_router_link", "_resolveComponent", "_openBlock", "_createElementBlock", "_createElementVNode", "_toDisplayString", "onClick", "args", "fetchMovie", "_createVNode", "to", "default", "_withCtx", "_createTextVNode", "_", "goBack", "xmlns", "viewBox", "fill", "stroke", "points", "toggleBookmark", "disabled", "bookmarkLoading", "d", "name_rus", "name_original", "year", "kp_rating", "imdb_rating", "_createCommentVNode", "formatType", "description_short", "iframeLoading", "iframe_url", "src", "frameborder", "allowfullscreen", "onLoad", "iframeLoaded", "openInTelegram", "shareMovie", "_createStaticVNode", "defineComponent", "name", "setup", "route", "useRoute", "router", "useRouter", "moviesStore", "bookmarksStore", "ref", "computed", "value", "movieId", "async", "bookmarkId", "openLink", "open", "botUsername", "process", "shareUrl", "shareText", "telegramShareUrl", "encodeURIComponent", "back", "onMounted", "__exports__"], "sourceRoot": ""}