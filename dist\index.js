"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const morgan_1 = __importDefault(require("morgan"));
const dotenv_1 = __importDefault(require("dotenv"));
const mongoose_1 = __importDefault(require("mongoose"));
const telegraf_1 = require("telegraf");
const compression_1 = __importDefault(require("compression"));
// Load environment variables
dotenv_1.default.config();
// Import routes
const bookmarkRoutes_1 = __importDefault(require("./routes/bookmarkRoutes"));
const taskRoutes_1 = __importDefault(require("./routes/taskRoutes"));
const userRoutes_1 = __importDefault(require("./routes/userRoutes"));
const vibixRoutes_1 = __importDefault(require("./routes/vibixRoutes"));
// Import bot commands
const bot_1 = require("./bot/bot");
// Import models
const User_1 = __importDefault(require("./models/User"));
// Import jobs
const posterMaintenanceJob_1 = __importDefault(require("./jobs/posterMaintenanceJob"));
// Import security middleware
const securityMiddleware_1 = require("./middlewares/securityMiddleware");
// Initialize Express app
const app = (0, express_1.default)();
const port = process.env.PORT || 3000;
// Trust proxy for accurate IP detection when behind Nginx
// This is needed for rate limiting and security middleware to work correctly
app.set('trust proxy', 1);
// Get allowed IPs from environment variable or use default
const allowedIps = process.env.ALLOWED_IPS ? process.env.ALLOWED_IPS.split(',') : ['127.0.0.1'];
// Security middleware
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || '*',
    credentials: true
}));
app.use(securityMiddleware_1.helmetMiddleware);
app.use(securityMiddleware_1.secureHeaders);
app.use((0, securityMiddleware_1.ipRestriction)(allowedIps));
app.use('/api', securityMiddleware_1.apiLimiter);
app.use(securityMiddleware_1.mongoSanitizeMiddleware);
// Примечание: middleware для XSS и HPP защиты не используются для повышения производительности
// XSS защита реализована на уровне Vue.js, а HPP - на уровне бизнес-логики
// Standard middleware
// Используем morgan только в режиме разработки для повышения производительности
if (process.env.NODE_ENV === 'development') {
    app.use((0, morgan_1.default)('dev'));
}
app.use(express_1.default.json({ limit: '10kb' })); // Limit JSON payload size
app.use(express_1.default.urlencoded({ extended: true, limit: '10kb' }));
app.use((0, compression_1.default)()); // Compress responses
// Connect to MongoDB with optimized connection options
mongoose_1.default.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cinema-bot', {
    // Оптимизация пула соединений
    maxPoolSize: 10, // Максимальное количество соединений в пуле
    minPoolSize: 2, // Минимальное количество соединений в пуле
    // Таймауты для повышения надежности
    serverSelectionTimeoutMS: 5000, // Таймаут выбора сервера
    socketTimeoutMS: 45000, // Таймаут сокета
    connectTimeoutMS: 10000, // Таймаут соединения
    // Автоматическое переподключение
    retryWrites: true,
    retryReads: true
})
    .then(() => {
    console.log('Connected to MongoDB');
    // Запускаем cron задачи для кеширования постеров
    posterMaintenanceJob_1.default.scheduleJobs();
    console.log('Poster maintenance jobs scheduled');
})
    .catch((error) => {
    console.error('MongoDB connection error:', error);
    // В продакшн-режиме завершаем процесс при ошибке подключения к MongoDB
    if (process.env.NODE_ENV === 'production') {
        console.error('Failed to connect to MongoDB in production mode. Exiting...');
        process.exit(1);
    }
});
// Обработка событий соединения MongoDB
mongoose_1.default.connection.on('error', (err) => {
    console.error('MongoDB connection error:', err);
});
mongoose_1.default.connection.on('disconnected', () => {
    console.warn('MongoDB disconnected. Attempting to reconnect...');
});
mongoose_1.default.connection.on('reconnected', () => {
    console.log('MongoDB reconnected successfully');
});
// Initialize Telegram bot
const bot = new telegraf_1.Telegraf(process.env.TELEGRAM_BOT_TOKEN || '');
(0, bot_1.setupBot)(bot);
// Инициализация режима "только для администраторов" из переменных окружения
const adminOnlyMode = process.env.BOT_ADMIN_ONLY_MODE === 'true';
if (adminOnlyMode) {
    (0, bot_1.setAdminOnlyMode)(true);
    console.log('Bot admin-only mode enabled from environment variable');
}
// Установка администратора из переменной окружения ADMIN_CHAT_ID
const adminChatId = process.env.ADMIN_CHAT_ID;
if (adminChatId && !isNaN(Number(adminChatId))) {
    (async () => {
        try {
            const adminId = Number(adminChatId);
            // Проверяем, существует ли пользователь
            let user = await User_1.default.findOne({ chat_id: adminId });
            if (user) {
                // Если пользователь существует, но не является администратором
                if (user.role !== 'admin') {
                    user.role = 'admin';
                    await user.save();
                    console.log(`User with chat_id ${adminId} has been set as admin from environment variable`);
                }
                else {
                    console.log(`User with chat_id ${adminId} is already an admin`);
                }
            }
            else {
                // Если пользователь не существует, создаем его с ролью администратора
                // Но для этого нужны дополнительные данные, которые будут заполнены при первом взаимодействии
                console.log(`Admin user with chat_id ${adminId} not found in database. Will be set as admin on first interaction.`);
            }
        }
        catch (error) {
            console.error('Error setting admin from environment variable:', error);
        }
    })();
}
// Настройка webhook или long polling в зависимости от окружения
const webhookDomain = process.env.WEBHOOK_DOMAIN || 'https://v2test.appkinobot.com';
// Создаем секретный путь для webhook, используя часть токена бота
const botToken = process.env.TELEGRAM_BOT_TOKEN || '';
const tokenPart = botToken.split(':')[1]?.substring(0, 16) || 'webhook';
const webhookPath = `/telegram-webhook-${tokenPart}`;
// Настраиваем маршрут для webhook в Express
// Важно: этот маршрут должен быть определен ДО запуска бота
app.use(webhookPath, express_1.default.json(), (req, res) => {
    // Передаем обновление боту для обработки
    bot.handleUpdate(req.body, res);
});
if (process.env.NODE_ENV === 'production') {
    // Для продакшн проверяем текущий вебхук и устанавливаем новый при необходимости
    (async () => {
        try {
            // Получаем информацию о текущем вебхуке
            const webhookInfo = await bot.telegram.getWebhookInfo();
            const currentWebhook = webhookInfo.url;
            const targetWebhook = `${webhookDomain}${webhookPath}`;
            // Проверяем, нужно ли обновлять вебхук
            if (!currentWebhook || currentWebhook !== targetWebhook) {
                // Если вебхук не установлен или отличается от нужного, устанавливаем новый
                await bot.telegram.setWebhook(targetWebhook);
                console.log(`Telegram webhook updated to ${targetWebhook}`);
            }
            else {
                console.log(`Telegram webhook already set to ${currentWebhook}, no update needed`);
            }
            // Выводим дополнительную информацию о вебхуке для отладки
            console.log(`Webhook info: pending_update_count=${webhookInfo.pending_update_count}, max_connections=${webhookInfo.max_connections}`);
        }
        catch (error) {
            console.error('Error managing Telegram webhook:', error);
        }
    })();
}
else {
    // Для разработки используем long polling
    bot.launch().then(() => {
        console.log('Telegram bot started with long polling');
    }).catch((error) => {
        console.error('Telegram bot long polling error:', error);
    });
}
// API Routes
app.use('/api/bookmarks', bookmarkRoutes_1.default);
app.use('/api/tasks', taskRoutes_1.default);
app.use('/api/users', userRoutes_1.default);
app.use('/api/vibix', vibixRoutes_1.default);
// Улучшенная обработка ошибок
app.use((err, _req, res, _next) => {
    // Определяем код статуса и сообщение об ошибке
    const statusCode = err.statusCode || 500;
    const errorMessage = err.message || 'Internal Server Error';
    // Генерируем уникальный идентификатор ошибки для отслеживания
    const errorId = Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
    // Создаем структурированный объект для логирования
    const logObject = {
        error_id: errorId,
        timestamp: new Date().toISOString(),
        status: statusCode,
        message: errorMessage,
        path: _req.originalUrl || _req.url,
        method: _req.method,
        ip: _req.ip || _req.socket.remoteAddress,
        user_agent: _req.headers['user-agent'],
        // В режиме разработки добавляем полный стек ошибки
        ...(process.env.NODE_ENV === 'development' && {
            stack: err.stack,
            body: _req.body,
            query: _req.query
        })
    };
    // Логируем ошибку в структурированном формате для легкого поиска по error_id
    console.error(`[ERROR ${errorId}] ${statusCode} - ${errorMessage}`);
    // Сохраняем детальную информацию об ошибке в JSON-формате
    // Это позволит легко найти ошибку по error_id в логах
    console.error(JSON.stringify(logObject, null, process.env.NODE_ENV === 'development' ? 2 : 0));
    // Отправляем ответ клиенту
    res.status(statusCode).json({
        success: false,
        // В production показываем только общее сообщение, а не детали ошибки
        message: process.env.NODE_ENV === 'development'
            ? errorMessage
            : statusCode === 404
                ? 'Not Found'
                : 'Internal Server Error',
        error_id: errorId,
        // Включаем дополнительные детали только в режиме разработки
        ...(process.env.NODE_ENV === 'development' && {
            actual_message: errorMessage,
            stack: err.stack,
            details: err.details || undefined
        })
    });
});
// Start server
app.listen(port, () => {
    console.log(`Server running on port ${port}`);
});
// Handle graceful shutdown
process.on('SIGTERM', () => {
    bot.stop('SIGTERM');
    process.exit(0);
});
process.on('SIGINT', () => {
    bot.stop('SIGINT');
    process.exit(0);
});
exports.default = app;
