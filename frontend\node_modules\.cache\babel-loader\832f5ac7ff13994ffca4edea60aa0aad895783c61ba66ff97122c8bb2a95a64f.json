{"ast": null, "code": "/*!\n * pinia v2.3.1\n * (c) 2025 <PERSON>\n * @license MIT\n */\nimport { hasInjectionContext, inject, toRaw, watch, unref, markRaw, effectScope, ref, isVue2, isRef, isReactive, set, getCurrentScope, onScopeDispose, getCurrentInstance, reactive, toRef, del, nextTick, computed, toRefs } from 'vue-demi';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\n * setActivePinia must be called to handle SSR at the top of functions like\n * `fetch`, `setup`, `serverPrefetch` and others\n */\nlet activePinia;\n/**\n * Sets or unsets the active pinia. Used in SSR and internally when calling\n * actions and getters\n *\n * @param pinia - Pinia instance\n */\n// @ts-expect-error: cannot constrain the type of the return\nconst setActivePinia = pinia => activePinia = pinia;\n/**\n * Get the currently active pinia if there is any.\n */\nconst getActivePinia = () => hasInjectionContext() && inject(piniaSymbol) || activePinia;\nconst piniaSymbol = process.env.NODE_ENV !== 'production' ? Symbol('pinia') : /* istanbul ignore next */Symbol();\nfunction isPlainObject(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\no) {\n  return o && typeof o === 'object' && Object.prototype.toString.call(o) === '[object Object]' && typeof o.toJSON !== 'function';\n}\n// type DeepReadonly<T> = { readonly [P in keyof T]: DeepReadonly<T[P]> }\n// TODO: can we change these to numbers?\n/**\n * Possible types for SubscriptionCallback\n */\nvar MutationType;\n(function (MutationType) {\n  /**\n   * Direct mutation of the state:\n   *\n   * - `store.name = 'new name'`\n   * - `store.$state.name = 'new name'`\n   * - `store.list.push('new item')`\n   */\n  MutationType[\"direct\"] = \"direct\";\n  /**\n   * Mutated the state with `$patch` and an object\n   *\n   * - `store.$patch({ name: 'newName' })`\n   */\n  MutationType[\"patchObject\"] = \"patch object\";\n  /**\n   * Mutated the state with `$patch` and a function\n   *\n   * - `store.$patch(state => state.name = 'newName')`\n   */\n  MutationType[\"patchFunction\"] = \"patch function\";\n  // maybe reset? for $state = {} and $reset\n})(MutationType || (MutationType = {}));\nconst IS_CLIENT = typeof window !== 'undefined';\n\n/*\n * FileSaver.js A saveAs() FileSaver implementation.\n *\n * Originally by Eli Grey, adapted as an ESM module by Eduardo San Martin\n * Morote.\n *\n * License : MIT\n */\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nconst _global = /*#__PURE__*/(() => typeof window === 'object' && window.window === window ? window : typeof self === 'object' && self.self === self ? self : typeof global === 'object' && global.global === global ? global : typeof globalThis === 'object' ? globalThis : {\n  HTMLElement: null\n})();\nfunction bom(blob, {\n  autoBom = false\n} = {}) {\n  // prepend BOM for UTF-8 XML and text/* types (including HTML)\n  // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n  if (autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n    return new Blob([String.fromCharCode(0xfeff), blob], {\n      type: blob.type\n    });\n  }\n  return blob;\n}\nfunction download(url, name, opts) {\n  const xhr = new XMLHttpRequest();\n  xhr.open('GET', url);\n  xhr.responseType = 'blob';\n  xhr.onload = function () {\n    saveAs(xhr.response, name, opts);\n  };\n  xhr.onerror = function () {\n    console.error('could not download file');\n  };\n  xhr.send();\n}\nfunction corsEnabled(url) {\n  const xhr = new XMLHttpRequest();\n  // use sync to avoid popup blocker\n  xhr.open('HEAD', url, false);\n  try {\n    xhr.send();\n  } catch (e) {}\n  return xhr.status >= 200 && xhr.status <= 299;\n}\n// `a.click()` doesn't work for all browsers (#465)\nfunction click(node) {\n  try {\n    node.dispatchEvent(new MouseEvent('click'));\n  } catch (e) {\n    const evt = document.createEvent('MouseEvents');\n    evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80, 20, false, false, false, false, 0, null);\n    node.dispatchEvent(evt);\n  }\n}\nconst _navigator = typeof navigator === 'object' ? navigator : {\n  userAgent: ''\n};\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nconst isMacOSWebView = /*#__PURE__*/(() => /Macintosh/.test(_navigator.userAgent) && /AppleWebKit/.test(_navigator.userAgent) && !/Safari/.test(_navigator.userAgent))();\nconst saveAs = !IS_CLIENT ? () => {} // noop\n:\n// Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView or mini program\ntypeof HTMLAnchorElement !== 'undefined' && 'download' in HTMLAnchorElement.prototype && !isMacOSWebView ? downloadSaveAs :\n// Use msSaveOrOpenBlob as a second approach\n'msSaveOrOpenBlob' in _navigator ? msSaveAs :\n// Fallback to using FileReader and a popup\nfileSaverSaveAs;\nfunction downloadSaveAs(blob, name = 'download', opts) {\n  const a = document.createElement('a');\n  a.download = name;\n  a.rel = 'noopener'; // tabnabbing\n  // TODO: detect chrome extensions & packaged apps\n  // a.target = '_blank'\n  if (typeof blob === 'string') {\n    // Support regular links\n    a.href = blob;\n    if (a.origin !== location.origin) {\n      if (corsEnabled(a.href)) {\n        download(blob, name, opts);\n      } else {\n        a.target = '_blank';\n        click(a);\n      }\n    } else {\n      click(a);\n    }\n  } else {\n    // Support blobs\n    a.href = URL.createObjectURL(blob);\n    setTimeout(function () {\n      URL.revokeObjectURL(a.href);\n    }, 4e4); // 40s\n    setTimeout(function () {\n      click(a);\n    }, 0);\n  }\n}\nfunction msSaveAs(blob, name = 'download', opts) {\n  if (typeof blob === 'string') {\n    if (corsEnabled(blob)) {\n      download(blob, name, opts);\n    } else {\n      const a = document.createElement('a');\n      a.href = blob;\n      a.target = '_blank';\n      setTimeout(function () {\n        click(a);\n      });\n    }\n  } else {\n    // @ts-ignore: works on windows\n    navigator.msSaveOrOpenBlob(bom(blob, opts), name);\n  }\n}\nfunction fileSaverSaveAs(blob, name, opts, popup) {\n  // Open a popup immediately do go around popup blocker\n  // Mostly only available on user interaction and the fileReader is async so...\n  popup = popup || open('', '_blank');\n  if (popup) {\n    popup.document.title = popup.document.body.innerText = 'downloading...';\n  }\n  if (typeof blob === 'string') return download(blob, name, opts);\n  const force = blob.type === 'application/octet-stream';\n  const isSafari = /constructor/i.test(String(_global.HTMLElement)) || 'safari' in _global;\n  const isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent);\n  if ((isChromeIOS || force && isSafari || isMacOSWebView) && typeof FileReader !== 'undefined') {\n    // Safari doesn't allow downloading of blob URLs\n    const reader = new FileReader();\n    reader.onloadend = function () {\n      let url = reader.result;\n      if (typeof url !== 'string') {\n        popup = null;\n        throw new Error('Wrong reader.result type');\n      }\n      url = isChromeIOS ? url : url.replace(/^data:[^;]*;/, 'data:attachment/file;');\n      if (popup) {\n        popup.location.href = url;\n      } else {\n        location.assign(url);\n      }\n      popup = null; // reverse-tabnabbing #460\n    };\n    reader.readAsDataURL(blob);\n  } else {\n    const url = URL.createObjectURL(blob);\n    if (popup) popup.location.assign(url);else location.href = url;\n    popup = null; // reverse-tabnabbing #460\n    setTimeout(function () {\n      URL.revokeObjectURL(url);\n    }, 4e4); // 40s\n  }\n}\n\n/**\n * Shows a toast or console.log\n *\n * @param message - message to log\n * @param type - different color of the tooltip\n */\nfunction toastMessage(message, type) {\n  const piniaMessage = '🍍 ' + message;\n  if (typeof __VUE_DEVTOOLS_TOAST__ === 'function') {\n    // No longer available :(\n    __VUE_DEVTOOLS_TOAST__(piniaMessage, type);\n  } else if (type === 'error') {\n    console.error(piniaMessage);\n  } else if (type === 'warn') {\n    console.warn(piniaMessage);\n  } else {\n    console.log(piniaMessage);\n  }\n}\nfunction isPinia(o) {\n  return '_a' in o && 'install' in o;\n}\n\n/**\n * This file contain devtools actions, they are not Pinia actions.\n */\n// ---\nfunction checkClipboardAccess() {\n  if (!('clipboard' in navigator)) {\n    toastMessage(`Your browser doesn't support the Clipboard API`, 'error');\n    return true;\n  }\n}\nfunction checkNotFocusedError(error) {\n  if (error instanceof Error && error.message.toLowerCase().includes('document is not focused')) {\n    toastMessage('You need to activate the \"Emulate a focused page\" setting in the \"Rendering\" panel of devtools.', 'warn');\n    return true;\n  }\n  return false;\n}\nasync function actionGlobalCopyState(pinia) {\n  if (checkClipboardAccess()) return;\n  try {\n    await navigator.clipboard.writeText(JSON.stringify(pinia.state.value));\n    toastMessage('Global state copied to clipboard.');\n  } catch (error) {\n    if (checkNotFocusedError(error)) return;\n    toastMessage(`Failed to serialize the state. Check the console for more details.`, 'error');\n    console.error(error);\n  }\n}\nasync function actionGlobalPasteState(pinia) {\n  if (checkClipboardAccess()) return;\n  try {\n    loadStoresState(pinia, JSON.parse(await navigator.clipboard.readText()));\n    toastMessage('Global state pasted from clipboard.');\n  } catch (error) {\n    if (checkNotFocusedError(error)) return;\n    toastMessage(`Failed to deserialize the state from clipboard. Check the console for more details.`, 'error');\n    console.error(error);\n  }\n}\nasync function actionGlobalSaveState(pinia) {\n  try {\n    saveAs(new Blob([JSON.stringify(pinia.state.value)], {\n      type: 'text/plain;charset=utf-8'\n    }), 'pinia-state.json');\n  } catch (error) {\n    toastMessage(`Failed to export the state as JSON. Check the console for more details.`, 'error');\n    console.error(error);\n  }\n}\nlet fileInput;\nfunction getFileOpener() {\n  if (!fileInput) {\n    fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.json';\n  }\n  function openFile() {\n    return new Promise((resolve, reject) => {\n      fileInput.onchange = async () => {\n        const files = fileInput.files;\n        if (!files) return resolve(null);\n        const file = files.item(0);\n        if (!file) return resolve(null);\n        return resolve({\n          text: await file.text(),\n          file\n        });\n      };\n      // @ts-ignore: TODO: changed from 4.3 to 4.4\n      fileInput.oncancel = () => resolve(null);\n      fileInput.onerror = reject;\n      fileInput.click();\n    });\n  }\n  return openFile;\n}\nasync function actionGlobalOpenStateFile(pinia) {\n  try {\n    const open = getFileOpener();\n    const result = await open();\n    if (!result) return;\n    const {\n      text,\n      file\n    } = result;\n    loadStoresState(pinia, JSON.parse(text));\n    toastMessage(`Global state imported from \"${file.name}\".`);\n  } catch (error) {\n    toastMessage(`Failed to import the state from JSON. Check the console for more details.`, 'error');\n    console.error(error);\n  }\n}\nfunction loadStoresState(pinia, state) {\n  for (const key in state) {\n    const storeState = pinia.state.value[key];\n    // store is already instantiated, patch it\n    if (storeState) {\n      Object.assign(storeState, state[key]);\n    } else {\n      // store is not instantiated, set the initial state\n      pinia.state.value[key] = state[key];\n    }\n  }\n}\nfunction formatDisplay(display) {\n  return {\n    _custom: {\n      display\n    }\n  };\n}\nconst PINIA_ROOT_LABEL = '🍍 Pinia (root)';\nconst PINIA_ROOT_ID = '_root';\nfunction formatStoreForInspectorTree(store) {\n  return isPinia(store) ? {\n    id: PINIA_ROOT_ID,\n    label: PINIA_ROOT_LABEL\n  } : {\n    id: store.$id,\n    label: store.$id\n  };\n}\nfunction formatStoreForInspectorState(store) {\n  if (isPinia(store)) {\n    const storeNames = Array.from(store._s.keys());\n    const storeMap = store._s;\n    const state = {\n      state: storeNames.map(storeId => ({\n        editable: true,\n        key: storeId,\n        value: store.state.value[storeId]\n      })),\n      getters: storeNames.filter(id => storeMap.get(id)._getters).map(id => {\n        const store = storeMap.get(id);\n        return {\n          editable: false,\n          key: id,\n          value: store._getters.reduce((getters, key) => {\n            getters[key] = store[key];\n            return getters;\n          }, {})\n        };\n      })\n    };\n    return state;\n  }\n  const state = {\n    state: Object.keys(store.$state).map(key => ({\n      editable: true,\n      key,\n      value: store.$state[key]\n    }))\n  };\n  // avoid adding empty getters\n  if (store._getters && store._getters.length) {\n    state.getters = store._getters.map(getterName => ({\n      editable: false,\n      key: getterName,\n      value: store[getterName]\n    }));\n  }\n  if (store._customProperties.size) {\n    state.customProperties = Array.from(store._customProperties).map(key => ({\n      editable: true,\n      key,\n      value: store[key]\n    }));\n  }\n  return state;\n}\nfunction formatEventData(events) {\n  if (!events) return {};\n  if (Array.isArray(events)) {\n    // TODO: handle add and delete for arrays and objects\n    return events.reduce((data, event) => {\n      data.keys.push(event.key);\n      data.operations.push(event.type);\n      data.oldValue[event.key] = event.oldValue;\n      data.newValue[event.key] = event.newValue;\n      return data;\n    }, {\n      oldValue: {},\n      keys: [],\n      operations: [],\n      newValue: {}\n    });\n  } else {\n    return {\n      operation: formatDisplay(events.type),\n      key: formatDisplay(events.key),\n      oldValue: events.oldValue,\n      newValue: events.newValue\n    };\n  }\n}\nfunction formatMutationType(type) {\n  switch (type) {\n    case MutationType.direct:\n      return 'mutation';\n    case MutationType.patchFunction:\n      return '$patch';\n    case MutationType.patchObject:\n      return '$patch';\n    default:\n      return 'unknown';\n  }\n}\n\n// timeline can be paused when directly changing the state\nlet isTimelineActive = true;\nconst componentStateTypes = [];\nconst MUTATIONS_LAYER_ID = 'pinia:mutations';\nconst INSPECTOR_ID = 'pinia';\nconst {\n  assign: assign$1\n} = Object;\n/**\n * Gets the displayed name of a store in devtools\n *\n * @param id - id of the store\n * @returns a formatted string\n */\nconst getStoreType = id => '🍍 ' + id;\n/**\n * Add the pinia plugin without any store. Allows displaying a Pinia plugin tab\n * as soon as it is added to the application.\n *\n * @param app - Vue application\n * @param pinia - pinia instance\n */\nfunction registerPiniaDevtools(app, pinia) {\n  setupDevtoolsPlugin({\n    id: 'dev.esm.pinia',\n    label: 'Pinia 🍍',\n    logo: 'https://pinia.vuejs.org/logo.svg',\n    packageName: 'pinia',\n    homepage: 'https://pinia.vuejs.org',\n    componentStateTypes,\n    app\n  }, api => {\n    if (typeof api.now !== 'function') {\n      toastMessage('You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n    }\n    api.addTimelineLayer({\n      id: MUTATIONS_LAYER_ID,\n      label: `Pinia 🍍`,\n      color: 0xe5df88\n    });\n    api.addInspector({\n      id: INSPECTOR_ID,\n      label: 'Pinia 🍍',\n      icon: 'storage',\n      treeFilterPlaceholder: 'Search stores',\n      actions: [{\n        icon: 'content_copy',\n        action: () => {\n          actionGlobalCopyState(pinia);\n        },\n        tooltip: 'Serialize and copy the state'\n      }, {\n        icon: 'content_paste',\n        action: async () => {\n          await actionGlobalPasteState(pinia);\n          api.sendInspectorTree(INSPECTOR_ID);\n          api.sendInspectorState(INSPECTOR_ID);\n        },\n        tooltip: 'Replace the state with the content of your clipboard'\n      }, {\n        icon: 'save',\n        action: () => {\n          actionGlobalSaveState(pinia);\n        },\n        tooltip: 'Save the state as a JSON file'\n      }, {\n        icon: 'folder_open',\n        action: async () => {\n          await actionGlobalOpenStateFile(pinia);\n          api.sendInspectorTree(INSPECTOR_ID);\n          api.sendInspectorState(INSPECTOR_ID);\n        },\n        tooltip: 'Import the state from a JSON file'\n      }],\n      nodeActions: [{\n        icon: 'restore',\n        tooltip: 'Reset the state (with \"$reset\")',\n        action: nodeId => {\n          const store = pinia._s.get(nodeId);\n          if (!store) {\n            toastMessage(`Cannot reset \"${nodeId}\" store because it wasn't found.`, 'warn');\n          } else if (typeof store.$reset !== 'function') {\n            toastMessage(`Cannot reset \"${nodeId}\" store because it doesn't have a \"$reset\" method implemented.`, 'warn');\n          } else {\n            store.$reset();\n            toastMessage(`Store \"${nodeId}\" reset.`);\n          }\n        }\n      }]\n    });\n    api.on.inspectComponent((payload, ctx) => {\n      const proxy = payload.componentInstance && payload.componentInstance.proxy;\n      if (proxy && proxy._pStores) {\n        const piniaStores = payload.componentInstance.proxy._pStores;\n        Object.values(piniaStores).forEach(store => {\n          payload.instanceData.state.push({\n            type: getStoreType(store.$id),\n            key: 'state',\n            editable: true,\n            value: store._isOptionsAPI ? {\n              _custom: {\n                value: toRaw(store.$state),\n                actions: [{\n                  icon: 'restore',\n                  tooltip: 'Reset the state of this store',\n                  action: () => store.$reset()\n                }]\n              }\n            } :\n            // NOTE: workaround to unwrap transferred refs\n            Object.keys(store.$state).reduce((state, key) => {\n              state[key] = store.$state[key];\n              return state;\n            }, {})\n          });\n          if (store._getters && store._getters.length) {\n            payload.instanceData.state.push({\n              type: getStoreType(store.$id),\n              key: 'getters',\n              editable: false,\n              value: store._getters.reduce((getters, key) => {\n                try {\n                  getters[key] = store[key];\n                } catch (error) {\n                  // @ts-expect-error: we just want to show it in devtools\n                  getters[key] = error;\n                }\n                return getters;\n              }, {})\n            });\n          }\n        });\n      }\n    });\n    api.on.getInspectorTree(payload => {\n      if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n        let stores = [pinia];\n        stores = stores.concat(Array.from(pinia._s.values()));\n        payload.rootNodes = (payload.filter ? stores.filter(store => '$id' in store ? store.$id.toLowerCase().includes(payload.filter.toLowerCase()) : PINIA_ROOT_LABEL.toLowerCase().includes(payload.filter.toLowerCase())) : stores).map(formatStoreForInspectorTree);\n      }\n    });\n    // Expose pinia instance as $pinia to window\n    globalThis.$pinia = pinia;\n    api.on.getInspectorState(payload => {\n      if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n        const inspectedStore = payload.nodeId === PINIA_ROOT_ID ? pinia : pinia._s.get(payload.nodeId);\n        if (!inspectedStore) {\n          // this could be the selected store restored for a different project\n          // so it's better not to say anything here\n          return;\n        }\n        if (inspectedStore) {\n          // Expose selected store as $store to window\n          if (payload.nodeId !== PINIA_ROOT_ID) globalThis.$store = toRaw(inspectedStore);\n          payload.state = formatStoreForInspectorState(inspectedStore);\n        }\n      }\n    });\n    api.on.editInspectorState((payload, ctx) => {\n      if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n        const inspectedStore = payload.nodeId === PINIA_ROOT_ID ? pinia : pinia._s.get(payload.nodeId);\n        if (!inspectedStore) {\n          return toastMessage(`store \"${payload.nodeId}\" not found`, 'error');\n        }\n        const {\n          path\n        } = payload;\n        if (!isPinia(inspectedStore)) {\n          // access only the state\n          if (path.length !== 1 || !inspectedStore._customProperties.has(path[0]) || path[0] in inspectedStore.$state) {\n            path.unshift('$state');\n          }\n        } else {\n          // Root access, we can omit the `.value` because the devtools API does it for us\n          path.unshift('state');\n        }\n        isTimelineActive = false;\n        payload.set(inspectedStore, path, payload.state.value);\n        isTimelineActive = true;\n      }\n    });\n    api.on.editComponentState(payload => {\n      if (payload.type.startsWith('🍍')) {\n        const storeId = payload.type.replace(/^🍍\\s*/, '');\n        const store = pinia._s.get(storeId);\n        if (!store) {\n          return toastMessage(`store \"${storeId}\" not found`, 'error');\n        }\n        const {\n          path\n        } = payload;\n        if (path[0] !== 'state') {\n          return toastMessage(`Invalid path for store \"${storeId}\":\\n${path}\\nOnly state can be modified.`);\n        }\n        // rewrite the first entry to be able to directly set the state as\n        // well as any other path\n        path[0] = '$state';\n        isTimelineActive = false;\n        payload.set(store, path, payload.state.value);\n        isTimelineActive = true;\n      }\n    });\n  });\n}\nfunction addStoreToDevtools(app, store) {\n  if (!componentStateTypes.includes(getStoreType(store.$id))) {\n    componentStateTypes.push(getStoreType(store.$id));\n  }\n  setupDevtoolsPlugin({\n    id: 'dev.esm.pinia',\n    label: 'Pinia 🍍',\n    logo: 'https://pinia.vuejs.org/logo.svg',\n    packageName: 'pinia',\n    homepage: 'https://pinia.vuejs.org',\n    componentStateTypes,\n    app,\n    settings: {\n      logStoreChanges: {\n        label: 'Notify about new/deleted stores',\n        type: 'boolean',\n        defaultValue: true\n      }\n      // useEmojis: {\n      //   label: 'Use emojis in messages ⚡️',\n      //   type: 'boolean',\n      //   defaultValue: true,\n      // },\n    }\n  }, api => {\n    // gracefully handle errors\n    const now = typeof api.now === 'function' ? api.now.bind(api) : Date.now;\n    store.$onAction(({\n      after,\n      onError,\n      name,\n      args\n    }) => {\n      const groupId = runningActionId++;\n      api.addTimelineEvent({\n        layerId: MUTATIONS_LAYER_ID,\n        event: {\n          time: now(),\n          title: '🛫 ' + name,\n          subtitle: 'start',\n          data: {\n            store: formatDisplay(store.$id),\n            action: formatDisplay(name),\n            args\n          },\n          groupId\n        }\n      });\n      after(result => {\n        activeAction = undefined;\n        api.addTimelineEvent({\n          layerId: MUTATIONS_LAYER_ID,\n          event: {\n            time: now(),\n            title: '🛬 ' + name,\n            subtitle: 'end',\n            data: {\n              store: formatDisplay(store.$id),\n              action: formatDisplay(name),\n              args,\n              result\n            },\n            groupId\n          }\n        });\n      });\n      onError(error => {\n        activeAction = undefined;\n        api.addTimelineEvent({\n          layerId: MUTATIONS_LAYER_ID,\n          event: {\n            time: now(),\n            logType: 'error',\n            title: '💥 ' + name,\n            subtitle: 'end',\n            data: {\n              store: formatDisplay(store.$id),\n              action: formatDisplay(name),\n              args,\n              error\n            },\n            groupId\n          }\n        });\n      });\n    }, true);\n    store._customProperties.forEach(name => {\n      watch(() => unref(store[name]), (newValue, oldValue) => {\n        api.notifyComponentUpdate();\n        api.sendInspectorState(INSPECTOR_ID);\n        if (isTimelineActive) {\n          api.addTimelineEvent({\n            layerId: MUTATIONS_LAYER_ID,\n            event: {\n              time: now(),\n              title: 'Change',\n              subtitle: name,\n              data: {\n                newValue,\n                oldValue\n              },\n              groupId: activeAction\n            }\n          });\n        }\n      }, {\n        deep: true\n      });\n    });\n    store.$subscribe(({\n      events,\n      type\n    }, state) => {\n      api.notifyComponentUpdate();\n      api.sendInspectorState(INSPECTOR_ID);\n      if (!isTimelineActive) return;\n      // rootStore.state[store.id] = state\n      const eventData = {\n        time: now(),\n        title: formatMutationType(type),\n        data: assign$1({\n          store: formatDisplay(store.$id)\n        }, formatEventData(events)),\n        groupId: activeAction\n      };\n      if (type === MutationType.patchFunction) {\n        eventData.subtitle = '⤵️';\n      } else if (type === MutationType.patchObject) {\n        eventData.subtitle = '🧩';\n      } else if (events && !Array.isArray(events)) {\n        eventData.subtitle = events.type;\n      }\n      if (events) {\n        eventData.data['rawEvent(s)'] = {\n          _custom: {\n            display: 'DebuggerEvent',\n            type: 'object',\n            tooltip: 'raw DebuggerEvent[]',\n            value: events\n          }\n        };\n      }\n      api.addTimelineEvent({\n        layerId: MUTATIONS_LAYER_ID,\n        event: eventData\n      });\n    }, {\n      detached: true,\n      flush: 'sync'\n    });\n    const hotUpdate = store._hotUpdate;\n    store._hotUpdate = markRaw(newStore => {\n      hotUpdate(newStore);\n      api.addTimelineEvent({\n        layerId: MUTATIONS_LAYER_ID,\n        event: {\n          time: now(),\n          title: '🔥 ' + store.$id,\n          subtitle: 'HMR update',\n          data: {\n            store: formatDisplay(store.$id),\n            info: formatDisplay(`HMR update`)\n          }\n        }\n      });\n      // update the devtools too\n      api.notifyComponentUpdate();\n      api.sendInspectorTree(INSPECTOR_ID);\n      api.sendInspectorState(INSPECTOR_ID);\n    });\n    const {\n      $dispose\n    } = store;\n    store.$dispose = () => {\n      $dispose();\n      api.notifyComponentUpdate();\n      api.sendInspectorTree(INSPECTOR_ID);\n      api.sendInspectorState(INSPECTOR_ID);\n      api.getSettings().logStoreChanges && toastMessage(`Disposed \"${store.$id}\" store 🗑`);\n    };\n    // trigger an update so it can display new registered stores\n    api.notifyComponentUpdate();\n    api.sendInspectorTree(INSPECTOR_ID);\n    api.sendInspectorState(INSPECTOR_ID);\n    api.getSettings().logStoreChanges && toastMessage(`\"${store.$id}\" store installed 🆕`);\n  });\n}\nlet runningActionId = 0;\nlet activeAction;\n/**\n * Patches a store to enable action grouping in devtools by wrapping the store with a Proxy that is passed as the\n * context of all actions, allowing us to set `runningAction` on each access and effectively associating any state\n * mutation to the action.\n *\n * @param store - store to patch\n * @param actionNames - list of actionst to patch\n */\nfunction patchActionForGrouping(store, actionNames, wrapWithProxy) {\n  // original actions of the store as they are given by pinia. We are going to override them\n  const actions = actionNames.reduce((storeActions, actionName) => {\n    // use toRaw to avoid tracking #541\n    storeActions[actionName] = toRaw(store)[actionName];\n    return storeActions;\n  }, {});\n  for (const actionName in actions) {\n    store[actionName] = function () {\n      // the running action id is incremented in a before action hook\n      const _actionId = runningActionId;\n      const trackedStore = wrapWithProxy ? new Proxy(store, {\n        get(...args) {\n          activeAction = _actionId;\n          return Reflect.get(...args);\n        },\n        set(...args) {\n          activeAction = _actionId;\n          return Reflect.set(...args);\n        }\n      }) : store;\n      // For Setup Stores we need https://github.com/tc39/proposal-async-context\n      activeAction = _actionId;\n      const retValue = actions[actionName].apply(trackedStore, arguments);\n      // this is safer as async actions in Setup Stores would associate mutations done outside of the action\n      activeAction = undefined;\n      return retValue;\n    };\n  }\n}\n/**\n * pinia.use(devtoolsPlugin)\n */\nfunction devtoolsPlugin({\n  app,\n  store,\n  options\n}) {\n  // HMR module\n  if (store.$id.startsWith('__hot:')) {\n    return;\n  }\n  // detect option api vs setup api\n  store._isOptionsAPI = !!options.state;\n  // Do not overwrite actions mocked by @pinia/testing (#2298)\n  if (!store._p._testing) {\n    patchActionForGrouping(store, Object.keys(options.actions), store._isOptionsAPI);\n    // Upgrade the HMR to also update the new actions\n    const originalHotUpdate = store._hotUpdate;\n    toRaw(store)._hotUpdate = function (newStore) {\n      originalHotUpdate.apply(this, arguments);\n      patchActionForGrouping(store, Object.keys(newStore._hmrPayload.actions), !!store._isOptionsAPI);\n    };\n  }\n  addStoreToDevtools(app,\n  // FIXME: is there a way to allow the assignment from Store<Id, S, G, A> to StoreGeneric?\n  store);\n}\n\n/**\n * Creates a Pinia instance to be used by the application\n */\nfunction createPinia() {\n  const scope = effectScope(true);\n  // NOTE: here we could check the window object for a state and directly set it\n  // if there is anything like it with Vue 3 SSR\n  const state = scope.run(() => ref({}));\n  let _p = [];\n  // plugins added before calling app.use(pinia)\n  let toBeInstalled = [];\n  const pinia = markRaw({\n    install(app) {\n      // this allows calling useStore() outside of a component setup after\n      // installing pinia's plugin\n      setActivePinia(pinia);\n      if (!isVue2) {\n        pinia._a = app;\n        app.provide(piniaSymbol, pinia);\n        app.config.globalProperties.$pinia = pinia;\n        /* istanbul ignore else */\n        if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n          registerPiniaDevtools(app, pinia);\n        }\n        toBeInstalled.forEach(plugin => _p.push(plugin));\n        toBeInstalled = [];\n      }\n    },\n    use(plugin) {\n      if (!this._a && !isVue2) {\n        toBeInstalled.push(plugin);\n      } else {\n        _p.push(plugin);\n      }\n      return this;\n    },\n    _p,\n    // it's actually undefined here\n    // @ts-expect-error\n    _a: null,\n    _e: scope,\n    _s: new Map(),\n    state\n  });\n  // pinia devtools rely on dev only features so they cannot be forced unless\n  // the dev build of Vue is used. Avoid old browsers like IE11.\n  if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT && typeof Proxy !== 'undefined') {\n    pinia.use(devtoolsPlugin);\n  }\n  return pinia;\n}\n/**\n * Dispose a Pinia instance by stopping its effectScope and removing the state, plugins and stores. This is mostly\n * useful in tests, with both a testing pinia or a regular pinia and in applications that use multiple pinia instances.\n * Once disposed, the pinia instance cannot be used anymore.\n *\n * @param pinia - pinia instance\n */\nfunction disposePinia(pinia) {\n  pinia._e.stop();\n  pinia._s.clear();\n  pinia._p.splice(0);\n  pinia.state.value = {};\n  // @ts-expect-error: non valid\n  pinia._a = null;\n}\n\n/**\n * Checks if a function is a `StoreDefinition`.\n *\n * @param fn - object to test\n * @returns true if `fn` is a StoreDefinition\n */\nconst isUseStore = fn => {\n  return typeof fn === 'function' && typeof fn.$id === 'string';\n};\n/**\n * Mutates in place `newState` with `oldState` to _hot update_ it. It will\n * remove any key not existing in `newState` and recursively merge plain\n * objects.\n *\n * @param newState - new state object to be patched\n * @param oldState - old state that should be used to patch newState\n * @returns - newState\n */\nfunction patchObject(newState, oldState) {\n  // no need to go through symbols because they cannot be serialized anyway\n  for (const key in oldState) {\n    const subPatch = oldState[key];\n    // skip the whole sub tree\n    if (!(key in newState)) {\n      continue;\n    }\n    const targetValue = newState[key];\n    if (isPlainObject(targetValue) && isPlainObject(subPatch) && !isRef(subPatch) && !isReactive(subPatch)) {\n      newState[key] = patchObject(targetValue, subPatch);\n    } else {\n      // objects are either a bit more complex (e.g. refs) or primitives, so we\n      // just set the whole thing\n      if (isVue2) {\n        set(newState, key, subPatch);\n      } else {\n        newState[key] = subPatch;\n      }\n    }\n  }\n  return newState;\n}\n/**\n * Creates an _accept_ function to pass to `import.meta.hot` in Vite applications.\n *\n * @example\n * ```js\n * const useUser = defineStore(...)\n * if (import.meta.hot) {\n *   import.meta.hot.accept(acceptHMRUpdate(useUser, import.meta.hot))\n * }\n * ```\n *\n * @param initialUseStore - return of the defineStore to hot update\n * @param hot - `import.meta.hot`\n */\nfunction acceptHMRUpdate(initialUseStore, hot) {\n  // strip as much as possible from iife.prod\n  if (!(process.env.NODE_ENV !== 'production')) {\n    return () => {};\n  }\n  return newModule => {\n    const pinia = hot.data.pinia || initialUseStore._pinia;\n    if (!pinia) {\n      // this store is still not used\n      return;\n    }\n    // preserve the pinia instance across loads\n    hot.data.pinia = pinia;\n    // console.log('got data', newStore)\n    for (const exportName in newModule) {\n      const useStore = newModule[exportName];\n      // console.log('checking for', exportName)\n      if (isUseStore(useStore) && pinia._s.has(useStore.$id)) {\n        // console.log('Accepting update for', useStore.$id)\n        const id = useStore.$id;\n        if (id !== initialUseStore.$id) {\n          console.warn(`The id of the store changed from \"${initialUseStore.$id}\" to \"${id}\". Reloading.`);\n          // return import.meta.hot.invalidate()\n          return hot.invalidate();\n        }\n        const existingStore = pinia._s.get(id);\n        if (!existingStore) {\n          console.log(`[Pinia]: skipping hmr because store doesn't exist yet`);\n          return;\n        }\n        useStore(pinia, existingStore);\n      }\n    }\n  };\n}\nconst noop = () => {};\nfunction addSubscription(subscriptions, callback, detached, onCleanup = noop) {\n  subscriptions.push(callback);\n  const removeSubscription = () => {\n    const idx = subscriptions.indexOf(callback);\n    if (idx > -1) {\n      subscriptions.splice(idx, 1);\n      onCleanup();\n    }\n  };\n  if (!detached && getCurrentScope()) {\n    onScopeDispose(removeSubscription);\n  }\n  return removeSubscription;\n}\nfunction triggerSubscriptions(subscriptions, ...args) {\n  subscriptions.slice().forEach(callback => {\n    callback(...args);\n  });\n}\nconst fallbackRunWithContext = fn => fn();\n/**\n * Marks a function as an action for `$onAction`\n * @internal\n */\nconst ACTION_MARKER = Symbol();\n/**\n * Action name symbol. Allows to add a name to an action after defining it\n * @internal\n */\nconst ACTION_NAME = Symbol();\nfunction mergeReactiveObjects(target, patchToApply) {\n  // Handle Map instances\n  if (target instanceof Map && patchToApply instanceof Map) {\n    patchToApply.forEach((value, key) => target.set(key, value));\n  } else if (target instanceof Set && patchToApply instanceof Set) {\n    // Handle Set instances\n    patchToApply.forEach(target.add, target);\n  }\n  // no need to go through symbols because they cannot be serialized anyway\n  for (const key in patchToApply) {\n    if (!patchToApply.hasOwnProperty(key)) continue;\n    const subPatch = patchToApply[key];\n    const targetValue = target[key];\n    if (isPlainObject(targetValue) && isPlainObject(subPatch) && target.hasOwnProperty(key) && !isRef(subPatch) && !isReactive(subPatch)) {\n      // NOTE: here I wanted to warn about inconsistent types but it's not possible because in setup stores one might\n      // start the value of a property as a certain type e.g. a Map, and then for some reason, during SSR, change that\n      // to `undefined`. When trying to hydrate, we want to override the Map with `undefined`.\n      target[key] = mergeReactiveObjects(targetValue, subPatch);\n    } else {\n      // @ts-expect-error: subPatch is a valid value\n      target[key] = subPatch;\n    }\n  }\n  return target;\n}\nconst skipHydrateSymbol = process.env.NODE_ENV !== 'production' ? Symbol('pinia:skipHydration') : /* istanbul ignore next */Symbol();\n/**\n * Tells Pinia to skip the hydration process of a given object. This is useful in setup stores (only) when you return a\n * stateful object in the store but it isn't really state. e.g. returning a router instance in a setup store.\n *\n * @param obj - target object\n * @returns obj\n */\nfunction skipHydrate(obj) {\n  return Object.defineProperty(obj, skipHydrateSymbol, {});\n}\n/**\n * Returns whether a value should be hydrated\n *\n * @param obj - target variable\n * @returns true if `obj` should be hydrated\n */\nfunction shouldHydrate(obj) {\n  return !isPlainObject(obj) || !obj.hasOwnProperty(skipHydrateSymbol);\n}\nconst {\n  assign\n} = Object;\nfunction isComputed(o) {\n  return !!(isRef(o) && o.effect);\n}\nfunction createOptionsStore(id, options, pinia, hot) {\n  const {\n    state,\n    actions,\n    getters\n  } = options;\n  const initialState = pinia.state.value[id];\n  let store;\n  function setup() {\n    if (!initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n      /* istanbul ignore if */\n      if (isVue2) {\n        set(pinia.state.value, id, state ? state() : {});\n      } else {\n        pinia.state.value[id] = state ? state() : {};\n      }\n    }\n    // avoid creating a state in pinia.state.value\n    const localState = process.env.NODE_ENV !== 'production' && hot ?\n    // use ref() to unwrap refs inside state TODO: check if this is still necessary\n    toRefs(ref(state ? state() : {}).value) : toRefs(pinia.state.value[id]);\n    return assign(localState, actions, Object.keys(getters || {}).reduce((computedGetters, name) => {\n      if (process.env.NODE_ENV !== 'production' && name in localState) {\n        console.warn(`[🍍]: A getter cannot have the same name as another state property. Rename one of them. Found with \"${name}\" in store \"${id}\".`);\n      }\n      computedGetters[name] = markRaw(computed(() => {\n        setActivePinia(pinia);\n        // it was created just before\n        const store = pinia._s.get(id);\n        // allow cross using stores\n        /* istanbul ignore if */\n        if (isVue2 && !store._r) return;\n        // @ts-expect-error\n        // return getters![name].call(context, context)\n        // TODO: avoid reading the getter while assigning with a global variable\n        return getters[name].call(store, store);\n      }));\n      return computedGetters;\n    }, {}));\n  }\n  store = createSetupStore(id, setup, options, pinia, hot, true);\n  return store;\n}\nfunction createSetupStore($id, setup, options = {}, pinia, hot, isOptionsStore) {\n  let scope;\n  const optionsForPlugin = assign({\n    actions: {}\n  }, options);\n  /* istanbul ignore if */\n  if (process.env.NODE_ENV !== 'production' && !pinia._e.active) {\n    throw new Error('Pinia destroyed');\n  }\n  // watcher options for $subscribe\n  const $subscribeOptions = {\n    deep: true\n  };\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production' && !isVue2) {\n    $subscribeOptions.onTrigger = event => {\n      /* istanbul ignore else */\n      if (isListening) {\n        debuggerEvents = event;\n        // avoid triggering this while the store is being built and the state is being set in pinia\n      } else if (isListening == false && !store._hotUpdating) {\n        // let patch send all the events together later\n        /* istanbul ignore else */\n        if (Array.isArray(debuggerEvents)) {\n          debuggerEvents.push(event);\n        } else {\n          console.error('🍍 debuggerEvents should be an array. This is most likely an internal Pinia bug.');\n        }\n      }\n    };\n  }\n  // internal state\n  let isListening; // set to true at the end\n  let isSyncListening; // set to true at the end\n  let subscriptions = [];\n  let actionSubscriptions = [];\n  let debuggerEvents;\n  const initialState = pinia.state.value[$id];\n  // avoid setting the state for option stores if it is set\n  // by the setup\n  if (!isOptionsStore && !initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n    /* istanbul ignore if */\n    if (isVue2) {\n      set(pinia.state.value, $id, {});\n    } else {\n      pinia.state.value[$id] = {};\n    }\n  }\n  const hotState = ref({});\n  // avoid triggering too many listeners\n  // https://github.com/vuejs/pinia/issues/1129\n  let activeListener;\n  function $patch(partialStateOrMutator) {\n    let subscriptionMutation;\n    isListening = isSyncListening = false;\n    // reset the debugger events since patches are sync\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      debuggerEvents = [];\n    }\n    if (typeof partialStateOrMutator === 'function') {\n      partialStateOrMutator(pinia.state.value[$id]);\n      subscriptionMutation = {\n        type: MutationType.patchFunction,\n        storeId: $id,\n        events: debuggerEvents\n      };\n    } else {\n      mergeReactiveObjects(pinia.state.value[$id], partialStateOrMutator);\n      subscriptionMutation = {\n        type: MutationType.patchObject,\n        payload: partialStateOrMutator,\n        storeId: $id,\n        events: debuggerEvents\n      };\n    }\n    const myListenerId = activeListener = Symbol();\n    nextTick().then(() => {\n      if (activeListener === myListenerId) {\n        isListening = true;\n      }\n    });\n    isSyncListening = true;\n    // because we paused the watcher, we need to manually call the subscriptions\n    triggerSubscriptions(subscriptions, subscriptionMutation, pinia.state.value[$id]);\n  }\n  const $reset = isOptionsStore ? function $reset() {\n    const {\n      state\n    } = options;\n    const newState = state ? state() : {};\n    // we use a patch to group all changes into one single subscription\n    this.$patch($state => {\n      // @ts-expect-error: FIXME: shouldn't error?\n      assign($state, newState);\n    });\n  } : /* istanbul ignore next */\n  process.env.NODE_ENV !== 'production' ? () => {\n    throw new Error(`🍍: Store \"${$id}\" is built using the setup syntax and does not implement $reset().`);\n  } : noop;\n  function $dispose() {\n    scope.stop();\n    subscriptions = [];\n    actionSubscriptions = [];\n    pinia._s.delete($id);\n  }\n  /**\n   * Helper that wraps function so it can be tracked with $onAction\n   * @param fn - action to wrap\n   * @param name - name of the action\n   */\n  const action = (fn, name = '') => {\n    if (ACTION_MARKER in fn) {\n      fn[ACTION_NAME] = name;\n      return fn;\n    }\n    const wrappedAction = function () {\n      setActivePinia(pinia);\n      const args = Array.from(arguments);\n      const afterCallbackList = [];\n      const onErrorCallbackList = [];\n      function after(callback) {\n        afterCallbackList.push(callback);\n      }\n      function onError(callback) {\n        onErrorCallbackList.push(callback);\n      }\n      // @ts-expect-error\n      triggerSubscriptions(actionSubscriptions, {\n        args,\n        name: wrappedAction[ACTION_NAME],\n        store,\n        after,\n        onError\n      });\n      let ret;\n      try {\n        ret = fn.apply(this && this.$id === $id ? this : store, args);\n        // handle sync errors\n      } catch (error) {\n        triggerSubscriptions(onErrorCallbackList, error);\n        throw error;\n      }\n      if (ret instanceof Promise) {\n        return ret.then(value => {\n          triggerSubscriptions(afterCallbackList, value);\n          return value;\n        }).catch(error => {\n          triggerSubscriptions(onErrorCallbackList, error);\n          return Promise.reject(error);\n        });\n      }\n      // trigger after callbacks\n      triggerSubscriptions(afterCallbackList, ret);\n      return ret;\n    };\n    wrappedAction[ACTION_MARKER] = true;\n    wrappedAction[ACTION_NAME] = name; // will be set later\n    // @ts-expect-error: we are intentionally limiting the returned type to just Fn\n    // because all the added properties are internals that are exposed through `$onAction()` only\n    return wrappedAction;\n  };\n  const _hmrPayload = /*#__PURE__*/markRaw({\n    actions: {},\n    getters: {},\n    state: [],\n    hotState\n  });\n  const partialStore = {\n    _p: pinia,\n    // _s: scope,\n    $id,\n    $onAction: addSubscription.bind(null, actionSubscriptions),\n    $patch,\n    $reset,\n    $subscribe(callback, options = {}) {\n      const removeSubscription = addSubscription(subscriptions, callback, options.detached, () => stopWatcher());\n      const stopWatcher = scope.run(() => watch(() => pinia.state.value[$id], state => {\n        if (options.flush === 'sync' ? isSyncListening : isListening) {\n          callback({\n            storeId: $id,\n            type: MutationType.direct,\n            events: debuggerEvents\n          }, state);\n        }\n      }, assign({}, $subscribeOptions, options)));\n      return removeSubscription;\n    },\n    $dispose\n  };\n  /* istanbul ignore if */\n  if (isVue2) {\n    // start as non ready\n    partialStore._r = false;\n  }\n  const store = reactive(process.env.NODE_ENV !== 'production' || (process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT ? assign({\n    _hmrPayload,\n    _customProperties: markRaw(new Set()) // devtools custom properties\n  }, partialStore\n  // must be added later\n  // setupStore\n  ) : partialStore);\n  // store the partial store now so the setup of stores can instantiate each other before they are finished without\n  // creating infinite loops.\n  pinia._s.set($id, store);\n  const runWithContext = pinia._a && pinia._a.runWithContext || fallbackRunWithContext;\n  // TODO: idea create skipSerialize that marks properties as non serializable and they are skipped\n  const setupStore = runWithContext(() => pinia._e.run(() => (scope = effectScope()).run(() => setup({\n    action\n  }))));\n  // overwrite existing actions to support $onAction\n  for (const key in setupStore) {\n    const prop = setupStore[key];\n    if (isRef(prop) && !isComputed(prop) || isReactive(prop)) {\n      // mark it as a piece of state to be serialized\n      if (process.env.NODE_ENV !== 'production' && hot) {\n        set(hotState.value, key, toRef(setupStore, key));\n        // createOptionStore directly sets the state in pinia.state.value so we\n        // can just skip that\n      } else if (!isOptionsStore) {\n        // in setup stores we must hydrate the state and sync pinia state tree with the refs the user just created\n        if (initialState && shouldHydrate(prop)) {\n          if (isRef(prop)) {\n            prop.value = initialState[key];\n          } else {\n            // probably a reactive object, lets recursively assign\n            // @ts-expect-error: prop is unknown\n            mergeReactiveObjects(prop, initialState[key]);\n          }\n        }\n        // transfer the ref to the pinia state to keep everything in sync\n        /* istanbul ignore if */\n        if (isVue2) {\n          set(pinia.state.value[$id], key, prop);\n        } else {\n          pinia.state.value[$id][key] = prop;\n        }\n      }\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        _hmrPayload.state.push(key);\n      }\n      // action\n    } else if (typeof prop === 'function') {\n      const actionValue = process.env.NODE_ENV !== 'production' && hot ? prop : action(prop, key);\n      // this a hot module replacement store because the hotUpdate method needs\n      // to do it with the right context\n      /* istanbul ignore if */\n      if (isVue2) {\n        set(setupStore, key, actionValue);\n      } else {\n        // @ts-expect-error\n        setupStore[key] = actionValue;\n      }\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        _hmrPayload.actions[key] = prop;\n      }\n      // list actions so they can be used in plugins\n      // @ts-expect-error\n      optionsForPlugin.actions[key] = prop;\n    } else if (process.env.NODE_ENV !== 'production') {\n      // add getters for devtools\n      if (isComputed(prop)) {\n        _hmrPayload.getters[key] = isOptionsStore ?\n        // @ts-expect-error\n        options.getters[key] : prop;\n        if (IS_CLIENT) {\n          const getters = setupStore._getters || (\n          // @ts-expect-error: same\n          setupStore._getters = markRaw([]));\n          getters.push(key);\n        }\n      }\n    }\n  }\n  // add the state, getters, and action properties\n  /* istanbul ignore if */\n  if (isVue2) {\n    Object.keys(setupStore).forEach(key => {\n      set(store, key, setupStore[key]);\n    });\n  } else {\n    assign(store, setupStore);\n    // allows retrieving reactive objects with `storeToRefs()`. Must be called after assigning to the reactive object.\n    // Make `storeToRefs()` work with `reactive()` #799\n    assign(toRaw(store), setupStore);\n  }\n  // use this instead of a computed with setter to be able to create it anywhere\n  // without linking the computed lifespan to wherever the store is first\n  // created.\n  Object.defineProperty(store, '$state', {\n    get: () => process.env.NODE_ENV !== 'production' && hot ? hotState.value : pinia.state.value[$id],\n    set: state => {\n      /* istanbul ignore if */\n      if (process.env.NODE_ENV !== 'production' && hot) {\n        throw new Error('cannot set hotState');\n      }\n      $patch($state => {\n        // @ts-expect-error: FIXME: shouldn't error?\n        assign($state, state);\n      });\n    }\n  });\n  // add the hotUpdate before plugins to allow them to override it\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production') {\n    store._hotUpdate = markRaw(newStore => {\n      store._hotUpdating = true;\n      newStore._hmrPayload.state.forEach(stateKey => {\n        if (stateKey in store.$state) {\n          const newStateTarget = newStore.$state[stateKey];\n          const oldStateSource = store.$state[stateKey];\n          if (typeof newStateTarget === 'object' && isPlainObject(newStateTarget) && isPlainObject(oldStateSource)) {\n            patchObject(newStateTarget, oldStateSource);\n          } else {\n            // transfer the ref\n            newStore.$state[stateKey] = oldStateSource;\n          }\n        }\n        // patch direct access properties to allow store.stateProperty to work as\n        // store.$state.stateProperty\n        set(store, stateKey, toRef(newStore.$state, stateKey));\n      });\n      // remove deleted state properties\n      Object.keys(store.$state).forEach(stateKey => {\n        if (!(stateKey in newStore.$state)) {\n          del(store, stateKey);\n        }\n      });\n      // avoid devtools logging this as a mutation\n      isListening = false;\n      isSyncListening = false;\n      pinia.state.value[$id] = toRef(newStore._hmrPayload, 'hotState');\n      isSyncListening = true;\n      nextTick().then(() => {\n        isListening = true;\n      });\n      for (const actionName in newStore._hmrPayload.actions) {\n        const actionFn = newStore[actionName];\n        set(store, actionName, action(actionFn, actionName));\n      }\n      // TODO: does this work in both setup and option store?\n      for (const getterName in newStore._hmrPayload.getters) {\n        const getter = newStore._hmrPayload.getters[getterName];\n        const getterValue = isOptionsStore ?\n        // special handling of options api\n        computed(() => {\n          setActivePinia(pinia);\n          return getter.call(store, store);\n        }) : getter;\n        set(store, getterName, getterValue);\n      }\n      // remove deleted getters\n      Object.keys(store._hmrPayload.getters).forEach(key => {\n        if (!(key in newStore._hmrPayload.getters)) {\n          del(store, key);\n        }\n      });\n      // remove old actions\n      Object.keys(store._hmrPayload.actions).forEach(key => {\n        if (!(key in newStore._hmrPayload.actions)) {\n          del(store, key);\n        }\n      });\n      // update the values used in devtools and to allow deleting new properties later on\n      store._hmrPayload = newStore._hmrPayload;\n      store._getters = newStore._getters;\n      store._hotUpdating = false;\n    });\n  }\n  if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n    const nonEnumerable = {\n      writable: true,\n      configurable: true,\n      // avoid warning on devtools trying to display this property\n      enumerable: false\n    };\n    ['_p', '_hmrPayload', '_getters', '_customProperties'].forEach(p => {\n      Object.defineProperty(store, p, assign({\n        value: store[p]\n      }, nonEnumerable));\n    });\n  }\n  /* istanbul ignore if */\n  if (isVue2) {\n    // mark the store as ready before plugins\n    store._r = true;\n  }\n  // apply all plugins\n  pinia._p.forEach(extender => {\n    /* istanbul ignore else */\n    if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n      const extensions = scope.run(() => extender({\n        store: store,\n        app: pinia._a,\n        pinia,\n        options: optionsForPlugin\n      }));\n      Object.keys(extensions || {}).forEach(key => store._customProperties.add(key));\n      assign(store, extensions);\n    } else {\n      assign(store, scope.run(() => extender({\n        store: store,\n        app: pinia._a,\n        pinia,\n        options: optionsForPlugin\n      })));\n    }\n  });\n  if (process.env.NODE_ENV !== 'production' && store.$state && typeof store.$state === 'object' && typeof store.$state.constructor === 'function' && !store.$state.constructor.toString().includes('[native code]')) {\n    console.warn(`[🍍]: The \"state\" must be a plain object. It cannot be\\n` + `\\tstate: () => new MyClass()\\n` + `Found in store \"${store.$id}\".`);\n  }\n  // only apply hydrate to option stores with an initial state in pinia\n  if (initialState && isOptionsStore && options.hydrate) {\n    options.hydrate(store.$state, initialState);\n  }\n  isListening = true;\n  isSyncListening = true;\n  return store;\n}\n// allows unused stores to be tree shaken\n/*! #__NO_SIDE_EFFECTS__ */\nfunction defineStore(\n// TODO: add proper types from above\nidOrOptions, setup, setupOptions) {\n  let id;\n  let options;\n  const isSetupStore = typeof setup === 'function';\n  if (typeof idOrOptions === 'string') {\n    id = idOrOptions;\n    // the option store setup will contain the actual options in this case\n    options = isSetupStore ? setupOptions : setup;\n  } else {\n    options = idOrOptions;\n    id = idOrOptions.id;\n    if (process.env.NODE_ENV !== 'production' && typeof id !== 'string') {\n      throw new Error(`[🍍]: \"defineStore()\" must be passed a store id as its first argument.`);\n    }\n  }\n  function useStore(pinia, hot) {\n    const hasContext = hasInjectionContext();\n    pinia =\n    // in test mode, ignore the argument provided as we can always retrieve a\n    // pinia instance with getActivePinia()\n    (process.env.NODE_ENV === 'test' && activePinia && activePinia._testing ? null : pinia) || (hasContext ? inject(piniaSymbol, null) : null);\n    if (pinia) setActivePinia(pinia);\n    if (process.env.NODE_ENV !== 'production' && !activePinia) {\n      throw new Error(`[🍍]: \"getActivePinia()\" was called but there was no active Pinia. Are you trying to use a store before calling \"app.use(pinia)\"?\\n` + `See https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\\n` + `This will fail in production.`);\n    }\n    pinia = activePinia;\n    if (!pinia._s.has(id)) {\n      // creating the store registers it in `pinia._s`\n      if (isSetupStore) {\n        createSetupStore(id, setup, options, pinia);\n      } else {\n        createOptionsStore(id, options, pinia);\n      }\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        // @ts-expect-error: not the right inferred type\n        useStore._pinia = pinia;\n      }\n    }\n    const store = pinia._s.get(id);\n    if (process.env.NODE_ENV !== 'production' && hot) {\n      const hotId = '__hot:' + id;\n      const newStore = isSetupStore ? createSetupStore(hotId, setup, options, pinia, true) : createOptionsStore(hotId, assign({}, options), pinia, true);\n      hot._hotUpdate(newStore);\n      // cleanup the state properties and the store from the cache\n      delete pinia.state.value[hotId];\n      pinia._s.delete(hotId);\n    }\n    if (process.env.NODE_ENV !== 'production' && IS_CLIENT) {\n      const currentInstance = getCurrentInstance();\n      // save stores in instances to access them devtools\n      if (currentInstance && currentInstance.proxy &&\n      // avoid adding stores that are just built for hot module replacement\n      !hot) {\n        const vm = currentInstance.proxy;\n        const cache = '_pStores' in vm ? vm._pStores : vm._pStores = {};\n        cache[id] = store;\n      }\n    }\n    // StoreGeneric cannot be casted towards Store\n    return store;\n  }\n  useStore.$id = id;\n  return useStore;\n}\nlet mapStoreSuffix = 'Store';\n/**\n * Changes the suffix added by `mapStores()`. Can be set to an empty string.\n * Defaults to `\"Store\"`. Make sure to extend the MapStoresCustomization\n * interface if you are using TypeScript.\n *\n * @param suffix - new suffix\n */\nfunction setMapStoreSuffix(suffix // could be 'Store' but that would be annoying for JS\n) {\n  mapStoreSuffix = suffix;\n}\n/**\n * Allows using stores without the composition API (`setup()`) by generating an\n * object to be spread in the `computed` field of a component. It accepts a list\n * of store definitions.\n *\n * @example\n * ```js\n * export default {\n *   computed: {\n *     // other computed properties\n *     ...mapStores(useUserStore, useCartStore)\n *   },\n *\n *   created() {\n *     this.userStore // store with id \"user\"\n *     this.cartStore // store with id \"cart\"\n *   }\n * }\n * ```\n *\n * @param stores - list of stores to map to an object\n */\nfunction mapStores(...stores) {\n  if (process.env.NODE_ENV !== 'production' && Array.isArray(stores[0])) {\n    console.warn(`[🍍]: Directly pass all stores to \"mapStores()\" without putting them in an array:\\n` + `Replace\\n` + `\\tmapStores([useAuthStore, useCartStore])\\n` + `with\\n` + `\\tmapStores(useAuthStore, useCartStore)\\n` + `This will fail in production if not fixed.`);\n    stores = stores[0];\n  }\n  return stores.reduce((reduced, useStore) => {\n    // @ts-expect-error: $id is added by defineStore\n    reduced[useStore.$id + mapStoreSuffix] = function () {\n      return useStore(this.$pinia);\n    };\n    return reduced;\n  }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapState(useStore, keysOrMapper) {\n  return Array.isArray(keysOrMapper) ? keysOrMapper.reduce((reduced, key) => {\n    reduced[key] = function () {\n      // @ts-expect-error: FIXME: should work?\n      return useStore(this.$pinia)[key];\n    };\n    return reduced;\n  }, {}) : Object.keys(keysOrMapper).reduce((reduced, key) => {\n    // @ts-expect-error\n    reduced[key] = function () {\n      const store = useStore(this.$pinia);\n      const storeKey = keysOrMapper[key];\n      // for some reason TS is unable to infer the type of storeKey to be a\n      // function\n      return typeof storeKey === 'function' ? storeKey.call(this, store) :\n      // @ts-expect-error: FIXME: should work?\n      store[storeKey];\n    };\n    return reduced;\n  }, {});\n}\n/**\n * Alias for `mapState()`. You should use `mapState()` instead.\n * @deprecated use `mapState()` instead.\n */\nconst mapGetters = mapState;\n/**\n * Allows directly using actions from your store without using the composition\n * API (`setup()`) by generating an object to be spread in the `methods` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapActions(useStore, keysOrMapper) {\n  return Array.isArray(keysOrMapper) ? keysOrMapper.reduce((reduced, key) => {\n    // @ts-expect-error\n    reduced[key] = function (...args) {\n      // @ts-expect-error: FIXME: should work?\n      return useStore(this.$pinia)[key](...args);\n    };\n    return reduced;\n  }, {}) : Object.keys(keysOrMapper).reduce((reduced, key) => {\n    // @ts-expect-error\n    reduced[key] = function (...args) {\n      // @ts-expect-error: FIXME: should work?\n      return useStore(this.$pinia)[keysOrMapper[key]](...args);\n    };\n    return reduced;\n  }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapWritableState(useStore, keysOrMapper) {\n  return Array.isArray(keysOrMapper) ? keysOrMapper.reduce((reduced, key) => {\n    reduced[key] = {\n      get() {\n        return useStore(this.$pinia)[key];\n      },\n      set(value) {\n        return useStore(this.$pinia)[key] = value;\n      }\n    };\n    return reduced;\n  }, {}) : Object.keys(keysOrMapper).reduce((reduced, key) => {\n    reduced[key] = {\n      get() {\n        return useStore(this.$pinia)[keysOrMapper[key]];\n      },\n      set(value) {\n        return useStore(this.$pinia)[keysOrMapper[key]] = value;\n      }\n    };\n    return reduced;\n  }, {});\n}\n\n/**\n * Creates an object of references with all the state, getters, and plugin-added\n * state properties of the store. Similar to `toRefs()` but specifically\n * designed for Pinia stores so methods and non reactive properties are\n * completely ignored.\n *\n * @param store - store to extract the refs from\n */\nfunction storeToRefs(store) {\n  // See https://github.com/vuejs/pinia/issues/852\n  // It's easier to just use toRefs() even if it includes more stuff\n  if (isVue2) {\n    // @ts-expect-error: toRefs include methods and others\n    return toRefs(store);\n  } else {\n    const rawStore = toRaw(store);\n    const refs = {};\n    for (const key in rawStore) {\n      const value = rawStore[key];\n      // There is no native method to check for a computed\n      // https://github.com/vuejs/core/pull/4165\n      if (value.effect) {\n        // @ts-expect-error: too hard to type correctly\n        refs[key] =\n        // ...\n        computed({\n          get: () => store[key],\n          set(value) {\n            store[key] = value;\n          }\n        });\n      } else if (isRef(value) || isReactive(value)) {\n        // @ts-expect-error: the key is state or getter\n        refs[key] =\n        // ---\n        toRef(store, key);\n      }\n    }\n    return refs;\n  }\n}\n\n/**\n * Vue 2 Plugin that must be installed for pinia to work. Note **you don't need\n * this plugin if you are using Nuxt.js**. Use the `buildModule` instead:\n * https://pinia.vuejs.org/ssr/nuxt.html.\n *\n * @example\n * ```js\n * import Vue from 'vue'\n * import { PiniaVuePlugin, createPinia } from 'pinia'\n *\n * Vue.use(PiniaVuePlugin)\n * const pinia = createPinia()\n *\n * new Vue({\n *   el: '#app',\n *   // ...\n *   pinia,\n * })\n * ```\n *\n * @param _Vue - `Vue` imported from 'vue'.\n */\nconst PiniaVuePlugin = function (_Vue) {\n  // Equivalent of\n  // app.config.globalProperties.$pinia = pinia\n  _Vue.mixin({\n    beforeCreate() {\n      const options = this.$options;\n      if (options.pinia) {\n        const pinia = options.pinia;\n        // HACK: taken from provide(): https://github.com/vuejs/composition-api/blob/main/src/apis/inject.ts#L31\n        /* istanbul ignore else */\n        if (!this._provided) {\n          const provideCache = {};\n          Object.defineProperty(this, '_provided', {\n            get: () => provideCache,\n            set: v => Object.assign(provideCache, v)\n          });\n        }\n        this._provided[piniaSymbol] = pinia;\n        // propagate the pinia instance in an SSR friendly way\n        // avoid adding it to nuxt twice\n        /* istanbul ignore else */\n        if (!this.$pinia) {\n          this.$pinia = pinia;\n        }\n        pinia._a = this;\n        if (IS_CLIENT) {\n          // this allows calling useStore() outside of a component setup after\n          // installing pinia's plugin\n          setActivePinia(pinia);\n        }\n        if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n          registerPiniaDevtools(pinia._a, pinia);\n        }\n      } else if (!this.$pinia && options.parent && options.parent.$pinia) {\n        this.$pinia = options.parent.$pinia;\n      }\n    },\n    destroyed() {\n      delete this._pStores;\n    }\n  });\n};\nexport { MutationType, PiniaVuePlugin, acceptHMRUpdate, createPinia, defineStore, disposePinia, getActivePinia, mapActions, mapGetters, mapState, mapStores, mapWritableState, setActivePinia, setMapStoreSuffix, shouldHydrate, skipHydrate, storeToRefs };", "map": {"version": 3, "names": ["hasInjectionContext", "inject", "toRaw", "watch", "unref", "mark<PERSON>aw", "effectScope", "ref", "isVue2", "isRef", "isReactive", "set", "getCurrentScope", "onScopeDispose", "getCurrentInstance", "reactive", "toRef", "del", "nextTick", "computed", "toRefs", "setupDevtoolsPlugin", "activePinia", "setActivePinia", "pinia", "getActivePinia", "piniaSymbol", "process", "env", "NODE_ENV", "Symbol", "isPlainObject", "o", "Object", "prototype", "toString", "call", "toJSON", "MutationType", "IS_CLIENT", "window", "_global", "self", "global", "globalThis", "HTMLElement", "bom", "blob", "autoBom", "test", "type", "Blob", "String", "fromCharCode", "download", "url", "name", "opts", "xhr", "XMLHttpRequest", "open", "responseType", "onload", "saveAs", "response", "onerror", "console", "error", "send", "cors<PERSON>nabled", "e", "status", "click", "node", "dispatchEvent", "MouseEvent", "evt", "document", "createEvent", "initMouseEvent", "_navigator", "navigator", "userAgent", "isMacOSWebView", "HTMLAnchorElement", "downloadSaveAs", "msSaveAs", "fileSaverSaveAs", "a", "createElement", "rel", "href", "origin", "location", "target", "URL", "createObjectURL", "setTimeout", "revokeObjectURL", "msSaveOrOpenBlob", "popup", "title", "body", "innerText", "force", "<PERSON><PERSON><PERSON><PERSON>", "isChromeIOS", "FileReader", "reader", "onloadend", "result", "Error", "replace", "assign", "readAsDataURL", "toastMessage", "message", "piniaMessage", "__VUE_DEVTOOLS_TOAST__", "warn", "log", "isPinia", "checkClipboardAccess", "checkNotFocusedError", "toLowerCase", "includes", "actionGlobalCopyState", "clipboard", "writeText", "JSON", "stringify", "state", "value", "actionGlobalPasteState", "loadStoresState", "parse", "readText", "actionGlobalSaveState", "fileInput", "getFile<PERSON><PERSON>er", "accept", "openFile", "Promise", "resolve", "reject", "onchange", "files", "file", "item", "text", "oncancel", "actionGlobalOpenStateFile", "key", "storeState", "formatDisplay", "display", "_custom", "PINIA_ROOT_LABEL", "PINIA_ROOT_ID", "formatStoreForInspectorTree", "store", "id", "label", "$id", "formatStoreForInspectorState", "storeNames", "Array", "from", "_s", "keys", "storeMap", "map", "storeId", "editable", "getters", "filter", "get", "_getters", "reduce", "$state", "length", "getterName", "_customProperties", "size", "customProperties", "formatEventData", "events", "isArray", "data", "event", "push", "operations", "oldValue", "newValue", "operation", "formatMutationType", "direct", "patchFunction", "patchObject", "isTimelineActive", "componentStateTypes", "MUTATIONS_LAYER_ID", "INSPECTOR_ID", "assign$1", "getStoreType", "registerPiniaDevtools", "app", "logo", "packageName", "homepage", "api", "now", "addTimelineLayer", "color", "addInspector", "icon", "treeFilterPlaceholder", "actions", "action", "tooltip", "sendInspectorTree", "sendInspectorState", "nodeActions", "nodeId", "$reset", "on", "inspectComponent", "payload", "ctx", "proxy", "componentInstance", "_pStores", "piniaStores", "values", "for<PERSON>ach", "instanceData", "_isOptionsAPI", "getInspectorTree", "inspectorId", "stores", "concat", "rootNodes", "$pinia", "getInspectorState", "inspectedStore", "$store", "editInspectorState", "path", "has", "unshift", "editComponentState", "startsWith", "addStoreToDevtools", "settings", "logStoreChanges", "defaultValue", "bind", "Date", "$onAction", "after", "onError", "args", "groupId", "runningActionId", "addTimelineEvent", "layerId", "time", "subtitle", "activeAction", "undefined", "logType", "notifyComponentUpdate", "deep", "$subscribe", "eventData", "detached", "flush", "hotUpdate", "_hotUpdate", "newStore", "info", "$dispose", "getSettings", "patchActionForGrouping", "actionNames", "wrapWithProxy", "storeActions", "actionName", "_actionId", "trackedStore", "Proxy", "Reflect", "retValue", "apply", "arguments", "devtoolsPlugin", "options", "_p", "_testing", "originalHotUpdate", "_hmrPayload", "createPinia", "scope", "run", "toBeInstalled", "install", "_a", "provide", "config", "globalProperties", "__VUE_PROD_DEVTOOLS__", "plugin", "use", "_e", "Map", "disposePinia", "stop", "clear", "splice", "isUseStore", "fn", "newState", "oldState", "subPatch", "targetValue", "acceptHMRUpdate", "initialUseStore", "hot", "newModule", "_pinia", "exportName", "useStore", "invalidate", "existingStore", "noop", "addSubscription", "subscriptions", "callback", "onCleanup", "removeSubscription", "idx", "indexOf", "triggerSubscriptions", "slice", "fallbackRunWithContext", "ACTION_MARKER", "ACTION_NAME", "mergeReactiveObjects", "patchToApply", "Set", "add", "hasOwnProperty", "skipHydrateSymbol", "skipHydrate", "obj", "defineProperty", "shouldHydrate", "isComputed", "effect", "createOptionsStore", "initialState", "setup", "localState", "computedGetters", "_r", "createSetupStore", "isOptionsStore", "optionsForPlugin", "active", "$subscribeOptions", "onTrigger", "isListening", "debuggerEvents", "_hotUpdating", "isSyncListening", "actionSubscriptions", "hotState", "activeListener", "$patch", "partialStateOrMutator", "subscriptionMutation", "myListenerId", "then", "delete", "wrappedAction", "afterCallbackList", "onErrorCallbackList", "ret", "catch", "partialStore", "stopWatcher", "runWithContext", "setupStore", "prop", "actionValue", "stateKey", "newStateTarget", "oldStateSource", "actionFn", "getter", "getterV<PERSON>ue", "nonEnumerable", "writable", "configurable", "enumerable", "p", "extender", "extensions", "constructor", "hydrate", "defineStore", "idOrOptions", "setupOptions", "isSetupStore", "hasContext", "hotId", "currentInstance", "vm", "cache", "mapStoreSuffix", "setMapStoreSuffix", "suffix", "mapStores", "reduced", "mapState", "keysOrMapper", "storeKey", "mapGetters", "mapActions", "mapWritableState", "storeToRefs", "rawStore", "refs", "PiniaVuePlugin", "_Vue", "mixin", "beforeCreate", "$options", "_provided", "provideCache", "v", "parent", "destroyed"], "sources": ["D:/dev/CinemaBotV2/frontend/node_modules/pinia/dist/pinia.mjs"], "sourcesContent": ["/*!\n * pinia v2.3.1\n * (c) 2025 <PERSON>\n * @license MIT\n */\nimport { hasInjectionContext, inject, toRaw, watch, unref, markRaw, effectScope, ref, isVue2, isRef, isReactive, set, getCurrentScope, onScopeDispose, getCurrentInstance, reactive, toRef, del, nextTick, computed, toRefs } from 'vue-demi';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\n * setActivePinia must be called to handle SSR at the top of functions like\n * `fetch`, `setup`, `serverPrefetch` and others\n */\nlet activePinia;\n/**\n * Sets or unsets the active pinia. Used in SSR and internally when calling\n * actions and getters\n *\n * @param pinia - Pinia instance\n */\n// @ts-expect-error: cannot constrain the type of the return\nconst setActivePinia = (pinia) => (activePinia = pinia);\n/**\n * Get the currently active pinia if there is any.\n */\nconst getActivePinia = () => (hasInjectionContext() && inject(piniaSymbol)) || activePinia;\nconst piniaSymbol = ((process.env.NODE_ENV !== 'production') ? Symbol('pinia') : /* istanbul ignore next */ Symbol());\n\nfunction isPlainObject(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\no) {\n    return (o &&\n        typeof o === 'object' &&\n        Object.prototype.toString.call(o) === '[object Object]' &&\n        typeof o.toJSON !== 'function');\n}\n// type DeepReadonly<T> = { readonly [P in keyof T]: DeepReadonly<T[P]> }\n// TODO: can we change these to numbers?\n/**\n * Possible types for SubscriptionCallback\n */\nvar MutationType;\n(function (MutationType) {\n    /**\n     * Direct mutation of the state:\n     *\n     * - `store.name = 'new name'`\n     * - `store.$state.name = 'new name'`\n     * - `store.list.push('new item')`\n     */\n    MutationType[\"direct\"] = \"direct\";\n    /**\n     * Mutated the state with `$patch` and an object\n     *\n     * - `store.$patch({ name: 'newName' })`\n     */\n    MutationType[\"patchObject\"] = \"patch object\";\n    /**\n     * Mutated the state with `$patch` and a function\n     *\n     * - `store.$patch(state => state.name = 'newName')`\n     */\n    MutationType[\"patchFunction\"] = \"patch function\";\n    // maybe reset? for $state = {} and $reset\n})(MutationType || (MutationType = {}));\n\nconst IS_CLIENT = typeof window !== 'undefined';\n\n/*\n * FileSaver.js A saveAs() FileSaver implementation.\n *\n * Originally by Eli Grey, adapted as an ESM module by Eduardo San Martin\n * Morote.\n *\n * License : MIT\n */\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nconst _global = /*#__PURE__*/ (() => typeof window === 'object' && window.window === window\n    ? window\n    : typeof self === 'object' && self.self === self\n        ? self\n        : typeof global === 'object' && global.global === global\n            ? global\n            : typeof globalThis === 'object'\n                ? globalThis\n                : { HTMLElement: null })();\nfunction bom(blob, { autoBom = false } = {}) {\n    // prepend BOM for UTF-8 XML and text/* types (including HTML)\n    // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n    if (autoBom &&\n        /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n        return new Blob([String.fromCharCode(0xfeff), blob], { type: blob.type });\n    }\n    return blob;\n}\nfunction download(url, name, opts) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url);\n    xhr.responseType = 'blob';\n    xhr.onload = function () {\n        saveAs(xhr.response, name, opts);\n    };\n    xhr.onerror = function () {\n        console.error('could not download file');\n    };\n    xhr.send();\n}\nfunction corsEnabled(url) {\n    const xhr = new XMLHttpRequest();\n    // use sync to avoid popup blocker\n    xhr.open('HEAD', url, false);\n    try {\n        xhr.send();\n    }\n    catch (e) { }\n    return xhr.status >= 200 && xhr.status <= 299;\n}\n// `a.click()` doesn't work for all browsers (#465)\nfunction click(node) {\n    try {\n        node.dispatchEvent(new MouseEvent('click'));\n    }\n    catch (e) {\n        const evt = document.createEvent('MouseEvents');\n        evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80, 20, false, false, false, false, 0, null);\n        node.dispatchEvent(evt);\n    }\n}\nconst _navigator = typeof navigator === 'object' ? navigator : { userAgent: '' };\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nconst isMacOSWebView = /*#__PURE__*/ (() => /Macintosh/.test(_navigator.userAgent) &&\n    /AppleWebKit/.test(_navigator.userAgent) &&\n    !/Safari/.test(_navigator.userAgent))();\nconst saveAs = !IS_CLIENT\n    ? () => { } // noop\n    : // Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView or mini program\n        typeof HTMLAnchorElement !== 'undefined' &&\n            'download' in HTMLAnchorElement.prototype &&\n            !isMacOSWebView\n            ? downloadSaveAs\n            : // Use msSaveOrOpenBlob as a second approach\n                'msSaveOrOpenBlob' in _navigator\n                    ? msSaveAs\n                    : // Fallback to using FileReader and a popup\n                        fileSaverSaveAs;\nfunction downloadSaveAs(blob, name = 'download', opts) {\n    const a = document.createElement('a');\n    a.download = name;\n    a.rel = 'noopener'; // tabnabbing\n    // TODO: detect chrome extensions & packaged apps\n    // a.target = '_blank'\n    if (typeof blob === 'string') {\n        // Support regular links\n        a.href = blob;\n        if (a.origin !== location.origin) {\n            if (corsEnabled(a.href)) {\n                download(blob, name, opts);\n            }\n            else {\n                a.target = '_blank';\n                click(a);\n            }\n        }\n        else {\n            click(a);\n        }\n    }\n    else {\n        // Support blobs\n        a.href = URL.createObjectURL(blob);\n        setTimeout(function () {\n            URL.revokeObjectURL(a.href);\n        }, 4e4); // 40s\n        setTimeout(function () {\n            click(a);\n        }, 0);\n    }\n}\nfunction msSaveAs(blob, name = 'download', opts) {\n    if (typeof blob === 'string') {\n        if (corsEnabled(blob)) {\n            download(blob, name, opts);\n        }\n        else {\n            const a = document.createElement('a');\n            a.href = blob;\n            a.target = '_blank';\n            setTimeout(function () {\n                click(a);\n            });\n        }\n    }\n    else {\n        // @ts-ignore: works on windows\n        navigator.msSaveOrOpenBlob(bom(blob, opts), name);\n    }\n}\nfunction fileSaverSaveAs(blob, name, opts, popup) {\n    // Open a popup immediately do go around popup blocker\n    // Mostly only available on user interaction and the fileReader is async so...\n    popup = popup || open('', '_blank');\n    if (popup) {\n        popup.document.title = popup.document.body.innerText = 'downloading...';\n    }\n    if (typeof blob === 'string')\n        return download(blob, name, opts);\n    const force = blob.type === 'application/octet-stream';\n    const isSafari = /constructor/i.test(String(_global.HTMLElement)) || 'safari' in _global;\n    const isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent);\n    if ((isChromeIOS || (force && isSafari) || isMacOSWebView) &&\n        typeof FileReader !== 'undefined') {\n        // Safari doesn't allow downloading of blob URLs\n        const reader = new FileReader();\n        reader.onloadend = function () {\n            let url = reader.result;\n            if (typeof url !== 'string') {\n                popup = null;\n                throw new Error('Wrong reader.result type');\n            }\n            url = isChromeIOS\n                ? url\n                : url.replace(/^data:[^;]*;/, 'data:attachment/file;');\n            if (popup) {\n                popup.location.href = url;\n            }\n            else {\n                location.assign(url);\n            }\n            popup = null; // reverse-tabnabbing #460\n        };\n        reader.readAsDataURL(blob);\n    }\n    else {\n        const url = URL.createObjectURL(blob);\n        if (popup)\n            popup.location.assign(url);\n        else\n            location.href = url;\n        popup = null; // reverse-tabnabbing #460\n        setTimeout(function () {\n            URL.revokeObjectURL(url);\n        }, 4e4); // 40s\n    }\n}\n\n/**\n * Shows a toast or console.log\n *\n * @param message - message to log\n * @param type - different color of the tooltip\n */\nfunction toastMessage(message, type) {\n    const piniaMessage = '🍍 ' + message;\n    if (typeof __VUE_DEVTOOLS_TOAST__ === 'function') {\n        // No longer available :(\n        __VUE_DEVTOOLS_TOAST__(piniaMessage, type);\n    }\n    else if (type === 'error') {\n        console.error(piniaMessage);\n    }\n    else if (type === 'warn') {\n        console.warn(piniaMessage);\n    }\n    else {\n        console.log(piniaMessage);\n    }\n}\nfunction isPinia(o) {\n    return '_a' in o && 'install' in o;\n}\n\n/**\n * This file contain devtools actions, they are not Pinia actions.\n */\n// ---\nfunction checkClipboardAccess() {\n    if (!('clipboard' in navigator)) {\n        toastMessage(`Your browser doesn't support the Clipboard API`, 'error');\n        return true;\n    }\n}\nfunction checkNotFocusedError(error) {\n    if (error instanceof Error &&\n        error.message.toLowerCase().includes('document is not focused')) {\n        toastMessage('You need to activate the \"Emulate a focused page\" setting in the \"Rendering\" panel of devtools.', 'warn');\n        return true;\n    }\n    return false;\n}\nasync function actionGlobalCopyState(pinia) {\n    if (checkClipboardAccess())\n        return;\n    try {\n        await navigator.clipboard.writeText(JSON.stringify(pinia.state.value));\n        toastMessage('Global state copied to clipboard.');\n    }\n    catch (error) {\n        if (checkNotFocusedError(error))\n            return;\n        toastMessage(`Failed to serialize the state. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nasync function actionGlobalPasteState(pinia) {\n    if (checkClipboardAccess())\n        return;\n    try {\n        loadStoresState(pinia, JSON.parse(await navigator.clipboard.readText()));\n        toastMessage('Global state pasted from clipboard.');\n    }\n    catch (error) {\n        if (checkNotFocusedError(error))\n            return;\n        toastMessage(`Failed to deserialize the state from clipboard. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nasync function actionGlobalSaveState(pinia) {\n    try {\n        saveAs(new Blob([JSON.stringify(pinia.state.value)], {\n            type: 'text/plain;charset=utf-8',\n        }), 'pinia-state.json');\n    }\n    catch (error) {\n        toastMessage(`Failed to export the state as JSON. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nlet fileInput;\nfunction getFileOpener() {\n    if (!fileInput) {\n        fileInput = document.createElement('input');\n        fileInput.type = 'file';\n        fileInput.accept = '.json';\n    }\n    function openFile() {\n        return new Promise((resolve, reject) => {\n            fileInput.onchange = async () => {\n                const files = fileInput.files;\n                if (!files)\n                    return resolve(null);\n                const file = files.item(0);\n                if (!file)\n                    return resolve(null);\n                return resolve({ text: await file.text(), file });\n            };\n            // @ts-ignore: TODO: changed from 4.3 to 4.4\n            fileInput.oncancel = () => resolve(null);\n            fileInput.onerror = reject;\n            fileInput.click();\n        });\n    }\n    return openFile;\n}\nasync function actionGlobalOpenStateFile(pinia) {\n    try {\n        const open = getFileOpener();\n        const result = await open();\n        if (!result)\n            return;\n        const { text, file } = result;\n        loadStoresState(pinia, JSON.parse(text));\n        toastMessage(`Global state imported from \"${file.name}\".`);\n    }\n    catch (error) {\n        toastMessage(`Failed to import the state from JSON. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nfunction loadStoresState(pinia, state) {\n    for (const key in state) {\n        const storeState = pinia.state.value[key];\n        // store is already instantiated, patch it\n        if (storeState) {\n            Object.assign(storeState, state[key]);\n        }\n        else {\n            // store is not instantiated, set the initial state\n            pinia.state.value[key] = state[key];\n        }\n    }\n}\n\nfunction formatDisplay(display) {\n    return {\n        _custom: {\n            display,\n        },\n    };\n}\nconst PINIA_ROOT_LABEL = '🍍 Pinia (root)';\nconst PINIA_ROOT_ID = '_root';\nfunction formatStoreForInspectorTree(store) {\n    return isPinia(store)\n        ? {\n            id: PINIA_ROOT_ID,\n            label: PINIA_ROOT_LABEL,\n        }\n        : {\n            id: store.$id,\n            label: store.$id,\n        };\n}\nfunction formatStoreForInspectorState(store) {\n    if (isPinia(store)) {\n        const storeNames = Array.from(store._s.keys());\n        const storeMap = store._s;\n        const state = {\n            state: storeNames.map((storeId) => ({\n                editable: true,\n                key: storeId,\n                value: store.state.value[storeId],\n            })),\n            getters: storeNames\n                .filter((id) => storeMap.get(id)._getters)\n                .map((id) => {\n                const store = storeMap.get(id);\n                return {\n                    editable: false,\n                    key: id,\n                    value: store._getters.reduce((getters, key) => {\n                        getters[key] = store[key];\n                        return getters;\n                    }, {}),\n                };\n            }),\n        };\n        return state;\n    }\n    const state = {\n        state: Object.keys(store.$state).map((key) => ({\n            editable: true,\n            key,\n            value: store.$state[key],\n        })),\n    };\n    // avoid adding empty getters\n    if (store._getters && store._getters.length) {\n        state.getters = store._getters.map((getterName) => ({\n            editable: false,\n            key: getterName,\n            value: store[getterName],\n        }));\n    }\n    if (store._customProperties.size) {\n        state.customProperties = Array.from(store._customProperties).map((key) => ({\n            editable: true,\n            key,\n            value: store[key],\n        }));\n    }\n    return state;\n}\nfunction formatEventData(events) {\n    if (!events)\n        return {};\n    if (Array.isArray(events)) {\n        // TODO: handle add and delete for arrays and objects\n        return events.reduce((data, event) => {\n            data.keys.push(event.key);\n            data.operations.push(event.type);\n            data.oldValue[event.key] = event.oldValue;\n            data.newValue[event.key] = event.newValue;\n            return data;\n        }, {\n            oldValue: {},\n            keys: [],\n            operations: [],\n            newValue: {},\n        });\n    }\n    else {\n        return {\n            operation: formatDisplay(events.type),\n            key: formatDisplay(events.key),\n            oldValue: events.oldValue,\n            newValue: events.newValue,\n        };\n    }\n}\nfunction formatMutationType(type) {\n    switch (type) {\n        case MutationType.direct:\n            return 'mutation';\n        case MutationType.patchFunction:\n            return '$patch';\n        case MutationType.patchObject:\n            return '$patch';\n        default:\n            return 'unknown';\n    }\n}\n\n// timeline can be paused when directly changing the state\nlet isTimelineActive = true;\nconst componentStateTypes = [];\nconst MUTATIONS_LAYER_ID = 'pinia:mutations';\nconst INSPECTOR_ID = 'pinia';\nconst { assign: assign$1 } = Object;\n/**\n * Gets the displayed name of a store in devtools\n *\n * @param id - id of the store\n * @returns a formatted string\n */\nconst getStoreType = (id) => '🍍 ' + id;\n/**\n * Add the pinia plugin without any store. Allows displaying a Pinia plugin tab\n * as soon as it is added to the application.\n *\n * @param app - Vue application\n * @param pinia - pinia instance\n */\nfunction registerPiniaDevtools(app, pinia) {\n    setupDevtoolsPlugin({\n        id: 'dev.esm.pinia',\n        label: 'Pinia 🍍',\n        logo: 'https://pinia.vuejs.org/logo.svg',\n        packageName: 'pinia',\n        homepage: 'https://pinia.vuejs.org',\n        componentStateTypes,\n        app,\n    }, (api) => {\n        if (typeof api.now !== 'function') {\n            toastMessage('You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n        }\n        api.addTimelineLayer({\n            id: MUTATIONS_LAYER_ID,\n            label: `Pinia 🍍`,\n            color: 0xe5df88,\n        });\n        api.addInspector({\n            id: INSPECTOR_ID,\n            label: 'Pinia 🍍',\n            icon: 'storage',\n            treeFilterPlaceholder: 'Search stores',\n            actions: [\n                {\n                    icon: 'content_copy',\n                    action: () => {\n                        actionGlobalCopyState(pinia);\n                    },\n                    tooltip: 'Serialize and copy the state',\n                },\n                {\n                    icon: 'content_paste',\n                    action: async () => {\n                        await actionGlobalPasteState(pinia);\n                        api.sendInspectorTree(INSPECTOR_ID);\n                        api.sendInspectorState(INSPECTOR_ID);\n                    },\n                    tooltip: 'Replace the state with the content of your clipboard',\n                },\n                {\n                    icon: 'save',\n                    action: () => {\n                        actionGlobalSaveState(pinia);\n                    },\n                    tooltip: 'Save the state as a JSON file',\n                },\n                {\n                    icon: 'folder_open',\n                    action: async () => {\n                        await actionGlobalOpenStateFile(pinia);\n                        api.sendInspectorTree(INSPECTOR_ID);\n                        api.sendInspectorState(INSPECTOR_ID);\n                    },\n                    tooltip: 'Import the state from a JSON file',\n                },\n            ],\n            nodeActions: [\n                {\n                    icon: 'restore',\n                    tooltip: 'Reset the state (with \"$reset\")',\n                    action: (nodeId) => {\n                        const store = pinia._s.get(nodeId);\n                        if (!store) {\n                            toastMessage(`Cannot reset \"${nodeId}\" store because it wasn't found.`, 'warn');\n                        }\n                        else if (typeof store.$reset !== 'function') {\n                            toastMessage(`Cannot reset \"${nodeId}\" store because it doesn't have a \"$reset\" method implemented.`, 'warn');\n                        }\n                        else {\n                            store.$reset();\n                            toastMessage(`Store \"${nodeId}\" reset.`);\n                        }\n                    },\n                },\n            ],\n        });\n        api.on.inspectComponent((payload, ctx) => {\n            const proxy = (payload.componentInstance &&\n                payload.componentInstance.proxy);\n            if (proxy && proxy._pStores) {\n                const piniaStores = payload.componentInstance.proxy._pStores;\n                Object.values(piniaStores).forEach((store) => {\n                    payload.instanceData.state.push({\n                        type: getStoreType(store.$id),\n                        key: 'state',\n                        editable: true,\n                        value: store._isOptionsAPI\n                            ? {\n                                _custom: {\n                                    value: toRaw(store.$state),\n                                    actions: [\n                                        {\n                                            icon: 'restore',\n                                            tooltip: 'Reset the state of this store',\n                                            action: () => store.$reset(),\n                                        },\n                                    ],\n                                },\n                            }\n                            : // NOTE: workaround to unwrap transferred refs\n                                Object.keys(store.$state).reduce((state, key) => {\n                                    state[key] = store.$state[key];\n                                    return state;\n                                }, {}),\n                    });\n                    if (store._getters && store._getters.length) {\n                        payload.instanceData.state.push({\n                            type: getStoreType(store.$id),\n                            key: 'getters',\n                            editable: false,\n                            value: store._getters.reduce((getters, key) => {\n                                try {\n                                    getters[key] = store[key];\n                                }\n                                catch (error) {\n                                    // @ts-expect-error: we just want to show it in devtools\n                                    getters[key] = error;\n                                }\n                                return getters;\n                            }, {}),\n                        });\n                    }\n                });\n            }\n        });\n        api.on.getInspectorTree((payload) => {\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n                let stores = [pinia];\n                stores = stores.concat(Array.from(pinia._s.values()));\n                payload.rootNodes = (payload.filter\n                    ? stores.filter((store) => '$id' in store\n                        ? store.$id\n                            .toLowerCase()\n                            .includes(payload.filter.toLowerCase())\n                        : PINIA_ROOT_LABEL.toLowerCase().includes(payload.filter.toLowerCase()))\n                    : stores).map(formatStoreForInspectorTree);\n            }\n        });\n        // Expose pinia instance as $pinia to window\n        globalThis.$pinia = pinia;\n        api.on.getInspectorState((payload) => {\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n                const inspectedStore = payload.nodeId === PINIA_ROOT_ID\n                    ? pinia\n                    : pinia._s.get(payload.nodeId);\n                if (!inspectedStore) {\n                    // this could be the selected store restored for a different project\n                    // so it's better not to say anything here\n                    return;\n                }\n                if (inspectedStore) {\n                    // Expose selected store as $store to window\n                    if (payload.nodeId !== PINIA_ROOT_ID)\n                        globalThis.$store = toRaw(inspectedStore);\n                    payload.state = formatStoreForInspectorState(inspectedStore);\n                }\n            }\n        });\n        api.on.editInspectorState((payload, ctx) => {\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n                const inspectedStore = payload.nodeId === PINIA_ROOT_ID\n                    ? pinia\n                    : pinia._s.get(payload.nodeId);\n                if (!inspectedStore) {\n                    return toastMessage(`store \"${payload.nodeId}\" not found`, 'error');\n                }\n                const { path } = payload;\n                if (!isPinia(inspectedStore)) {\n                    // access only the state\n                    if (path.length !== 1 ||\n                        !inspectedStore._customProperties.has(path[0]) ||\n                        path[0] in inspectedStore.$state) {\n                        path.unshift('$state');\n                    }\n                }\n                else {\n                    // Root access, we can omit the `.value` because the devtools API does it for us\n                    path.unshift('state');\n                }\n                isTimelineActive = false;\n                payload.set(inspectedStore, path, payload.state.value);\n                isTimelineActive = true;\n            }\n        });\n        api.on.editComponentState((payload) => {\n            if (payload.type.startsWith('🍍')) {\n                const storeId = payload.type.replace(/^🍍\\s*/, '');\n                const store = pinia._s.get(storeId);\n                if (!store) {\n                    return toastMessage(`store \"${storeId}\" not found`, 'error');\n                }\n                const { path } = payload;\n                if (path[0] !== 'state') {\n                    return toastMessage(`Invalid path for store \"${storeId}\":\\n${path}\\nOnly state can be modified.`);\n                }\n                // rewrite the first entry to be able to directly set the state as\n                // well as any other path\n                path[0] = '$state';\n                isTimelineActive = false;\n                payload.set(store, path, payload.state.value);\n                isTimelineActive = true;\n            }\n        });\n    });\n}\nfunction addStoreToDevtools(app, store) {\n    if (!componentStateTypes.includes(getStoreType(store.$id))) {\n        componentStateTypes.push(getStoreType(store.$id));\n    }\n    setupDevtoolsPlugin({\n        id: 'dev.esm.pinia',\n        label: 'Pinia 🍍',\n        logo: 'https://pinia.vuejs.org/logo.svg',\n        packageName: 'pinia',\n        homepage: 'https://pinia.vuejs.org',\n        componentStateTypes,\n        app,\n        settings: {\n            logStoreChanges: {\n                label: 'Notify about new/deleted stores',\n                type: 'boolean',\n                defaultValue: true,\n            },\n            // useEmojis: {\n            //   label: 'Use emojis in messages ⚡️',\n            //   type: 'boolean',\n            //   defaultValue: true,\n            // },\n        },\n    }, (api) => {\n        // gracefully handle errors\n        const now = typeof api.now === 'function' ? api.now.bind(api) : Date.now;\n        store.$onAction(({ after, onError, name, args }) => {\n            const groupId = runningActionId++;\n            api.addTimelineEvent({\n                layerId: MUTATIONS_LAYER_ID,\n                event: {\n                    time: now(),\n                    title: '🛫 ' + name,\n                    subtitle: 'start',\n                    data: {\n                        store: formatDisplay(store.$id),\n                        action: formatDisplay(name),\n                        args,\n                    },\n                    groupId,\n                },\n            });\n            after((result) => {\n                activeAction = undefined;\n                api.addTimelineEvent({\n                    layerId: MUTATIONS_LAYER_ID,\n                    event: {\n                        time: now(),\n                        title: '🛬 ' + name,\n                        subtitle: 'end',\n                        data: {\n                            store: formatDisplay(store.$id),\n                            action: formatDisplay(name),\n                            args,\n                            result,\n                        },\n                        groupId,\n                    },\n                });\n            });\n            onError((error) => {\n                activeAction = undefined;\n                api.addTimelineEvent({\n                    layerId: MUTATIONS_LAYER_ID,\n                    event: {\n                        time: now(),\n                        logType: 'error',\n                        title: '💥 ' + name,\n                        subtitle: 'end',\n                        data: {\n                            store: formatDisplay(store.$id),\n                            action: formatDisplay(name),\n                            args,\n                            error,\n                        },\n                        groupId,\n                    },\n                });\n            });\n        }, true);\n        store._customProperties.forEach((name) => {\n            watch(() => unref(store[name]), (newValue, oldValue) => {\n                api.notifyComponentUpdate();\n                api.sendInspectorState(INSPECTOR_ID);\n                if (isTimelineActive) {\n                    api.addTimelineEvent({\n                        layerId: MUTATIONS_LAYER_ID,\n                        event: {\n                            time: now(),\n                            title: 'Change',\n                            subtitle: name,\n                            data: {\n                                newValue,\n                                oldValue,\n                            },\n                            groupId: activeAction,\n                        },\n                    });\n                }\n            }, { deep: true });\n        });\n        store.$subscribe(({ events, type }, state) => {\n            api.notifyComponentUpdate();\n            api.sendInspectorState(INSPECTOR_ID);\n            if (!isTimelineActive)\n                return;\n            // rootStore.state[store.id] = state\n            const eventData = {\n                time: now(),\n                title: formatMutationType(type),\n                data: assign$1({ store: formatDisplay(store.$id) }, formatEventData(events)),\n                groupId: activeAction,\n            };\n            if (type === MutationType.patchFunction) {\n                eventData.subtitle = '⤵️';\n            }\n            else if (type === MutationType.patchObject) {\n                eventData.subtitle = '🧩';\n            }\n            else if (events && !Array.isArray(events)) {\n                eventData.subtitle = events.type;\n            }\n            if (events) {\n                eventData.data['rawEvent(s)'] = {\n                    _custom: {\n                        display: 'DebuggerEvent',\n                        type: 'object',\n                        tooltip: 'raw DebuggerEvent[]',\n                        value: events,\n                    },\n                };\n            }\n            api.addTimelineEvent({\n                layerId: MUTATIONS_LAYER_ID,\n                event: eventData,\n            });\n        }, { detached: true, flush: 'sync' });\n        const hotUpdate = store._hotUpdate;\n        store._hotUpdate = markRaw((newStore) => {\n            hotUpdate(newStore);\n            api.addTimelineEvent({\n                layerId: MUTATIONS_LAYER_ID,\n                event: {\n                    time: now(),\n                    title: '🔥 ' + store.$id,\n                    subtitle: 'HMR update',\n                    data: {\n                        store: formatDisplay(store.$id),\n                        info: formatDisplay(`HMR update`),\n                    },\n                },\n            });\n            // update the devtools too\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(INSPECTOR_ID);\n            api.sendInspectorState(INSPECTOR_ID);\n        });\n        const { $dispose } = store;\n        store.$dispose = () => {\n            $dispose();\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(INSPECTOR_ID);\n            api.sendInspectorState(INSPECTOR_ID);\n            api.getSettings().logStoreChanges &&\n                toastMessage(`Disposed \"${store.$id}\" store 🗑`);\n        };\n        // trigger an update so it can display new registered stores\n        api.notifyComponentUpdate();\n        api.sendInspectorTree(INSPECTOR_ID);\n        api.sendInspectorState(INSPECTOR_ID);\n        api.getSettings().logStoreChanges &&\n            toastMessage(`\"${store.$id}\" store installed 🆕`);\n    });\n}\nlet runningActionId = 0;\nlet activeAction;\n/**\n * Patches a store to enable action grouping in devtools by wrapping the store with a Proxy that is passed as the\n * context of all actions, allowing us to set `runningAction` on each access and effectively associating any state\n * mutation to the action.\n *\n * @param store - store to patch\n * @param actionNames - list of actionst to patch\n */\nfunction patchActionForGrouping(store, actionNames, wrapWithProxy) {\n    // original actions of the store as they are given by pinia. We are going to override them\n    const actions = actionNames.reduce((storeActions, actionName) => {\n        // use toRaw to avoid tracking #541\n        storeActions[actionName] = toRaw(store)[actionName];\n        return storeActions;\n    }, {});\n    for (const actionName in actions) {\n        store[actionName] = function () {\n            // the running action id is incremented in a before action hook\n            const _actionId = runningActionId;\n            const trackedStore = wrapWithProxy\n                ? new Proxy(store, {\n                    get(...args) {\n                        activeAction = _actionId;\n                        return Reflect.get(...args);\n                    },\n                    set(...args) {\n                        activeAction = _actionId;\n                        return Reflect.set(...args);\n                    },\n                })\n                : store;\n            // For Setup Stores we need https://github.com/tc39/proposal-async-context\n            activeAction = _actionId;\n            const retValue = actions[actionName].apply(trackedStore, arguments);\n            // this is safer as async actions in Setup Stores would associate mutations done outside of the action\n            activeAction = undefined;\n            return retValue;\n        };\n    }\n}\n/**\n * pinia.use(devtoolsPlugin)\n */\nfunction devtoolsPlugin({ app, store, options }) {\n    // HMR module\n    if (store.$id.startsWith('__hot:')) {\n        return;\n    }\n    // detect option api vs setup api\n    store._isOptionsAPI = !!options.state;\n    // Do not overwrite actions mocked by @pinia/testing (#2298)\n    if (!store._p._testing) {\n        patchActionForGrouping(store, Object.keys(options.actions), store._isOptionsAPI);\n        // Upgrade the HMR to also update the new actions\n        const originalHotUpdate = store._hotUpdate;\n        toRaw(store)._hotUpdate = function (newStore) {\n            originalHotUpdate.apply(this, arguments);\n            patchActionForGrouping(store, Object.keys(newStore._hmrPayload.actions), !!store._isOptionsAPI);\n        };\n    }\n    addStoreToDevtools(app, \n    // FIXME: is there a way to allow the assignment from Store<Id, S, G, A> to StoreGeneric?\n    store);\n}\n\n/**\n * Creates a Pinia instance to be used by the application\n */\nfunction createPinia() {\n    const scope = effectScope(true);\n    // NOTE: here we could check the window object for a state and directly set it\n    // if there is anything like it with Vue 3 SSR\n    const state = scope.run(() => ref({}));\n    let _p = [];\n    // plugins added before calling app.use(pinia)\n    let toBeInstalled = [];\n    const pinia = markRaw({\n        install(app) {\n            // this allows calling useStore() outside of a component setup after\n            // installing pinia's plugin\n            setActivePinia(pinia);\n            if (!isVue2) {\n                pinia._a = app;\n                app.provide(piniaSymbol, pinia);\n                app.config.globalProperties.$pinia = pinia;\n                /* istanbul ignore else */\n                if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n                    registerPiniaDevtools(app, pinia);\n                }\n                toBeInstalled.forEach((plugin) => _p.push(plugin));\n                toBeInstalled = [];\n            }\n        },\n        use(plugin) {\n            if (!this._a && !isVue2) {\n                toBeInstalled.push(plugin);\n            }\n            else {\n                _p.push(plugin);\n            }\n            return this;\n        },\n        _p,\n        // it's actually undefined here\n        // @ts-expect-error\n        _a: null,\n        _e: scope,\n        _s: new Map(),\n        state,\n    });\n    // pinia devtools rely on dev only features so they cannot be forced unless\n    // the dev build of Vue is used. Avoid old browsers like IE11.\n    if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT && typeof Proxy !== 'undefined') {\n        pinia.use(devtoolsPlugin);\n    }\n    return pinia;\n}\n/**\n * Dispose a Pinia instance by stopping its effectScope and removing the state, plugins and stores. This is mostly\n * useful in tests, with both a testing pinia or a regular pinia and in applications that use multiple pinia instances.\n * Once disposed, the pinia instance cannot be used anymore.\n *\n * @param pinia - pinia instance\n */\nfunction disposePinia(pinia) {\n    pinia._e.stop();\n    pinia._s.clear();\n    pinia._p.splice(0);\n    pinia.state.value = {};\n    // @ts-expect-error: non valid\n    pinia._a = null;\n}\n\n/**\n * Checks if a function is a `StoreDefinition`.\n *\n * @param fn - object to test\n * @returns true if `fn` is a StoreDefinition\n */\nconst isUseStore = (fn) => {\n    return typeof fn === 'function' && typeof fn.$id === 'string';\n};\n/**\n * Mutates in place `newState` with `oldState` to _hot update_ it. It will\n * remove any key not existing in `newState` and recursively merge plain\n * objects.\n *\n * @param newState - new state object to be patched\n * @param oldState - old state that should be used to patch newState\n * @returns - newState\n */\nfunction patchObject(newState, oldState) {\n    // no need to go through symbols because they cannot be serialized anyway\n    for (const key in oldState) {\n        const subPatch = oldState[key];\n        // skip the whole sub tree\n        if (!(key in newState)) {\n            continue;\n        }\n        const targetValue = newState[key];\n        if (isPlainObject(targetValue) &&\n            isPlainObject(subPatch) &&\n            !isRef(subPatch) &&\n            !isReactive(subPatch)) {\n            newState[key] = patchObject(targetValue, subPatch);\n        }\n        else {\n            // objects are either a bit more complex (e.g. refs) or primitives, so we\n            // just set the whole thing\n            if (isVue2) {\n                set(newState, key, subPatch);\n            }\n            else {\n                newState[key] = subPatch;\n            }\n        }\n    }\n    return newState;\n}\n/**\n * Creates an _accept_ function to pass to `import.meta.hot` in Vite applications.\n *\n * @example\n * ```js\n * const useUser = defineStore(...)\n * if (import.meta.hot) {\n *   import.meta.hot.accept(acceptHMRUpdate(useUser, import.meta.hot))\n * }\n * ```\n *\n * @param initialUseStore - return of the defineStore to hot update\n * @param hot - `import.meta.hot`\n */\nfunction acceptHMRUpdate(initialUseStore, hot) {\n    // strip as much as possible from iife.prod\n    if (!(process.env.NODE_ENV !== 'production')) {\n        return () => { };\n    }\n    return (newModule) => {\n        const pinia = hot.data.pinia || initialUseStore._pinia;\n        if (!pinia) {\n            // this store is still not used\n            return;\n        }\n        // preserve the pinia instance across loads\n        hot.data.pinia = pinia;\n        // console.log('got data', newStore)\n        for (const exportName in newModule) {\n            const useStore = newModule[exportName];\n            // console.log('checking for', exportName)\n            if (isUseStore(useStore) && pinia._s.has(useStore.$id)) {\n                // console.log('Accepting update for', useStore.$id)\n                const id = useStore.$id;\n                if (id !== initialUseStore.$id) {\n                    console.warn(`The id of the store changed from \"${initialUseStore.$id}\" to \"${id}\". Reloading.`);\n                    // return import.meta.hot.invalidate()\n                    return hot.invalidate();\n                }\n                const existingStore = pinia._s.get(id);\n                if (!existingStore) {\n                    console.log(`[Pinia]: skipping hmr because store doesn't exist yet`);\n                    return;\n                }\n                useStore(pinia, existingStore);\n            }\n        }\n    };\n}\n\nconst noop = () => { };\nfunction addSubscription(subscriptions, callback, detached, onCleanup = noop) {\n    subscriptions.push(callback);\n    const removeSubscription = () => {\n        const idx = subscriptions.indexOf(callback);\n        if (idx > -1) {\n            subscriptions.splice(idx, 1);\n            onCleanup();\n        }\n    };\n    if (!detached && getCurrentScope()) {\n        onScopeDispose(removeSubscription);\n    }\n    return removeSubscription;\n}\nfunction triggerSubscriptions(subscriptions, ...args) {\n    subscriptions.slice().forEach((callback) => {\n        callback(...args);\n    });\n}\n\nconst fallbackRunWithContext = (fn) => fn();\n/**\n * Marks a function as an action for `$onAction`\n * @internal\n */\nconst ACTION_MARKER = Symbol();\n/**\n * Action name symbol. Allows to add a name to an action after defining it\n * @internal\n */\nconst ACTION_NAME = Symbol();\nfunction mergeReactiveObjects(target, patchToApply) {\n    // Handle Map instances\n    if (target instanceof Map && patchToApply instanceof Map) {\n        patchToApply.forEach((value, key) => target.set(key, value));\n    }\n    else if (target instanceof Set && patchToApply instanceof Set) {\n        // Handle Set instances\n        patchToApply.forEach(target.add, target);\n    }\n    // no need to go through symbols because they cannot be serialized anyway\n    for (const key in patchToApply) {\n        if (!patchToApply.hasOwnProperty(key))\n            continue;\n        const subPatch = patchToApply[key];\n        const targetValue = target[key];\n        if (isPlainObject(targetValue) &&\n            isPlainObject(subPatch) &&\n            target.hasOwnProperty(key) &&\n            !isRef(subPatch) &&\n            !isReactive(subPatch)) {\n            // NOTE: here I wanted to warn about inconsistent types but it's not possible because in setup stores one might\n            // start the value of a property as a certain type e.g. a Map, and then for some reason, during SSR, change that\n            // to `undefined`. When trying to hydrate, we want to override the Map with `undefined`.\n            target[key] = mergeReactiveObjects(targetValue, subPatch);\n        }\n        else {\n            // @ts-expect-error: subPatch is a valid value\n            target[key] = subPatch;\n        }\n    }\n    return target;\n}\nconst skipHydrateSymbol = (process.env.NODE_ENV !== 'production')\n    ? Symbol('pinia:skipHydration')\n    : /* istanbul ignore next */ Symbol();\n/**\n * Tells Pinia to skip the hydration process of a given object. This is useful in setup stores (only) when you return a\n * stateful object in the store but it isn't really state. e.g. returning a router instance in a setup store.\n *\n * @param obj - target object\n * @returns obj\n */\nfunction skipHydrate(obj) {\n    return Object.defineProperty(obj, skipHydrateSymbol, {});\n}\n/**\n * Returns whether a value should be hydrated\n *\n * @param obj - target variable\n * @returns true if `obj` should be hydrated\n */\nfunction shouldHydrate(obj) {\n    return !isPlainObject(obj) || !obj.hasOwnProperty(skipHydrateSymbol);\n}\nconst { assign } = Object;\nfunction isComputed(o) {\n    return !!(isRef(o) && o.effect);\n}\nfunction createOptionsStore(id, options, pinia, hot) {\n    const { state, actions, getters } = options;\n    const initialState = pinia.state.value[id];\n    let store;\n    function setup() {\n        if (!initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n            /* istanbul ignore if */\n            if (isVue2) {\n                set(pinia.state.value, id, state ? state() : {});\n            }\n            else {\n                pinia.state.value[id] = state ? state() : {};\n            }\n        }\n        // avoid creating a state in pinia.state.value\n        const localState = (process.env.NODE_ENV !== 'production') && hot\n            ? // use ref() to unwrap refs inside state TODO: check if this is still necessary\n                toRefs(ref(state ? state() : {}).value)\n            : toRefs(pinia.state.value[id]);\n        return assign(localState, actions, Object.keys(getters || {}).reduce((computedGetters, name) => {\n            if ((process.env.NODE_ENV !== 'production') && name in localState) {\n                console.warn(`[🍍]: A getter cannot have the same name as another state property. Rename one of them. Found with \"${name}\" in store \"${id}\".`);\n            }\n            computedGetters[name] = markRaw(computed(() => {\n                setActivePinia(pinia);\n                // it was created just before\n                const store = pinia._s.get(id);\n                // allow cross using stores\n                /* istanbul ignore if */\n                if (isVue2 && !store._r)\n                    return;\n                // @ts-expect-error\n                // return getters![name].call(context, context)\n                // TODO: avoid reading the getter while assigning with a global variable\n                return getters[name].call(store, store);\n            }));\n            return computedGetters;\n        }, {}));\n    }\n    store = createSetupStore(id, setup, options, pinia, hot, true);\n    return store;\n}\nfunction createSetupStore($id, setup, options = {}, pinia, hot, isOptionsStore) {\n    let scope;\n    const optionsForPlugin = assign({ actions: {} }, options);\n    /* istanbul ignore if */\n    if ((process.env.NODE_ENV !== 'production') && !pinia._e.active) {\n        throw new Error('Pinia destroyed');\n    }\n    // watcher options for $subscribe\n    const $subscribeOptions = { deep: true };\n    /* istanbul ignore else */\n    if ((process.env.NODE_ENV !== 'production') && !isVue2) {\n        $subscribeOptions.onTrigger = (event) => {\n            /* istanbul ignore else */\n            if (isListening) {\n                debuggerEvents = event;\n                // avoid triggering this while the store is being built and the state is being set in pinia\n            }\n            else if (isListening == false && !store._hotUpdating) {\n                // let patch send all the events together later\n                /* istanbul ignore else */\n                if (Array.isArray(debuggerEvents)) {\n                    debuggerEvents.push(event);\n                }\n                else {\n                    console.error('🍍 debuggerEvents should be an array. This is most likely an internal Pinia bug.');\n                }\n            }\n        };\n    }\n    // internal state\n    let isListening; // set to true at the end\n    let isSyncListening; // set to true at the end\n    let subscriptions = [];\n    let actionSubscriptions = [];\n    let debuggerEvents;\n    const initialState = pinia.state.value[$id];\n    // avoid setting the state for option stores if it is set\n    // by the setup\n    if (!isOptionsStore && !initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n        /* istanbul ignore if */\n        if (isVue2) {\n            set(pinia.state.value, $id, {});\n        }\n        else {\n            pinia.state.value[$id] = {};\n        }\n    }\n    const hotState = ref({});\n    // avoid triggering too many listeners\n    // https://github.com/vuejs/pinia/issues/1129\n    let activeListener;\n    function $patch(partialStateOrMutator) {\n        let subscriptionMutation;\n        isListening = isSyncListening = false;\n        // reset the debugger events since patches are sync\n        /* istanbul ignore else */\n        if ((process.env.NODE_ENV !== 'production')) {\n            debuggerEvents = [];\n        }\n        if (typeof partialStateOrMutator === 'function') {\n            partialStateOrMutator(pinia.state.value[$id]);\n            subscriptionMutation = {\n                type: MutationType.patchFunction,\n                storeId: $id,\n                events: debuggerEvents,\n            };\n        }\n        else {\n            mergeReactiveObjects(pinia.state.value[$id], partialStateOrMutator);\n            subscriptionMutation = {\n                type: MutationType.patchObject,\n                payload: partialStateOrMutator,\n                storeId: $id,\n                events: debuggerEvents,\n            };\n        }\n        const myListenerId = (activeListener = Symbol());\n        nextTick().then(() => {\n            if (activeListener === myListenerId) {\n                isListening = true;\n            }\n        });\n        isSyncListening = true;\n        // because we paused the watcher, we need to manually call the subscriptions\n        triggerSubscriptions(subscriptions, subscriptionMutation, pinia.state.value[$id]);\n    }\n    const $reset = isOptionsStore\n        ? function $reset() {\n            const { state } = options;\n            const newState = state ? state() : {};\n            // we use a patch to group all changes into one single subscription\n            this.$patch(($state) => {\n                // @ts-expect-error: FIXME: shouldn't error?\n                assign($state, newState);\n            });\n        }\n        : /* istanbul ignore next */\n            (process.env.NODE_ENV !== 'production')\n                ? () => {\n                    throw new Error(`🍍: Store \"${$id}\" is built using the setup syntax and does not implement $reset().`);\n                }\n                : noop;\n    function $dispose() {\n        scope.stop();\n        subscriptions = [];\n        actionSubscriptions = [];\n        pinia._s.delete($id);\n    }\n    /**\n     * Helper that wraps function so it can be tracked with $onAction\n     * @param fn - action to wrap\n     * @param name - name of the action\n     */\n    const action = (fn, name = '') => {\n        if (ACTION_MARKER in fn) {\n            fn[ACTION_NAME] = name;\n            return fn;\n        }\n        const wrappedAction = function () {\n            setActivePinia(pinia);\n            const args = Array.from(arguments);\n            const afterCallbackList = [];\n            const onErrorCallbackList = [];\n            function after(callback) {\n                afterCallbackList.push(callback);\n            }\n            function onError(callback) {\n                onErrorCallbackList.push(callback);\n            }\n            // @ts-expect-error\n            triggerSubscriptions(actionSubscriptions, {\n                args,\n                name: wrappedAction[ACTION_NAME],\n                store,\n                after,\n                onError,\n            });\n            let ret;\n            try {\n                ret = fn.apply(this && this.$id === $id ? this : store, args);\n                // handle sync errors\n            }\n            catch (error) {\n                triggerSubscriptions(onErrorCallbackList, error);\n                throw error;\n            }\n            if (ret instanceof Promise) {\n                return ret\n                    .then((value) => {\n                    triggerSubscriptions(afterCallbackList, value);\n                    return value;\n                })\n                    .catch((error) => {\n                    triggerSubscriptions(onErrorCallbackList, error);\n                    return Promise.reject(error);\n                });\n            }\n            // trigger after callbacks\n            triggerSubscriptions(afterCallbackList, ret);\n            return ret;\n        };\n        wrappedAction[ACTION_MARKER] = true;\n        wrappedAction[ACTION_NAME] = name; // will be set later\n        // @ts-expect-error: we are intentionally limiting the returned type to just Fn\n        // because all the added properties are internals that are exposed through `$onAction()` only\n        return wrappedAction;\n    };\n    const _hmrPayload = /*#__PURE__*/ markRaw({\n        actions: {},\n        getters: {},\n        state: [],\n        hotState,\n    });\n    const partialStore = {\n        _p: pinia,\n        // _s: scope,\n        $id,\n        $onAction: addSubscription.bind(null, actionSubscriptions),\n        $patch,\n        $reset,\n        $subscribe(callback, options = {}) {\n            const removeSubscription = addSubscription(subscriptions, callback, options.detached, () => stopWatcher());\n            const stopWatcher = scope.run(() => watch(() => pinia.state.value[$id], (state) => {\n                if (options.flush === 'sync' ? isSyncListening : isListening) {\n                    callback({\n                        storeId: $id,\n                        type: MutationType.direct,\n                        events: debuggerEvents,\n                    }, state);\n                }\n            }, assign({}, $subscribeOptions, options)));\n            return removeSubscription;\n        },\n        $dispose,\n    };\n    /* istanbul ignore if */\n    if (isVue2) {\n        // start as non ready\n        partialStore._r = false;\n    }\n    const store = reactive((process.env.NODE_ENV !== 'production') || ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT)\n        ? assign({\n            _hmrPayload,\n            _customProperties: markRaw(new Set()), // devtools custom properties\n        }, partialStore\n        // must be added later\n        // setupStore\n        )\n        : partialStore);\n    // store the partial store now so the setup of stores can instantiate each other before they are finished without\n    // creating infinite loops.\n    pinia._s.set($id, store);\n    const runWithContext = (pinia._a && pinia._a.runWithContext) || fallbackRunWithContext;\n    // TODO: idea create skipSerialize that marks properties as non serializable and they are skipped\n    const setupStore = runWithContext(() => pinia._e.run(() => (scope = effectScope()).run(() => setup({ action }))));\n    // overwrite existing actions to support $onAction\n    for (const key in setupStore) {\n        const prop = setupStore[key];\n        if ((isRef(prop) && !isComputed(prop)) || isReactive(prop)) {\n            // mark it as a piece of state to be serialized\n            if ((process.env.NODE_ENV !== 'production') && hot) {\n                set(hotState.value, key, toRef(setupStore, key));\n                // createOptionStore directly sets the state in pinia.state.value so we\n                // can just skip that\n            }\n            else if (!isOptionsStore) {\n                // in setup stores we must hydrate the state and sync pinia state tree with the refs the user just created\n                if (initialState && shouldHydrate(prop)) {\n                    if (isRef(prop)) {\n                        prop.value = initialState[key];\n                    }\n                    else {\n                        // probably a reactive object, lets recursively assign\n                        // @ts-expect-error: prop is unknown\n                        mergeReactiveObjects(prop, initialState[key]);\n                    }\n                }\n                // transfer the ref to the pinia state to keep everything in sync\n                /* istanbul ignore if */\n                if (isVue2) {\n                    set(pinia.state.value[$id], key, prop);\n                }\n                else {\n                    pinia.state.value[$id][key] = prop;\n                }\n            }\n            /* istanbul ignore else */\n            if ((process.env.NODE_ENV !== 'production')) {\n                _hmrPayload.state.push(key);\n            }\n            // action\n        }\n        else if (typeof prop === 'function') {\n            const actionValue = (process.env.NODE_ENV !== 'production') && hot ? prop : action(prop, key);\n            // this a hot module replacement store because the hotUpdate method needs\n            // to do it with the right context\n            /* istanbul ignore if */\n            if (isVue2) {\n                set(setupStore, key, actionValue);\n            }\n            else {\n                // @ts-expect-error\n                setupStore[key] = actionValue;\n            }\n            /* istanbul ignore else */\n            if ((process.env.NODE_ENV !== 'production')) {\n                _hmrPayload.actions[key] = prop;\n            }\n            // list actions so they can be used in plugins\n            // @ts-expect-error\n            optionsForPlugin.actions[key] = prop;\n        }\n        else if ((process.env.NODE_ENV !== 'production')) {\n            // add getters for devtools\n            if (isComputed(prop)) {\n                _hmrPayload.getters[key] = isOptionsStore\n                    ? // @ts-expect-error\n                        options.getters[key]\n                    : prop;\n                if (IS_CLIENT) {\n                    const getters = setupStore._getters ||\n                        // @ts-expect-error: same\n                        (setupStore._getters = markRaw([]));\n                    getters.push(key);\n                }\n            }\n        }\n    }\n    // add the state, getters, and action properties\n    /* istanbul ignore if */\n    if (isVue2) {\n        Object.keys(setupStore).forEach((key) => {\n            set(store, key, setupStore[key]);\n        });\n    }\n    else {\n        assign(store, setupStore);\n        // allows retrieving reactive objects with `storeToRefs()`. Must be called after assigning to the reactive object.\n        // Make `storeToRefs()` work with `reactive()` #799\n        assign(toRaw(store), setupStore);\n    }\n    // use this instead of a computed with setter to be able to create it anywhere\n    // without linking the computed lifespan to wherever the store is first\n    // created.\n    Object.defineProperty(store, '$state', {\n        get: () => ((process.env.NODE_ENV !== 'production') && hot ? hotState.value : pinia.state.value[$id]),\n        set: (state) => {\n            /* istanbul ignore if */\n            if ((process.env.NODE_ENV !== 'production') && hot) {\n                throw new Error('cannot set hotState');\n            }\n            $patch(($state) => {\n                // @ts-expect-error: FIXME: shouldn't error?\n                assign($state, state);\n            });\n        },\n    });\n    // add the hotUpdate before plugins to allow them to override it\n    /* istanbul ignore else */\n    if ((process.env.NODE_ENV !== 'production')) {\n        store._hotUpdate = markRaw((newStore) => {\n            store._hotUpdating = true;\n            newStore._hmrPayload.state.forEach((stateKey) => {\n                if (stateKey in store.$state) {\n                    const newStateTarget = newStore.$state[stateKey];\n                    const oldStateSource = store.$state[stateKey];\n                    if (typeof newStateTarget === 'object' &&\n                        isPlainObject(newStateTarget) &&\n                        isPlainObject(oldStateSource)) {\n                        patchObject(newStateTarget, oldStateSource);\n                    }\n                    else {\n                        // transfer the ref\n                        newStore.$state[stateKey] = oldStateSource;\n                    }\n                }\n                // patch direct access properties to allow store.stateProperty to work as\n                // store.$state.stateProperty\n                set(store, stateKey, toRef(newStore.$state, stateKey));\n            });\n            // remove deleted state properties\n            Object.keys(store.$state).forEach((stateKey) => {\n                if (!(stateKey in newStore.$state)) {\n                    del(store, stateKey);\n                }\n            });\n            // avoid devtools logging this as a mutation\n            isListening = false;\n            isSyncListening = false;\n            pinia.state.value[$id] = toRef(newStore._hmrPayload, 'hotState');\n            isSyncListening = true;\n            nextTick().then(() => {\n                isListening = true;\n            });\n            for (const actionName in newStore._hmrPayload.actions) {\n                const actionFn = newStore[actionName];\n                set(store, actionName, action(actionFn, actionName));\n            }\n            // TODO: does this work in both setup and option store?\n            for (const getterName in newStore._hmrPayload.getters) {\n                const getter = newStore._hmrPayload.getters[getterName];\n                const getterValue = isOptionsStore\n                    ? // special handling of options api\n                        computed(() => {\n                            setActivePinia(pinia);\n                            return getter.call(store, store);\n                        })\n                    : getter;\n                set(store, getterName, getterValue);\n            }\n            // remove deleted getters\n            Object.keys(store._hmrPayload.getters).forEach((key) => {\n                if (!(key in newStore._hmrPayload.getters)) {\n                    del(store, key);\n                }\n            });\n            // remove old actions\n            Object.keys(store._hmrPayload.actions).forEach((key) => {\n                if (!(key in newStore._hmrPayload.actions)) {\n                    del(store, key);\n                }\n            });\n            // update the values used in devtools and to allow deleting new properties later on\n            store._hmrPayload = newStore._hmrPayload;\n            store._getters = newStore._getters;\n            store._hotUpdating = false;\n        });\n    }\n    if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n        const nonEnumerable = {\n            writable: true,\n            configurable: true,\n            // avoid warning on devtools trying to display this property\n            enumerable: false,\n        };\n        ['_p', '_hmrPayload', '_getters', '_customProperties'].forEach((p) => {\n            Object.defineProperty(store, p, assign({ value: store[p] }, nonEnumerable));\n        });\n    }\n    /* istanbul ignore if */\n    if (isVue2) {\n        // mark the store as ready before plugins\n        store._r = true;\n    }\n    // apply all plugins\n    pinia._p.forEach((extender) => {\n        /* istanbul ignore else */\n        if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n            const extensions = scope.run(() => extender({\n                store: store,\n                app: pinia._a,\n                pinia,\n                options: optionsForPlugin,\n            }));\n            Object.keys(extensions || {}).forEach((key) => store._customProperties.add(key));\n            assign(store, extensions);\n        }\n        else {\n            assign(store, scope.run(() => extender({\n                store: store,\n                app: pinia._a,\n                pinia,\n                options: optionsForPlugin,\n            })));\n        }\n    });\n    if ((process.env.NODE_ENV !== 'production') &&\n        store.$state &&\n        typeof store.$state === 'object' &&\n        typeof store.$state.constructor === 'function' &&\n        !store.$state.constructor.toString().includes('[native code]')) {\n        console.warn(`[🍍]: The \"state\" must be a plain object. It cannot be\\n` +\n            `\\tstate: () => new MyClass()\\n` +\n            `Found in store \"${store.$id}\".`);\n    }\n    // only apply hydrate to option stores with an initial state in pinia\n    if (initialState &&\n        isOptionsStore &&\n        options.hydrate) {\n        options.hydrate(store.$state, initialState);\n    }\n    isListening = true;\n    isSyncListening = true;\n    return store;\n}\n// allows unused stores to be tree shaken\n/*! #__NO_SIDE_EFFECTS__ */\nfunction defineStore(\n// TODO: add proper types from above\nidOrOptions, setup, setupOptions) {\n    let id;\n    let options;\n    const isSetupStore = typeof setup === 'function';\n    if (typeof idOrOptions === 'string') {\n        id = idOrOptions;\n        // the option store setup will contain the actual options in this case\n        options = isSetupStore ? setupOptions : setup;\n    }\n    else {\n        options = idOrOptions;\n        id = idOrOptions.id;\n        if ((process.env.NODE_ENV !== 'production') && typeof id !== 'string') {\n            throw new Error(`[🍍]: \"defineStore()\" must be passed a store id as its first argument.`);\n        }\n    }\n    function useStore(pinia, hot) {\n        const hasContext = hasInjectionContext();\n        pinia =\n            // in test mode, ignore the argument provided as we can always retrieve a\n            // pinia instance with getActivePinia()\n            ((process.env.NODE_ENV === 'test') && activePinia && activePinia._testing ? null : pinia) ||\n                (hasContext ? inject(piniaSymbol, null) : null);\n        if (pinia)\n            setActivePinia(pinia);\n        if ((process.env.NODE_ENV !== 'production') && !activePinia) {\n            throw new Error(`[🍍]: \"getActivePinia()\" was called but there was no active Pinia. Are you trying to use a store before calling \"app.use(pinia)\"?\\n` +\n                `See https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\\n` +\n                `This will fail in production.`);\n        }\n        pinia = activePinia;\n        if (!pinia._s.has(id)) {\n            // creating the store registers it in `pinia._s`\n            if (isSetupStore) {\n                createSetupStore(id, setup, options, pinia);\n            }\n            else {\n                createOptionsStore(id, options, pinia);\n            }\n            /* istanbul ignore else */\n            if ((process.env.NODE_ENV !== 'production')) {\n                // @ts-expect-error: not the right inferred type\n                useStore._pinia = pinia;\n            }\n        }\n        const store = pinia._s.get(id);\n        if ((process.env.NODE_ENV !== 'production') && hot) {\n            const hotId = '__hot:' + id;\n            const newStore = isSetupStore\n                ? createSetupStore(hotId, setup, options, pinia, true)\n                : createOptionsStore(hotId, assign({}, options), pinia, true);\n            hot._hotUpdate(newStore);\n            // cleanup the state properties and the store from the cache\n            delete pinia.state.value[hotId];\n            pinia._s.delete(hotId);\n        }\n        if ((process.env.NODE_ENV !== 'production') && IS_CLIENT) {\n            const currentInstance = getCurrentInstance();\n            // save stores in instances to access them devtools\n            if (currentInstance &&\n                currentInstance.proxy &&\n                // avoid adding stores that are just built for hot module replacement\n                !hot) {\n                const vm = currentInstance.proxy;\n                const cache = '_pStores' in vm ? vm._pStores : (vm._pStores = {});\n                cache[id] = store;\n            }\n        }\n        // StoreGeneric cannot be casted towards Store\n        return store;\n    }\n    useStore.$id = id;\n    return useStore;\n}\n\nlet mapStoreSuffix = 'Store';\n/**\n * Changes the suffix added by `mapStores()`. Can be set to an empty string.\n * Defaults to `\"Store\"`. Make sure to extend the MapStoresCustomization\n * interface if you are using TypeScript.\n *\n * @param suffix - new suffix\n */\nfunction setMapStoreSuffix(suffix // could be 'Store' but that would be annoying for JS\n) {\n    mapStoreSuffix = suffix;\n}\n/**\n * Allows using stores without the composition API (`setup()`) by generating an\n * object to be spread in the `computed` field of a component. It accepts a list\n * of store definitions.\n *\n * @example\n * ```js\n * export default {\n *   computed: {\n *     // other computed properties\n *     ...mapStores(useUserStore, useCartStore)\n *   },\n *\n *   created() {\n *     this.userStore // store with id \"user\"\n *     this.cartStore // store with id \"cart\"\n *   }\n * }\n * ```\n *\n * @param stores - list of stores to map to an object\n */\nfunction mapStores(...stores) {\n    if ((process.env.NODE_ENV !== 'production') && Array.isArray(stores[0])) {\n        console.warn(`[🍍]: Directly pass all stores to \"mapStores()\" without putting them in an array:\\n` +\n            `Replace\\n` +\n            `\\tmapStores([useAuthStore, useCartStore])\\n` +\n            `with\\n` +\n            `\\tmapStores(useAuthStore, useCartStore)\\n` +\n            `This will fail in production if not fixed.`);\n        stores = stores[0];\n    }\n    return stores.reduce((reduced, useStore) => {\n        // @ts-expect-error: $id is added by defineStore\n        reduced[useStore.$id + mapStoreSuffix] = function () {\n            return useStore(this.$pinia);\n        };\n        return reduced;\n    }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapState(useStore, keysOrMapper) {\n    return Array.isArray(keysOrMapper)\n        ? keysOrMapper.reduce((reduced, key) => {\n            reduced[key] = function () {\n                // @ts-expect-error: FIXME: should work?\n                return useStore(this.$pinia)[key];\n            };\n            return reduced;\n        }, {})\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\n            // @ts-expect-error\n            reduced[key] = function () {\n                const store = useStore(this.$pinia);\n                const storeKey = keysOrMapper[key];\n                // for some reason TS is unable to infer the type of storeKey to be a\n                // function\n                return typeof storeKey === 'function'\n                    ? storeKey.call(this, store)\n                    : // @ts-expect-error: FIXME: should work?\n                        store[storeKey];\n            };\n            return reduced;\n        }, {});\n}\n/**\n * Alias for `mapState()`. You should use `mapState()` instead.\n * @deprecated use `mapState()` instead.\n */\nconst mapGetters = mapState;\n/**\n * Allows directly using actions from your store without using the composition\n * API (`setup()`) by generating an object to be spread in the `methods` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapActions(useStore, keysOrMapper) {\n    return Array.isArray(keysOrMapper)\n        ? keysOrMapper.reduce((reduced, key) => {\n            // @ts-expect-error\n            reduced[key] = function (...args) {\n                // @ts-expect-error: FIXME: should work?\n                return useStore(this.$pinia)[key](...args);\n            };\n            return reduced;\n        }, {})\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\n            // @ts-expect-error\n            reduced[key] = function (...args) {\n                // @ts-expect-error: FIXME: should work?\n                return useStore(this.$pinia)[keysOrMapper[key]](...args);\n            };\n            return reduced;\n        }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapWritableState(useStore, keysOrMapper) {\n    return Array.isArray(keysOrMapper)\n        ? keysOrMapper.reduce((reduced, key) => {\n            reduced[key] = {\n                get() {\n                    return useStore(this.$pinia)[key];\n                },\n                set(value) {\n                    return (useStore(this.$pinia)[key] = value);\n                },\n            };\n            return reduced;\n        }, {})\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\n            reduced[key] = {\n                get() {\n                    return useStore(this.$pinia)[keysOrMapper[key]];\n                },\n                set(value) {\n                    return (useStore(this.$pinia)[keysOrMapper[key]] = value);\n                },\n            };\n            return reduced;\n        }, {});\n}\n\n/**\n * Creates an object of references with all the state, getters, and plugin-added\n * state properties of the store. Similar to `toRefs()` but specifically\n * designed for Pinia stores so methods and non reactive properties are\n * completely ignored.\n *\n * @param store - store to extract the refs from\n */\nfunction storeToRefs(store) {\n    // See https://github.com/vuejs/pinia/issues/852\n    // It's easier to just use toRefs() even if it includes more stuff\n    if (isVue2) {\n        // @ts-expect-error: toRefs include methods and others\n        return toRefs(store);\n    }\n    else {\n        const rawStore = toRaw(store);\n        const refs = {};\n        for (const key in rawStore) {\n            const value = rawStore[key];\n            // There is no native method to check for a computed\n            // https://github.com/vuejs/core/pull/4165\n            if (value.effect) {\n                // @ts-expect-error: too hard to type correctly\n                refs[key] =\n                    // ...\n                    computed({\n                        get: () => store[key],\n                        set(value) {\n                            store[key] = value;\n                        },\n                    });\n            }\n            else if (isRef(value) || isReactive(value)) {\n                // @ts-expect-error: the key is state or getter\n                refs[key] =\n                    // ---\n                    toRef(store, key);\n            }\n        }\n        return refs;\n    }\n}\n\n/**\n * Vue 2 Plugin that must be installed for pinia to work. Note **you don't need\n * this plugin if you are using Nuxt.js**. Use the `buildModule` instead:\n * https://pinia.vuejs.org/ssr/nuxt.html.\n *\n * @example\n * ```js\n * import Vue from 'vue'\n * import { PiniaVuePlugin, createPinia } from 'pinia'\n *\n * Vue.use(PiniaVuePlugin)\n * const pinia = createPinia()\n *\n * new Vue({\n *   el: '#app',\n *   // ...\n *   pinia,\n * })\n * ```\n *\n * @param _Vue - `Vue` imported from 'vue'.\n */\nconst PiniaVuePlugin = function (_Vue) {\n    // Equivalent of\n    // app.config.globalProperties.$pinia = pinia\n    _Vue.mixin({\n        beforeCreate() {\n            const options = this.$options;\n            if (options.pinia) {\n                const pinia = options.pinia;\n                // HACK: taken from provide(): https://github.com/vuejs/composition-api/blob/main/src/apis/inject.ts#L31\n                /* istanbul ignore else */\n                if (!this._provided) {\n                    const provideCache = {};\n                    Object.defineProperty(this, '_provided', {\n                        get: () => provideCache,\n                        set: (v) => Object.assign(provideCache, v),\n                    });\n                }\n                this._provided[piniaSymbol] = pinia;\n                // propagate the pinia instance in an SSR friendly way\n                // avoid adding it to nuxt twice\n                /* istanbul ignore else */\n                if (!this.$pinia) {\n                    this.$pinia = pinia;\n                }\n                pinia._a = this;\n                if (IS_CLIENT) {\n                    // this allows calling useStore() outside of a component setup after\n                    // installing pinia's plugin\n                    setActivePinia(pinia);\n                }\n                if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n                    registerPiniaDevtools(pinia._a, pinia);\n                }\n            }\n            else if (!this.$pinia && options.parent && options.parent.$pinia) {\n                this.$pinia = options.parent.$pinia;\n            }\n        },\n        destroyed() {\n            delete this._pStores;\n        },\n    });\n};\n\nexport { MutationType, PiniaVuePlugin, acceptHMRUpdate, createPinia, defineStore, disposePinia, getActivePinia, mapActions, mapGetters, mapState, mapStores, mapWritableState, setActivePinia, setMapStoreSuffix, shouldHydrate, skipHydrate, storeToRefs };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,WAAW,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,GAAG,EAAEC,eAAe,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,UAAU;AAC7O,SAASC,mBAAmB,QAAQ,mBAAmB;;AAEvD;AACA;AACA;AACA;AACA,IAAIC,WAAW;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAIC,KAAK,IAAMF,WAAW,GAAGE,KAAM;AACvD;AACA;AACA;AACA,MAAMC,cAAc,GAAGA,CAAA,KAAOzB,mBAAmB,CAAC,CAAC,IAAIC,MAAM,CAACyB,WAAW,CAAC,IAAKJ,WAAW;AAC1F,MAAMI,WAAW,GAAKC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAIC,MAAM,CAAC,OAAO,CAAC,GAAG,0BAA2BA,MAAM,CAAC,CAAE;AAErH,SAASC,aAAaA;AACtB;AACAC,CAAC,EAAE;EACC,OAAQA,CAAC,IACL,OAAOA,CAAC,KAAK,QAAQ,IACrBC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,CAAC,CAAC,KAAK,iBAAiB,IACvD,OAAOA,CAAC,CAACK,MAAM,KAAK,UAAU;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACjC;AACJ;AACA;AACA;AACA;EACIA,YAAY,CAAC,aAAa,CAAC,GAAG,cAAc;EAC5C;AACJ;AACA;AACA;AACA;EACIA,YAAY,CAAC,eAAe,CAAC,GAAG,gBAAgB;EAChD;AACJ,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AAEvC,MAAMC,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,aAAc,CAAC,MAAM,OAAOD,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACA,MAAM,KAAKA,MAAM,GACrFA,MAAM,GACN,OAAOE,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACA,IAAI,KAAKA,IAAI,GAC1CA,IAAI,GACJ,OAAOC,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACA,MAAM,KAAKA,MAAM,GAClDA,MAAM,GACN,OAAOC,UAAU,KAAK,QAAQ,GAC1BA,UAAU,GACV;EAAEC,WAAW,EAAE;AAAK,CAAC,EAAE,CAAC;AAC1C,SAASC,GAAGA,CAACC,IAAI,EAAE;EAAEC,OAAO,GAAG;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EACzC;EACA;EACA,IAAIA,OAAO,IACP,4EAA4E,CAACC,IAAI,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;IAC9F,OAAO,IAAIC,IAAI,CAAC,CAACC,MAAM,CAACC,YAAY,CAAC,MAAM,CAAC,EAAEN,IAAI,CAAC,EAAE;MAAEG,IAAI,EAAEH,IAAI,CAACG;IAAK,CAAC,CAAC;EAC7E;EACA,OAAOH,IAAI;AACf;AACA,SAASO,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC/B,MAAMC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;EAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAEL,GAAG,CAAC;EACpBG,GAAG,CAACG,YAAY,GAAG,MAAM;EACzBH,GAAG,CAACI,MAAM,GAAG,YAAY;IACrBC,MAAM,CAACL,GAAG,CAACM,QAAQ,EAAER,IAAI,EAAEC,IAAI,CAAC;EACpC,CAAC;EACDC,GAAG,CAACO,OAAO,GAAG,YAAY;IACtBC,OAAO,CAACC,KAAK,CAAC,yBAAyB,CAAC;EAC5C,CAAC;EACDT,GAAG,CAACU,IAAI,CAAC,CAAC;AACd;AACA,SAASC,WAAWA,CAACd,GAAG,EAAE;EACtB,MAAMG,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;EAChC;EACAD,GAAG,CAACE,IAAI,CAAC,MAAM,EAAEL,GAAG,EAAE,KAAK,CAAC;EAC5B,IAAI;IACAG,GAAG,CAACU,IAAI,CAAC,CAAC;EACd,CAAC,CACD,OAAOE,CAAC,EAAE,CAAE;EACZ,OAAOZ,GAAG,CAACa,MAAM,IAAI,GAAG,IAAIb,GAAG,CAACa,MAAM,IAAI,GAAG;AACjD;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAE;EACjB,IAAI;IACAA,IAAI,CAACC,aAAa,CAAC,IAAIC,UAAU,CAAC,OAAO,CAAC,CAAC;EAC/C,CAAC,CACD,OAAOL,CAAC,EAAE;IACN,MAAMM,GAAG,GAAGC,QAAQ,CAACC,WAAW,CAAC,aAAa,CAAC;IAC/CF,GAAG,CAACG,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAEvC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC;IACrGiC,IAAI,CAACC,aAAa,CAACE,GAAG,CAAC;EAC3B;AACJ;AACA,MAAMI,UAAU,GAAG,OAAOC,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG;EAAEC,SAAS,EAAE;AAAG,CAAC;AAChF;AACA;AACA;AACA,MAAMC,cAAc,GAAG,aAAc,CAAC,MAAM,WAAW,CAAClC,IAAI,CAAC+B,UAAU,CAACE,SAAS,CAAC,IAC9E,aAAa,CAACjC,IAAI,CAAC+B,UAAU,CAACE,SAAS,CAAC,IACxC,CAAC,QAAQ,CAACjC,IAAI,CAAC+B,UAAU,CAACE,SAAS,CAAC,EAAE,CAAC;AAC3C,MAAMnB,MAAM,GAAG,CAACxB,SAAS,GACnB,MAAM,CAAE,CAAC,CAAC;AAAA;AACV;AACE,OAAO6C,iBAAiB,KAAK,WAAW,IACpC,UAAU,IAAIA,iBAAiB,CAAClD,SAAS,IACzC,CAACiD,cAAc,GACbE,cAAc;AACd;AACE,kBAAkB,IAAIL,UAAU,GAC1BM,QAAQ;AACR;AACEC,eAAe;AACvC,SAASF,cAAcA,CAACtC,IAAI,EAAES,IAAI,GAAG,UAAU,EAAEC,IAAI,EAAE;EACnD,MAAM+B,CAAC,GAAGX,QAAQ,CAACY,aAAa,CAAC,GAAG,CAAC;EACrCD,CAAC,CAAClC,QAAQ,GAAGE,IAAI;EACjBgC,CAAC,CAACE,GAAG,GAAG,UAAU,CAAC,CAAC;EACpB;EACA;EACA,IAAI,OAAO3C,IAAI,KAAK,QAAQ,EAAE;IAC1B;IACAyC,CAAC,CAACG,IAAI,GAAG5C,IAAI;IACb,IAAIyC,CAAC,CAACI,MAAM,KAAKC,QAAQ,CAACD,MAAM,EAAE;MAC9B,IAAIvB,WAAW,CAACmB,CAAC,CAACG,IAAI,CAAC,EAAE;QACrBrC,QAAQ,CAACP,IAAI,EAAES,IAAI,EAAEC,IAAI,CAAC;MAC9B,CAAC,MACI;QACD+B,CAAC,CAACM,MAAM,GAAG,QAAQ;QACnBtB,KAAK,CAACgB,CAAC,CAAC;MACZ;IACJ,CAAC,MACI;MACDhB,KAAK,CAACgB,CAAC,CAAC;IACZ;EACJ,CAAC,MACI;IACD;IACAA,CAAC,CAACG,IAAI,GAAGI,GAAG,CAACC,eAAe,CAACjD,IAAI,CAAC;IAClCkD,UAAU,CAAC,YAAY;MACnBF,GAAG,CAACG,eAAe,CAACV,CAAC,CAACG,IAAI,CAAC;IAC/B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACTM,UAAU,CAAC,YAAY;MACnBzB,KAAK,CAACgB,CAAC,CAAC;IACZ,CAAC,EAAE,CAAC,CAAC;EACT;AACJ;AACA,SAASF,QAAQA,CAACvC,IAAI,EAAES,IAAI,GAAG,UAAU,EAAEC,IAAI,EAAE;EAC7C,IAAI,OAAOV,IAAI,KAAK,QAAQ,EAAE;IAC1B,IAAIsB,WAAW,CAACtB,IAAI,CAAC,EAAE;MACnBO,QAAQ,CAACP,IAAI,EAAES,IAAI,EAAEC,IAAI,CAAC;IAC9B,CAAC,MACI;MACD,MAAM+B,CAAC,GAAGX,QAAQ,CAACY,aAAa,CAAC,GAAG,CAAC;MACrCD,CAAC,CAACG,IAAI,GAAG5C,IAAI;MACbyC,CAAC,CAACM,MAAM,GAAG,QAAQ;MACnBG,UAAU,CAAC,YAAY;QACnBzB,KAAK,CAACgB,CAAC,CAAC;MACZ,CAAC,CAAC;IACN;EACJ,CAAC,MACI;IACD;IACAP,SAAS,CAACkB,gBAAgB,CAACrD,GAAG,CAACC,IAAI,EAAEU,IAAI,CAAC,EAAED,IAAI,CAAC;EACrD;AACJ;AACA,SAAS+B,eAAeA,CAACxC,IAAI,EAAES,IAAI,EAAEC,IAAI,EAAE2C,KAAK,EAAE;EAC9C;EACA;EACAA,KAAK,GAAGA,KAAK,IAAIxC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;EACnC,IAAIwC,KAAK,EAAE;IACPA,KAAK,CAACvB,QAAQ,CAACwB,KAAK,GAAGD,KAAK,CAACvB,QAAQ,CAACyB,IAAI,CAACC,SAAS,GAAG,gBAAgB;EAC3E;EACA,IAAI,OAAOxD,IAAI,KAAK,QAAQ,EACxB,OAAOO,QAAQ,CAACP,IAAI,EAAES,IAAI,EAAEC,IAAI,CAAC;EACrC,MAAM+C,KAAK,GAAGzD,IAAI,CAACG,IAAI,KAAK,0BAA0B;EACtD,MAAMuD,QAAQ,GAAG,cAAc,CAACxD,IAAI,CAACG,MAAM,CAACX,OAAO,CAACI,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAIJ,OAAO;EACxF,MAAMiE,WAAW,GAAG,cAAc,CAACzD,IAAI,CAACgC,SAAS,CAACC,SAAS,CAAC;EAC5D,IAAI,CAACwB,WAAW,IAAKF,KAAK,IAAIC,QAAS,IAAItB,cAAc,KACrD,OAAOwB,UAAU,KAAK,WAAW,EAAE;IACnC;IACA,MAAMC,MAAM,GAAG,IAAID,UAAU,CAAC,CAAC;IAC/BC,MAAM,CAACC,SAAS,GAAG,YAAY;MAC3B,IAAItD,GAAG,GAAGqD,MAAM,CAACE,MAAM;MACvB,IAAI,OAAOvD,GAAG,KAAK,QAAQ,EAAE;QACzB6C,KAAK,GAAG,IAAI;QACZ,MAAM,IAAIW,KAAK,CAAC,0BAA0B,CAAC;MAC/C;MACAxD,GAAG,GAAGmD,WAAW,GACXnD,GAAG,GACHA,GAAG,CAACyD,OAAO,CAAC,cAAc,EAAE,uBAAuB,CAAC;MAC1D,IAAIZ,KAAK,EAAE;QACPA,KAAK,CAACP,QAAQ,CAACF,IAAI,GAAGpC,GAAG;MAC7B,CAAC,MACI;QACDsC,QAAQ,CAACoB,MAAM,CAAC1D,GAAG,CAAC;MACxB;MACA6C,KAAK,GAAG,IAAI,CAAC,CAAC;IAClB,CAAC;IACDQ,MAAM,CAACM,aAAa,CAACnE,IAAI,CAAC;EAC9B,CAAC,MACI;IACD,MAAMQ,GAAG,GAAGwC,GAAG,CAACC,eAAe,CAACjD,IAAI,CAAC;IACrC,IAAIqD,KAAK,EACLA,KAAK,CAACP,QAAQ,CAACoB,MAAM,CAAC1D,GAAG,CAAC,CAAC,KAE3BsC,QAAQ,CAACF,IAAI,GAAGpC,GAAG;IACvB6C,KAAK,GAAG,IAAI,CAAC,CAAC;IACdH,UAAU,CAAC,YAAY;MACnBF,GAAG,CAACG,eAAe,CAAC3C,GAAG,CAAC;IAC5B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACb;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4D,YAAYA,CAACC,OAAO,EAAElE,IAAI,EAAE;EACjC,MAAMmE,YAAY,GAAG,KAAK,GAAGD,OAAO;EACpC,IAAI,OAAOE,sBAAsB,KAAK,UAAU,EAAE;IAC9C;IACAA,sBAAsB,CAACD,YAAY,EAAEnE,IAAI,CAAC;EAC9C,CAAC,MACI,IAAIA,IAAI,KAAK,OAAO,EAAE;IACvBgB,OAAO,CAACC,KAAK,CAACkD,YAAY,CAAC;EAC/B,CAAC,MACI,IAAInE,IAAI,KAAK,MAAM,EAAE;IACtBgB,OAAO,CAACqD,IAAI,CAACF,YAAY,CAAC;EAC9B,CAAC,MACI;IACDnD,OAAO,CAACsD,GAAG,CAACH,YAAY,CAAC;EAC7B;AACJ;AACA,SAASI,OAAOA,CAACzF,CAAC,EAAE;EAChB,OAAO,IAAI,IAAIA,CAAC,IAAI,SAAS,IAAIA,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA,SAAS0F,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,EAAE,WAAW,IAAIzC,SAAS,CAAC,EAAE;IAC7BkC,YAAY,CAAC,gDAAgD,EAAE,OAAO,CAAC;IACvE,OAAO,IAAI;EACf;AACJ;AACA,SAASQ,oBAAoBA,CAACxD,KAAK,EAAE;EACjC,IAAIA,KAAK,YAAY4C,KAAK,IACtB5C,KAAK,CAACiD,OAAO,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,yBAAyB,CAAC,EAAE;IACjEV,YAAY,CAAC,iGAAiG,EAAE,MAAM,CAAC;IACvH,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA,eAAeW,qBAAqBA,CAACtG,KAAK,EAAE;EACxC,IAAIkG,oBAAoB,CAAC,CAAC,EACtB;EACJ,IAAI;IACA,MAAMzC,SAAS,CAAC8C,SAAS,CAACC,SAAS,CAACC,IAAI,CAACC,SAAS,CAAC1G,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC,CAAC;IACtEjB,YAAY,CAAC,mCAAmC,CAAC;EACrD,CAAC,CACD,OAAOhD,KAAK,EAAE;IACV,IAAIwD,oBAAoB,CAACxD,KAAK,CAAC,EAC3B;IACJgD,YAAY,CAAC,oEAAoE,EAAE,OAAO,CAAC;IAC3FjD,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AACA,eAAekE,sBAAsBA,CAAC7G,KAAK,EAAE;EACzC,IAAIkG,oBAAoB,CAAC,CAAC,EACtB;EACJ,IAAI;IACAY,eAAe,CAAC9G,KAAK,EAAEyG,IAAI,CAACM,KAAK,CAAC,MAAMtD,SAAS,CAAC8C,SAAS,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxErB,YAAY,CAAC,qCAAqC,CAAC;EACvD,CAAC,CACD,OAAOhD,KAAK,EAAE;IACV,IAAIwD,oBAAoB,CAACxD,KAAK,CAAC,EAC3B;IACJgD,YAAY,CAAC,qFAAqF,EAAE,OAAO,CAAC;IAC5GjD,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AACA,eAAesE,qBAAqBA,CAACjH,KAAK,EAAE;EACxC,IAAI;IACAuC,MAAM,CAAC,IAAIZ,IAAI,CAAC,CAAC8E,IAAI,CAACC,SAAS,CAAC1G,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE;MACjDlF,IAAI,EAAE;IACV,CAAC,CAAC,EAAE,kBAAkB,CAAC;EAC3B,CAAC,CACD,OAAOiB,KAAK,EAAE;IACVgD,YAAY,CAAC,yEAAyE,EAAE,OAAO,CAAC;IAChGjD,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AACA,IAAIuE,SAAS;AACb,SAASC,aAAaA,CAAA,EAAG;EACrB,IAAI,CAACD,SAAS,EAAE;IACZA,SAAS,GAAG7D,QAAQ,CAACY,aAAa,CAAC,OAAO,CAAC;IAC3CiD,SAAS,CAACxF,IAAI,GAAG,MAAM;IACvBwF,SAAS,CAACE,MAAM,GAAG,OAAO;EAC9B;EACA,SAASC,QAAQA,CAAA,EAAG;IAChB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpCN,SAAS,CAACO,QAAQ,GAAG,YAAY;QAC7B,MAAMC,KAAK,GAAGR,SAAS,CAACQ,KAAK;QAC7B,IAAI,CAACA,KAAK,EACN,OAAOH,OAAO,CAAC,IAAI,CAAC;QACxB,MAAMI,IAAI,GAAGD,KAAK,CAACE,IAAI,CAAC,CAAC,CAAC;QAC1B,IAAI,CAACD,IAAI,EACL,OAAOJ,OAAO,CAAC,IAAI,CAAC;QACxB,OAAOA,OAAO,CAAC;UAAEM,IAAI,EAAE,MAAMF,IAAI,CAACE,IAAI,CAAC,CAAC;UAAEF;QAAK,CAAC,CAAC;MACrD,CAAC;MACD;MACAT,SAAS,CAACY,QAAQ,GAAG,MAAMP,OAAO,CAAC,IAAI,CAAC;MACxCL,SAAS,CAACzE,OAAO,GAAG+E,MAAM;MAC1BN,SAAS,CAAClE,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC;EACN;EACA,OAAOqE,QAAQ;AACnB;AACA,eAAeU,yBAAyBA,CAAC/H,KAAK,EAAE;EAC5C,IAAI;IACA,MAAMoC,IAAI,GAAG+E,aAAa,CAAC,CAAC;IAC5B,MAAM7B,MAAM,GAAG,MAAMlD,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACkD,MAAM,EACP;IACJ,MAAM;MAAEuC,IAAI;MAAEF;IAAK,CAAC,GAAGrC,MAAM;IAC7BwB,eAAe,CAAC9G,KAAK,EAAEyG,IAAI,CAACM,KAAK,CAACc,IAAI,CAAC,CAAC;IACxClC,YAAY,CAAC,+BAA+BgC,IAAI,CAAC3F,IAAI,IAAI,CAAC;EAC9D,CAAC,CACD,OAAOW,KAAK,EAAE;IACVgD,YAAY,CAAC,2EAA2E,EAAE,OAAO,CAAC;IAClGjD,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AACA,SAASmE,eAAeA,CAAC9G,KAAK,EAAE2G,KAAK,EAAE;EACnC,KAAK,MAAMqB,GAAG,IAAIrB,KAAK,EAAE;IACrB,MAAMsB,UAAU,GAAGjI,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAACoB,GAAG,CAAC;IACzC;IACA,IAAIC,UAAU,EAAE;MACZxH,MAAM,CAACgF,MAAM,CAACwC,UAAU,EAAEtB,KAAK,CAACqB,GAAG,CAAC,CAAC;IACzC,CAAC,MACI;MACD;MACAhI,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAACoB,GAAG,CAAC,GAAGrB,KAAK,CAACqB,GAAG,CAAC;IACvC;EACJ;AACJ;AAEA,SAASE,aAAaA,CAACC,OAAO,EAAE;EAC5B,OAAO;IACHC,OAAO,EAAE;MACLD;IACJ;EACJ,CAAC;AACL;AACA,MAAME,gBAAgB,GAAG,iBAAiB;AAC1C,MAAMC,aAAa,GAAG,OAAO;AAC7B,SAASC,2BAA2BA,CAACC,KAAK,EAAE;EACxC,OAAOvC,OAAO,CAACuC,KAAK,CAAC,GACf;IACEC,EAAE,EAAEH,aAAa;IACjBI,KAAK,EAAEL;EACX,CAAC,GACC;IACEI,EAAE,EAAED,KAAK,CAACG,GAAG;IACbD,KAAK,EAAEF,KAAK,CAACG;EACjB,CAAC;AACT;AACA,SAASC,4BAA4BA,CAACJ,KAAK,EAAE;EACzC,IAAIvC,OAAO,CAACuC,KAAK,CAAC,EAAE;IAChB,MAAMK,UAAU,GAAGC,KAAK,CAACC,IAAI,CAACP,KAAK,CAACQ,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC;IAC9C,MAAMC,QAAQ,GAAGV,KAAK,CAACQ,EAAE;IACzB,MAAMrC,KAAK,GAAG;MACVA,KAAK,EAAEkC,UAAU,CAACM,GAAG,CAAEC,OAAO,KAAM;QAChCC,QAAQ,EAAE,IAAI;QACdrB,GAAG,EAAEoB,OAAO;QACZxC,KAAK,EAAE4B,KAAK,CAAC7B,KAAK,CAACC,KAAK,CAACwC,OAAO;MACpC,CAAC,CAAC,CAAC;MACHE,OAAO,EAAET,UAAU,CACdU,MAAM,CAAEd,EAAE,IAAKS,QAAQ,CAACM,GAAG,CAACf,EAAE,CAAC,CAACgB,QAAQ,CAAC,CACzCN,GAAG,CAAEV,EAAE,IAAK;QACb,MAAMD,KAAK,GAAGU,QAAQ,CAACM,GAAG,CAACf,EAAE,CAAC;QAC9B,OAAO;UACHY,QAAQ,EAAE,KAAK;UACfrB,GAAG,EAAES,EAAE;UACP7B,KAAK,EAAE4B,KAAK,CAACiB,QAAQ,CAACC,MAAM,CAAC,CAACJ,OAAO,EAAEtB,GAAG,KAAK;YAC3CsB,OAAO,CAACtB,GAAG,CAAC,GAAGQ,KAAK,CAACR,GAAG,CAAC;YACzB,OAAOsB,OAAO;UAClB,CAAC,EAAE,CAAC,CAAC;QACT,CAAC;MACL,CAAC;IACL,CAAC;IACD,OAAO3C,KAAK;EAChB;EACA,MAAMA,KAAK,GAAG;IACVA,KAAK,EAAElG,MAAM,CAACwI,IAAI,CAACT,KAAK,CAACmB,MAAM,CAAC,CAACR,GAAG,CAAEnB,GAAG,KAAM;MAC3CqB,QAAQ,EAAE,IAAI;MACdrB,GAAG;MACHpB,KAAK,EAAE4B,KAAK,CAACmB,MAAM,CAAC3B,GAAG;IAC3B,CAAC,CAAC;EACN,CAAC;EACD;EACA,IAAIQ,KAAK,CAACiB,QAAQ,IAAIjB,KAAK,CAACiB,QAAQ,CAACG,MAAM,EAAE;IACzCjD,KAAK,CAAC2C,OAAO,GAAGd,KAAK,CAACiB,QAAQ,CAACN,GAAG,CAAEU,UAAU,KAAM;MAChDR,QAAQ,EAAE,KAAK;MACfrB,GAAG,EAAE6B,UAAU;MACfjD,KAAK,EAAE4B,KAAK,CAACqB,UAAU;IAC3B,CAAC,CAAC,CAAC;EACP;EACA,IAAIrB,KAAK,CAACsB,iBAAiB,CAACC,IAAI,EAAE;IAC9BpD,KAAK,CAACqD,gBAAgB,GAAGlB,KAAK,CAACC,IAAI,CAACP,KAAK,CAACsB,iBAAiB,CAAC,CAACX,GAAG,CAAEnB,GAAG,KAAM;MACvEqB,QAAQ,EAAE,IAAI;MACdrB,GAAG;MACHpB,KAAK,EAAE4B,KAAK,CAACR,GAAG;IACpB,CAAC,CAAC,CAAC;EACP;EACA,OAAOrB,KAAK;AAChB;AACA,SAASsD,eAAeA,CAACC,MAAM,EAAE;EAC7B,IAAI,CAACA,MAAM,EACP,OAAO,CAAC,CAAC;EACb,IAAIpB,KAAK,CAACqB,OAAO,CAACD,MAAM,CAAC,EAAE;IACvB;IACA,OAAOA,MAAM,CAACR,MAAM,CAAC,CAACU,IAAI,EAAEC,KAAK,KAAK;MAClCD,IAAI,CAACnB,IAAI,CAACqB,IAAI,CAACD,KAAK,CAACrC,GAAG,CAAC;MACzBoC,IAAI,CAACG,UAAU,CAACD,IAAI,CAACD,KAAK,CAAC3I,IAAI,CAAC;MAChC0I,IAAI,CAACI,QAAQ,CAACH,KAAK,CAACrC,GAAG,CAAC,GAAGqC,KAAK,CAACG,QAAQ;MACzCJ,IAAI,CAACK,QAAQ,CAACJ,KAAK,CAACrC,GAAG,CAAC,GAAGqC,KAAK,CAACI,QAAQ;MACzC,OAAOL,IAAI;IACf,CAAC,EAAE;MACCI,QAAQ,EAAE,CAAC,CAAC;MACZvB,IAAI,EAAE,EAAE;MACRsB,UAAU,EAAE,EAAE;MACdE,QAAQ,EAAE,CAAC;IACf,CAAC,CAAC;EACN,CAAC,MACI;IACD,OAAO;MACHC,SAAS,EAAExC,aAAa,CAACgC,MAAM,CAACxI,IAAI,CAAC;MACrCsG,GAAG,EAAEE,aAAa,CAACgC,MAAM,CAAClC,GAAG,CAAC;MAC9BwC,QAAQ,EAAEN,MAAM,CAACM,QAAQ;MACzBC,QAAQ,EAAEP,MAAM,CAACO;IACrB,CAAC;EACL;AACJ;AACA,SAASE,kBAAkBA,CAACjJ,IAAI,EAAE;EAC9B,QAAQA,IAAI;IACR,KAAKZ,YAAY,CAAC8J,MAAM;MACpB,OAAO,UAAU;IACrB,KAAK9J,YAAY,CAAC+J,aAAa;MAC3B,OAAO,QAAQ;IACnB,KAAK/J,YAAY,CAACgK,WAAW;MACzB,OAAO,QAAQ;IACnB;MACI,OAAO,SAAS;EACxB;AACJ;;AAEA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,MAAMC,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,kBAAkB,GAAG,iBAAiB;AAC5C,MAAMC,YAAY,GAAG,OAAO;AAC5B,MAAM;EAAEzF,MAAM,EAAE0F;AAAS,CAAC,GAAG1K,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2K,YAAY,GAAI3C,EAAE,IAAK,KAAK,GAAGA,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4C,qBAAqBA,CAACC,GAAG,EAAEtL,KAAK,EAAE;EACvCH,mBAAmB,CAAC;IAChB4I,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,UAAU;IACjB6C,IAAI,EAAE,kCAAkC;IACxCC,WAAW,EAAE,OAAO;IACpBC,QAAQ,EAAE,yBAAyB;IACnCT,mBAAmB;IACnBM;EACJ,CAAC,EAAGI,GAAG,IAAK;IACR,IAAI,OAAOA,GAAG,CAACC,GAAG,KAAK,UAAU,EAAE;MAC/BhG,YAAY,CAAC,yMAAyM,CAAC;IAC3N;IACA+F,GAAG,CAACE,gBAAgB,CAAC;MACjBnD,EAAE,EAAEwC,kBAAkB;MACtBvC,KAAK,EAAE,UAAU;MACjBmD,KAAK,EAAE;IACX,CAAC,CAAC;IACFH,GAAG,CAACI,YAAY,CAAC;MACbrD,EAAE,EAAEyC,YAAY;MAChBxC,KAAK,EAAE,UAAU;MACjBqD,IAAI,EAAE,SAAS;MACfC,qBAAqB,EAAE,eAAe;MACtCC,OAAO,EAAE,CACL;QACIF,IAAI,EAAE,cAAc;QACpBG,MAAM,EAAEA,CAAA,KAAM;UACV5F,qBAAqB,CAACtG,KAAK,CAAC;QAChC,CAAC;QACDmM,OAAO,EAAE;MACb,CAAC,EACD;QACIJ,IAAI,EAAE,eAAe;QACrBG,MAAM,EAAE,MAAAA,CAAA,KAAY;UAChB,MAAMrF,sBAAsB,CAAC7G,KAAK,CAAC;UACnC0L,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;UACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;QACxC,CAAC;QACDiB,OAAO,EAAE;MACb,CAAC,EACD;QACIJ,IAAI,EAAE,MAAM;QACZG,MAAM,EAAEA,CAAA,KAAM;UACVjF,qBAAqB,CAACjH,KAAK,CAAC;QAChC,CAAC;QACDmM,OAAO,EAAE;MACb,CAAC,EACD;QACIJ,IAAI,EAAE,aAAa;QACnBG,MAAM,EAAE,MAAAA,CAAA,KAAY;UAChB,MAAMnE,yBAAyB,CAAC/H,KAAK,CAAC;UACtC0L,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;UACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;QACxC,CAAC;QACDiB,OAAO,EAAE;MACb,CAAC,CACJ;MACDG,WAAW,EAAE,CACT;QACIP,IAAI,EAAE,SAAS;QACfI,OAAO,EAAE,iCAAiC;QAC1CD,MAAM,EAAGK,MAAM,IAAK;UAChB,MAAM/D,KAAK,GAAGxI,KAAK,CAACgJ,EAAE,CAACQ,GAAG,CAAC+C,MAAM,CAAC;UAClC,IAAI,CAAC/D,KAAK,EAAE;YACR7C,YAAY,CAAC,iBAAiB4G,MAAM,kCAAkC,EAAE,MAAM,CAAC;UACnF,CAAC,MACI,IAAI,OAAO/D,KAAK,CAACgE,MAAM,KAAK,UAAU,EAAE;YACzC7G,YAAY,CAAC,iBAAiB4G,MAAM,gEAAgE,EAAE,MAAM,CAAC;UACjH,CAAC,MACI;YACD/D,KAAK,CAACgE,MAAM,CAAC,CAAC;YACd7G,YAAY,CAAC,UAAU4G,MAAM,UAAU,CAAC;UAC5C;QACJ;MACJ,CAAC;IAET,CAAC,CAAC;IACFb,GAAG,CAACe,EAAE,CAACC,gBAAgB,CAAC,CAACC,OAAO,EAAEC,GAAG,KAAK;MACtC,MAAMC,KAAK,GAAIF,OAAO,CAACG,iBAAiB,IACpCH,OAAO,CAACG,iBAAiB,CAACD,KAAM;MACpC,IAAIA,KAAK,IAAIA,KAAK,CAACE,QAAQ,EAAE;QACzB,MAAMC,WAAW,GAAGL,OAAO,CAACG,iBAAiB,CAACD,KAAK,CAACE,QAAQ;QAC5DtM,MAAM,CAACwM,MAAM,CAACD,WAAW,CAAC,CAACE,OAAO,CAAE1E,KAAK,IAAK;UAC1CmE,OAAO,CAACQ,YAAY,CAACxG,KAAK,CAAC2D,IAAI,CAAC;YAC5B5I,IAAI,EAAE0J,YAAY,CAAC5C,KAAK,CAACG,GAAG,CAAC;YAC7BX,GAAG,EAAE,OAAO;YACZqB,QAAQ,EAAE,IAAI;YACdzC,KAAK,EAAE4B,KAAK,CAAC4E,aAAa,GACpB;cACEhF,OAAO,EAAE;gBACLxB,KAAK,EAAElI,KAAK,CAAC8J,KAAK,CAACmB,MAAM,CAAC;gBAC1BsC,OAAO,EAAE,CACL;kBACIF,IAAI,EAAE,SAAS;kBACfI,OAAO,EAAE,+BAA+B;kBACxCD,MAAM,EAAEA,CAAA,KAAM1D,KAAK,CAACgE,MAAM,CAAC;gBAC/B,CAAC;cAET;YACJ,CAAC;YACC;YACE/L,MAAM,CAACwI,IAAI,CAACT,KAAK,CAACmB,MAAM,CAAC,CAACD,MAAM,CAAC,CAAC/C,KAAK,EAAEqB,GAAG,KAAK;cAC7CrB,KAAK,CAACqB,GAAG,CAAC,GAAGQ,KAAK,CAACmB,MAAM,CAAC3B,GAAG,CAAC;cAC9B,OAAOrB,KAAK;YAChB,CAAC,EAAE,CAAC,CAAC;UACjB,CAAC,CAAC;UACF,IAAI6B,KAAK,CAACiB,QAAQ,IAAIjB,KAAK,CAACiB,QAAQ,CAACG,MAAM,EAAE;YACzC+C,OAAO,CAACQ,YAAY,CAACxG,KAAK,CAAC2D,IAAI,CAAC;cAC5B5I,IAAI,EAAE0J,YAAY,CAAC5C,KAAK,CAACG,GAAG,CAAC;cAC7BX,GAAG,EAAE,SAAS;cACdqB,QAAQ,EAAE,KAAK;cACfzC,KAAK,EAAE4B,KAAK,CAACiB,QAAQ,CAACC,MAAM,CAAC,CAACJ,OAAO,EAAEtB,GAAG,KAAK;gBAC3C,IAAI;kBACAsB,OAAO,CAACtB,GAAG,CAAC,GAAGQ,KAAK,CAACR,GAAG,CAAC;gBAC7B,CAAC,CACD,OAAOrF,KAAK,EAAE;kBACV;kBACA2G,OAAO,CAACtB,GAAG,CAAC,GAAGrF,KAAK;gBACxB;gBACA,OAAO2G,OAAO;cAClB,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACFoC,GAAG,CAACe,EAAE,CAACY,gBAAgB,CAAEV,OAAO,IAAK;MACjC,IAAIA,OAAO,CAACrB,GAAG,KAAKA,GAAG,IAAIqB,OAAO,CAACW,WAAW,KAAKpC,YAAY,EAAE;QAC7D,IAAIqC,MAAM,GAAG,CAACvN,KAAK,CAAC;QACpBuN,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC1E,KAAK,CAACC,IAAI,CAAC/I,KAAK,CAACgJ,EAAE,CAACiE,MAAM,CAAC,CAAC,CAAC,CAAC;QACrDN,OAAO,CAACc,SAAS,GAAG,CAACd,OAAO,CAACpD,MAAM,GAC7BgE,MAAM,CAAChE,MAAM,CAAEf,KAAK,IAAK,KAAK,IAAIA,KAAK,GACnCA,KAAK,CAACG,GAAG,CACNvC,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACsG,OAAO,CAACpD,MAAM,CAACnD,WAAW,CAAC,CAAC,CAAC,GACzCiC,gBAAgB,CAACjC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACsG,OAAO,CAACpD,MAAM,CAACnD,WAAW,CAAC,CAAC,CAAC,CAAC,GAC1EmH,MAAM,EAAEpE,GAAG,CAACZ,2BAA2B,CAAC;MAClD;IACJ,CAAC,CAAC;IACF;IACAnH,UAAU,CAACsM,MAAM,GAAG1N,KAAK;IACzB0L,GAAG,CAACe,EAAE,CAACkB,iBAAiB,CAAEhB,OAAO,IAAK;MAClC,IAAIA,OAAO,CAACrB,GAAG,KAAKA,GAAG,IAAIqB,OAAO,CAACW,WAAW,KAAKpC,YAAY,EAAE;QAC7D,MAAM0C,cAAc,GAAGjB,OAAO,CAACJ,MAAM,KAAKjE,aAAa,GACjDtI,KAAK,GACLA,KAAK,CAACgJ,EAAE,CAACQ,GAAG,CAACmD,OAAO,CAACJ,MAAM,CAAC;QAClC,IAAI,CAACqB,cAAc,EAAE;UACjB;UACA;UACA;QACJ;QACA,IAAIA,cAAc,EAAE;UAChB;UACA,IAAIjB,OAAO,CAACJ,MAAM,KAAKjE,aAAa,EAChClH,UAAU,CAACyM,MAAM,GAAGnP,KAAK,CAACkP,cAAc,CAAC;UAC7CjB,OAAO,CAAChG,KAAK,GAAGiC,4BAA4B,CAACgF,cAAc,CAAC;QAChE;MACJ;IACJ,CAAC,CAAC;IACFlC,GAAG,CAACe,EAAE,CAACqB,kBAAkB,CAAC,CAACnB,OAAO,EAAEC,GAAG,KAAK;MACxC,IAAID,OAAO,CAACrB,GAAG,KAAKA,GAAG,IAAIqB,OAAO,CAACW,WAAW,KAAKpC,YAAY,EAAE;QAC7D,MAAM0C,cAAc,GAAGjB,OAAO,CAACJ,MAAM,KAAKjE,aAAa,GACjDtI,KAAK,GACLA,KAAK,CAACgJ,EAAE,CAACQ,GAAG,CAACmD,OAAO,CAACJ,MAAM,CAAC;QAClC,IAAI,CAACqB,cAAc,EAAE;UACjB,OAAOjI,YAAY,CAAC,UAAUgH,OAAO,CAACJ,MAAM,aAAa,EAAE,OAAO,CAAC;QACvE;QACA,MAAM;UAAEwB;QAAK,CAAC,GAAGpB,OAAO;QACxB,IAAI,CAAC1G,OAAO,CAAC2H,cAAc,CAAC,EAAE;UAC1B;UACA,IAAIG,IAAI,CAACnE,MAAM,KAAK,CAAC,IACjB,CAACgE,cAAc,CAAC9D,iBAAiB,CAACkE,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,IAC9CA,IAAI,CAAC,CAAC,CAAC,IAAIH,cAAc,CAACjE,MAAM,EAAE;YAClCoE,IAAI,CAACE,OAAO,CAAC,QAAQ,CAAC;UAC1B;QACJ,CAAC,MACI;UACD;UACAF,IAAI,CAACE,OAAO,CAAC,OAAO,CAAC;QACzB;QACAlD,gBAAgB,GAAG,KAAK;QACxB4B,OAAO,CAACxN,GAAG,CAACyO,cAAc,EAAEG,IAAI,EAAEpB,OAAO,CAAChG,KAAK,CAACC,KAAK,CAAC;QACtDmE,gBAAgB,GAAG,IAAI;MAC3B;IACJ,CAAC,CAAC;IACFW,GAAG,CAACe,EAAE,CAACyB,kBAAkB,CAAEvB,OAAO,IAAK;MACnC,IAAIA,OAAO,CAACjL,IAAI,CAACyM,UAAU,CAAC,IAAI,CAAC,EAAE;QAC/B,MAAM/E,OAAO,GAAGuD,OAAO,CAACjL,IAAI,CAAC8D,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QAClD,MAAMgD,KAAK,GAAGxI,KAAK,CAACgJ,EAAE,CAACQ,GAAG,CAACJ,OAAO,CAAC;QACnC,IAAI,CAACZ,KAAK,EAAE;UACR,OAAO7C,YAAY,CAAC,UAAUyD,OAAO,aAAa,EAAE,OAAO,CAAC;QAChE;QACA,MAAM;UAAE2E;QAAK,CAAC,GAAGpB,OAAO;QACxB,IAAIoB,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UACrB,OAAOpI,YAAY,CAAC,2BAA2ByD,OAAO,OAAO2E,IAAI,+BAA+B,CAAC;QACrG;QACA;QACA;QACAA,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ;QAClBhD,gBAAgB,GAAG,KAAK;QACxB4B,OAAO,CAACxN,GAAG,CAACqJ,KAAK,EAAEuF,IAAI,EAAEpB,OAAO,CAAChG,KAAK,CAACC,KAAK,CAAC;QAC7CmE,gBAAgB,GAAG,IAAI;MAC3B;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASqD,kBAAkBA,CAAC9C,GAAG,EAAE9C,KAAK,EAAE;EACpC,IAAI,CAACwC,mBAAmB,CAAC3E,QAAQ,CAAC+E,YAAY,CAAC5C,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE;IACxDqC,mBAAmB,CAACV,IAAI,CAACc,YAAY,CAAC5C,KAAK,CAACG,GAAG,CAAC,CAAC;EACrD;EACA9I,mBAAmB,CAAC;IAChB4I,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,UAAU;IACjB6C,IAAI,EAAE,kCAAkC;IACxCC,WAAW,EAAE,OAAO;IACpBC,QAAQ,EAAE,yBAAyB;IACnCT,mBAAmB;IACnBM,GAAG;IACH+C,QAAQ,EAAE;MACNC,eAAe,EAAE;QACb5F,KAAK,EAAE,iCAAiC;QACxChH,IAAI,EAAE,SAAS;QACf6M,YAAY,EAAE;MAClB;MACA;MACA;MACA;MACA;MACA;IACJ;EACJ,CAAC,EAAG7C,GAAG,IAAK;IACR;IACA,MAAMC,GAAG,GAAG,OAAOD,GAAG,CAACC,GAAG,KAAK,UAAU,GAAGD,GAAG,CAACC,GAAG,CAAC6C,IAAI,CAAC9C,GAAG,CAAC,GAAG+C,IAAI,CAAC9C,GAAG;IACxEnD,KAAK,CAACkG,SAAS,CAAC,CAAC;MAAEC,KAAK;MAAEC,OAAO;MAAE5M,IAAI;MAAE6M;IAAK,CAAC,KAAK;MAChD,MAAMC,OAAO,GAAGC,eAAe,EAAE;MACjCrD,GAAG,CAACsD,gBAAgB,CAAC;QACjBC,OAAO,EAAEhE,kBAAkB;QAC3BZ,KAAK,EAAE;UACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;UACX9G,KAAK,EAAE,KAAK,GAAG7C,IAAI;UACnBmN,QAAQ,EAAE,OAAO;UACjB/E,IAAI,EAAE;YACF5B,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG,CAAC;YAC/BuD,MAAM,EAAEhE,aAAa,CAAClG,IAAI,CAAC;YAC3B6M;UACJ,CAAC;UACDC;QACJ;MACJ,CAAC,CAAC;MACFH,KAAK,CAAErJ,MAAM,IAAK;QACd8J,YAAY,GAAGC,SAAS;QACxB3D,GAAG,CAACsD,gBAAgB,CAAC;UACjBC,OAAO,EAAEhE,kBAAkB;UAC3BZ,KAAK,EAAE;YACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;YACX9G,KAAK,EAAE,KAAK,GAAG7C,IAAI;YACnBmN,QAAQ,EAAE,KAAK;YACf/E,IAAI,EAAE;cACF5B,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG,CAAC;cAC/BuD,MAAM,EAAEhE,aAAa,CAAClG,IAAI,CAAC;cAC3B6M,IAAI;cACJvJ;YACJ,CAAC;YACDwJ;UACJ;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;MACFF,OAAO,CAAEjM,KAAK,IAAK;QACfyM,YAAY,GAAGC,SAAS;QACxB3D,GAAG,CAACsD,gBAAgB,CAAC;UACjBC,OAAO,EAAEhE,kBAAkB;UAC3BZ,KAAK,EAAE;YACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;YACX2D,OAAO,EAAE,OAAO;YAChBzK,KAAK,EAAE,KAAK,GAAG7C,IAAI;YACnBmN,QAAQ,EAAE,KAAK;YACf/E,IAAI,EAAE;cACF5B,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG,CAAC;cAC/BuD,MAAM,EAAEhE,aAAa,CAAClG,IAAI,CAAC;cAC3B6M,IAAI;cACJlM;YACJ,CAAC;YACDmM;UACJ;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,EAAE,IAAI,CAAC;IACRtG,KAAK,CAACsB,iBAAiB,CAACoD,OAAO,CAAElL,IAAI,IAAK;MACtCrD,KAAK,CAAC,MAAMC,KAAK,CAAC4J,KAAK,CAACxG,IAAI,CAAC,CAAC,EAAE,CAACyI,QAAQ,EAAED,QAAQ,KAAK;QACpDkB,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;QAC3B7D,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;QACpC,IAAIH,gBAAgB,EAAE;UAClBW,GAAG,CAACsD,gBAAgB,CAAC;YACjBC,OAAO,EAAEhE,kBAAkB;YAC3BZ,KAAK,EAAE;cACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;cACX9G,KAAK,EAAE,QAAQ;cACfsK,QAAQ,EAAEnN,IAAI;cACdoI,IAAI,EAAE;gBACFK,QAAQ;gBACRD;cACJ,CAAC;cACDsE,OAAO,EAAEM;YACb;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,EAAE;QAAEI,IAAI,EAAE;MAAK,CAAC,CAAC;IACtB,CAAC,CAAC;IACFhH,KAAK,CAACiH,UAAU,CAAC,CAAC;MAAEvF,MAAM;MAAExI;IAAK,CAAC,EAAEiF,KAAK,KAAK;MAC1C+E,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;MAC3B7D,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;MACpC,IAAI,CAACH,gBAAgB,EACjB;MACJ;MACA,MAAM2E,SAAS,GAAG;QACdR,IAAI,EAAEvD,GAAG,CAAC,CAAC;QACX9G,KAAK,EAAE8F,kBAAkB,CAACjJ,IAAI,CAAC;QAC/B0I,IAAI,EAAEe,QAAQ,CAAC;UAAE3C,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG;QAAE,CAAC,EAAEsB,eAAe,CAACC,MAAM,CAAC,CAAC;QAC5E4E,OAAO,EAAEM;MACb,CAAC;MACD,IAAI1N,IAAI,KAAKZ,YAAY,CAAC+J,aAAa,EAAE;QACrC6E,SAAS,CAACP,QAAQ,GAAG,IAAI;MAC7B,CAAC,MACI,IAAIzN,IAAI,KAAKZ,YAAY,CAACgK,WAAW,EAAE;QACxC4E,SAAS,CAACP,QAAQ,GAAG,IAAI;MAC7B,CAAC,MACI,IAAIjF,MAAM,IAAI,CAACpB,KAAK,CAACqB,OAAO,CAACD,MAAM,CAAC,EAAE;QACvCwF,SAAS,CAACP,QAAQ,GAAGjF,MAAM,CAACxI,IAAI;MACpC;MACA,IAAIwI,MAAM,EAAE;QACRwF,SAAS,CAACtF,IAAI,CAAC,aAAa,CAAC,GAAG;UAC5BhC,OAAO,EAAE;YACLD,OAAO,EAAE,eAAe;YACxBzG,IAAI,EAAE,QAAQ;YACdyK,OAAO,EAAE,qBAAqB;YAC9BvF,KAAK,EAAEsD;UACX;QACJ,CAAC;MACL;MACAwB,GAAG,CAACsD,gBAAgB,CAAC;QACjBC,OAAO,EAAEhE,kBAAkB;QAC3BZ,KAAK,EAAEqF;MACX,CAAC,CAAC;IACN,CAAC,EAAE;MAAEC,QAAQ,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAO,CAAC,CAAC;IACrC,MAAMC,SAAS,GAAGrH,KAAK,CAACsH,UAAU;IAClCtH,KAAK,CAACsH,UAAU,GAAGjR,OAAO,CAAEkR,QAAQ,IAAK;MACrCF,SAAS,CAACE,QAAQ,CAAC;MACnBrE,GAAG,CAACsD,gBAAgB,CAAC;QACjBC,OAAO,EAAEhE,kBAAkB;QAC3BZ,KAAK,EAAE;UACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;UACX9G,KAAK,EAAE,KAAK,GAAG2D,KAAK,CAACG,GAAG;UACxBwG,QAAQ,EAAE,YAAY;UACtB/E,IAAI,EAAE;YACF5B,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG,CAAC;YAC/BqH,IAAI,EAAE9H,aAAa,CAAC,YAAY;UACpC;QACJ;MACJ,CAAC,CAAC;MACF;MACAwD,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;MAC3B7D,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;MACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;IACxC,CAAC,CAAC;IACF,MAAM;MAAE+E;IAAS,CAAC,GAAGzH,KAAK;IAC1BA,KAAK,CAACyH,QAAQ,GAAG,MAAM;MACnBA,QAAQ,CAAC,CAAC;MACVvE,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;MAC3B7D,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;MACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;MACpCQ,GAAG,CAACwE,WAAW,CAAC,CAAC,CAAC5B,eAAe,IAC7B3I,YAAY,CAAC,aAAa6C,KAAK,CAACG,GAAG,YAAY,CAAC;IACxD,CAAC;IACD;IACA+C,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;IAC3B7D,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;IACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;IACpCQ,GAAG,CAACwE,WAAW,CAAC,CAAC,CAAC5B,eAAe,IAC7B3I,YAAY,CAAC,IAAI6C,KAAK,CAACG,GAAG,sBAAsB,CAAC;EACzD,CAAC,CAAC;AACN;AACA,IAAIoG,eAAe,GAAG,CAAC;AACvB,IAAIK,YAAY;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,sBAAsBA,CAAC3H,KAAK,EAAE4H,WAAW,EAAEC,aAAa,EAAE;EAC/D;EACA,MAAMpE,OAAO,GAAGmE,WAAW,CAAC1G,MAAM,CAAC,CAAC4G,YAAY,EAAEC,UAAU,KAAK;IAC7D;IACAD,YAAY,CAACC,UAAU,CAAC,GAAG7R,KAAK,CAAC8J,KAAK,CAAC,CAAC+H,UAAU,CAAC;IACnD,OAAOD,YAAY;EACvB,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,KAAK,MAAMC,UAAU,IAAItE,OAAO,EAAE;IAC9BzD,KAAK,CAAC+H,UAAU,CAAC,GAAG,YAAY;MAC5B;MACA,MAAMC,SAAS,GAAGzB,eAAe;MACjC,MAAM0B,YAAY,GAAGJ,aAAa,GAC5B,IAAIK,KAAK,CAAClI,KAAK,EAAE;QACfgB,GAAGA,CAAC,GAAGqF,IAAI,EAAE;UACTO,YAAY,GAAGoB,SAAS;UACxB,OAAOG,OAAO,CAACnH,GAAG,CAAC,GAAGqF,IAAI,CAAC;QAC/B,CAAC;QACD1P,GAAGA,CAAC,GAAG0P,IAAI,EAAE;UACTO,YAAY,GAAGoB,SAAS;UACxB,OAAOG,OAAO,CAACxR,GAAG,CAAC,GAAG0P,IAAI,CAAC;QAC/B;MACJ,CAAC,CAAC,GACArG,KAAK;MACX;MACA4G,YAAY,GAAGoB,SAAS;MACxB,MAAMI,QAAQ,GAAG3E,OAAO,CAACsE,UAAU,CAAC,CAACM,KAAK,CAACJ,YAAY,EAAEK,SAAS,CAAC;MACnE;MACA1B,YAAY,GAAGC,SAAS;MACxB,OAAOuB,QAAQ;IACnB,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA,SAASG,cAAcA,CAAC;EAAEzF,GAAG;EAAE9C,KAAK;EAAEwI;AAAQ,CAAC,EAAE;EAC7C;EACA,IAAIxI,KAAK,CAACG,GAAG,CAACwF,UAAU,CAAC,QAAQ,CAAC,EAAE;IAChC;EACJ;EACA;EACA3F,KAAK,CAAC4E,aAAa,GAAG,CAAC,CAAC4D,OAAO,CAACrK,KAAK;EACrC;EACA,IAAI,CAAC6B,KAAK,CAACyI,EAAE,CAACC,QAAQ,EAAE;IACpBf,sBAAsB,CAAC3H,KAAK,EAAE/H,MAAM,CAACwI,IAAI,CAAC+H,OAAO,CAAC/E,OAAO,CAAC,EAAEzD,KAAK,CAAC4E,aAAa,CAAC;IAChF;IACA,MAAM+D,iBAAiB,GAAG3I,KAAK,CAACsH,UAAU;IAC1CpR,KAAK,CAAC8J,KAAK,CAAC,CAACsH,UAAU,GAAG,UAAUC,QAAQ,EAAE;MAC1CoB,iBAAiB,CAACN,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACxCX,sBAAsB,CAAC3H,KAAK,EAAE/H,MAAM,CAACwI,IAAI,CAAC8G,QAAQ,CAACqB,WAAW,CAACnF,OAAO,CAAC,EAAE,CAAC,CAACzD,KAAK,CAAC4E,aAAa,CAAC;IACnG,CAAC;EACL;EACAgB,kBAAkB,CAAC9C,GAAG;EACtB;EACA9C,KAAK,CAAC;AACV;;AAEA;AACA;AACA;AACA,SAAS6I,WAAWA,CAAA,EAAG;EACnB,MAAMC,KAAK,GAAGxS,WAAW,CAAC,IAAI,CAAC;EAC/B;EACA;EACA,MAAM6H,KAAK,GAAG2K,KAAK,CAACC,GAAG,CAAC,MAAMxS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC,IAAIkS,EAAE,GAAG,EAAE;EACX;EACA,IAAIO,aAAa,GAAG,EAAE;EACtB,MAAMxR,KAAK,GAAGnB,OAAO,CAAC;IAClB4S,OAAOA,CAACnG,GAAG,EAAE;MACT;MACA;MACAvL,cAAc,CAACC,KAAK,CAAC;MACrB,IAAI,CAAChB,MAAM,EAAE;QACTgB,KAAK,CAAC0R,EAAE,GAAGpG,GAAG;QACdA,GAAG,CAACqG,OAAO,CAACzR,WAAW,EAAEF,KAAK,CAAC;QAC/BsL,GAAG,CAACsG,MAAM,CAACC,gBAAgB,CAACnE,MAAM,GAAG1N,KAAK;QAC1C;QACA,IAAK,CAAEG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOyR,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAE3R,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAS,EAAE;UAC3KsK,qBAAqB,CAACC,GAAG,EAAEtL,KAAK,CAAC;QACrC;QACAwR,aAAa,CAACtE,OAAO,CAAE6E,MAAM,IAAKd,EAAE,CAAC3G,IAAI,CAACyH,MAAM,CAAC,CAAC;QAClDP,aAAa,GAAG,EAAE;MACtB;IACJ,CAAC;IACDQ,GAAGA,CAACD,MAAM,EAAE;MACR,IAAI,CAAC,IAAI,CAACL,EAAE,IAAI,CAAC1S,MAAM,EAAE;QACrBwS,aAAa,CAAClH,IAAI,CAACyH,MAAM,CAAC;MAC9B,CAAC,MACI;QACDd,EAAE,CAAC3G,IAAI,CAACyH,MAAM,CAAC;MACnB;MACA,OAAO,IAAI;IACf,CAAC;IACDd,EAAE;IACF;IACA;IACAS,EAAE,EAAE,IAAI;IACRO,EAAE,EAAEX,KAAK;IACTtI,EAAE,EAAE,IAAIkJ,GAAG,CAAC,CAAC;IACbvL;EACJ,CAAC,CAAC;EACF;EACA;EACA,IAAK,CAAExG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOyR,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAE3R,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAS,IAAI,OAAO2P,KAAK,KAAK,WAAW,EAAE;IAC3M1Q,KAAK,CAACgS,GAAG,CAACjB,cAAc,CAAC;EAC7B;EACA,OAAO/Q,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmS,YAAYA,CAACnS,KAAK,EAAE;EACzBA,KAAK,CAACiS,EAAE,CAACG,IAAI,CAAC,CAAC;EACfpS,KAAK,CAACgJ,EAAE,CAACqJ,KAAK,CAAC,CAAC;EAChBrS,KAAK,CAACiR,EAAE,CAACqB,MAAM,CAAC,CAAC,CAAC;EAClBtS,KAAK,CAAC2G,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC;EACtB;EACA5G,KAAK,CAAC0R,EAAE,GAAG,IAAI;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,UAAU,GAAIC,EAAE,IAAK;EACvB,OAAO,OAAOA,EAAE,KAAK,UAAU,IAAI,OAAOA,EAAE,CAAC7J,GAAG,KAAK,QAAQ;AACjE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmC,WAAWA,CAAC2H,QAAQ,EAAEC,QAAQ,EAAE;EACrC;EACA,KAAK,MAAM1K,GAAG,IAAI0K,QAAQ,EAAE;IACxB,MAAMC,QAAQ,GAAGD,QAAQ,CAAC1K,GAAG,CAAC;IAC9B;IACA,IAAI,EAAEA,GAAG,IAAIyK,QAAQ,CAAC,EAAE;MACpB;IACJ;IACA,MAAMG,WAAW,GAAGH,QAAQ,CAACzK,GAAG,CAAC;IACjC,IAAIzH,aAAa,CAACqS,WAAW,CAAC,IAC1BrS,aAAa,CAACoS,QAAQ,CAAC,IACvB,CAAC1T,KAAK,CAAC0T,QAAQ,CAAC,IAChB,CAACzT,UAAU,CAACyT,QAAQ,CAAC,EAAE;MACvBF,QAAQ,CAACzK,GAAG,CAAC,GAAG8C,WAAW,CAAC8H,WAAW,EAAED,QAAQ,CAAC;IACtD,CAAC,MACI;MACD;MACA;MACA,IAAI3T,MAAM,EAAE;QACRG,GAAG,CAACsT,QAAQ,EAAEzK,GAAG,EAAE2K,QAAQ,CAAC;MAChC,CAAC,MACI;QACDF,QAAQ,CAACzK,GAAG,CAAC,GAAG2K,QAAQ;MAC5B;IACJ;EACJ;EACA,OAAOF,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAACC,eAAe,EAAEC,GAAG,EAAE;EAC3C;EACA,IAAI,EAAE5S,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC,EAAE;IAC1C,OAAO,MAAM,CAAE,CAAC;EACpB;EACA,OAAQ2S,SAAS,IAAK;IAClB,MAAMhT,KAAK,GAAG+S,GAAG,CAAC3I,IAAI,CAACpK,KAAK,IAAI8S,eAAe,CAACG,MAAM;IACtD,IAAI,CAACjT,KAAK,EAAE;MACR;MACA;IACJ;IACA;IACA+S,GAAG,CAAC3I,IAAI,CAACpK,KAAK,GAAGA,KAAK;IACtB;IACA,KAAK,MAAMkT,UAAU,IAAIF,SAAS,EAAE;MAChC,MAAMG,QAAQ,GAAGH,SAAS,CAACE,UAAU,CAAC;MACtC;MACA,IAAIX,UAAU,CAACY,QAAQ,CAAC,IAAInT,KAAK,CAACgJ,EAAE,CAACgF,GAAG,CAACmF,QAAQ,CAACxK,GAAG,CAAC,EAAE;QACpD;QACA,MAAMF,EAAE,GAAG0K,QAAQ,CAACxK,GAAG;QACvB,IAAIF,EAAE,KAAKqK,eAAe,CAACnK,GAAG,EAAE;UAC5BjG,OAAO,CAACqD,IAAI,CAAC,qCAAqC+M,eAAe,CAACnK,GAAG,SAASF,EAAE,eAAe,CAAC;UAChG;UACA,OAAOsK,GAAG,CAACK,UAAU,CAAC,CAAC;QAC3B;QACA,MAAMC,aAAa,GAAGrT,KAAK,CAACgJ,EAAE,CAACQ,GAAG,CAACf,EAAE,CAAC;QACtC,IAAI,CAAC4K,aAAa,EAAE;UAChB3Q,OAAO,CAACsD,GAAG,CAAC,uDAAuD,CAAC;UACpE;QACJ;QACAmN,QAAQ,CAACnT,KAAK,EAAEqT,aAAa,CAAC;MAClC;IACJ;EACJ,CAAC;AACL;AAEA,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAE,CAAC;AACtB,SAASC,eAAeA,CAACC,aAAa,EAAEC,QAAQ,EAAE9D,QAAQ,EAAE+D,SAAS,GAAGJ,IAAI,EAAE;EAC1EE,aAAa,CAAClJ,IAAI,CAACmJ,QAAQ,CAAC;EAC5B,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,GAAG,GAAGJ,aAAa,CAACK,OAAO,CAACJ,QAAQ,CAAC;IAC3C,IAAIG,GAAG,GAAG,CAAC,CAAC,EAAE;MACVJ,aAAa,CAAClB,MAAM,CAACsB,GAAG,EAAE,CAAC,CAAC;MAC5BF,SAAS,CAAC,CAAC;IACf;EACJ,CAAC;EACD,IAAI,CAAC/D,QAAQ,IAAIvQ,eAAe,CAAC,CAAC,EAAE;IAChCC,cAAc,CAACsU,kBAAkB,CAAC;EACtC;EACA,OAAOA,kBAAkB;AAC7B;AACA,SAASG,oBAAoBA,CAACN,aAAa,EAAE,GAAG3E,IAAI,EAAE;EAClD2E,aAAa,CAACO,KAAK,CAAC,CAAC,CAAC7G,OAAO,CAAEuG,QAAQ,IAAK;IACxCA,QAAQ,CAAC,GAAG5E,IAAI,CAAC;EACrB,CAAC,CAAC;AACN;AAEA,MAAMmF,sBAAsB,GAAIxB,EAAE,IAAKA,EAAE,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA,MAAMyB,aAAa,GAAG3T,MAAM,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA,MAAM4T,WAAW,GAAG5T,MAAM,CAAC,CAAC;AAC5B,SAAS6T,oBAAoBA,CAAC7P,MAAM,EAAE8P,YAAY,EAAE;EAChD;EACA,IAAI9P,MAAM,YAAY4N,GAAG,IAAIkC,YAAY,YAAYlC,GAAG,EAAE;IACtDkC,YAAY,CAAClH,OAAO,CAAC,CAACtG,KAAK,EAAEoB,GAAG,KAAK1D,MAAM,CAACnF,GAAG,CAAC6I,GAAG,EAAEpB,KAAK,CAAC,CAAC;EAChE,CAAC,MACI,IAAItC,MAAM,YAAY+P,GAAG,IAAID,YAAY,YAAYC,GAAG,EAAE;IAC3D;IACAD,YAAY,CAAClH,OAAO,CAAC5I,MAAM,CAACgQ,GAAG,EAAEhQ,MAAM,CAAC;EAC5C;EACA;EACA,KAAK,MAAM0D,GAAG,IAAIoM,YAAY,EAAE;IAC5B,IAAI,CAACA,YAAY,CAACG,cAAc,CAACvM,GAAG,CAAC,EACjC;IACJ,MAAM2K,QAAQ,GAAGyB,YAAY,CAACpM,GAAG,CAAC;IAClC,MAAM4K,WAAW,GAAGtO,MAAM,CAAC0D,GAAG,CAAC;IAC/B,IAAIzH,aAAa,CAACqS,WAAW,CAAC,IAC1BrS,aAAa,CAACoS,QAAQ,CAAC,IACvBrO,MAAM,CAACiQ,cAAc,CAACvM,GAAG,CAAC,IAC1B,CAAC/I,KAAK,CAAC0T,QAAQ,CAAC,IAChB,CAACzT,UAAU,CAACyT,QAAQ,CAAC,EAAE;MACvB;MACA;MACA;MACArO,MAAM,CAAC0D,GAAG,CAAC,GAAGmM,oBAAoB,CAACvB,WAAW,EAAED,QAAQ,CAAC;IAC7D,CAAC,MACI;MACD;MACArO,MAAM,CAAC0D,GAAG,CAAC,GAAG2K,QAAQ;IAC1B;EACJ;EACA,OAAOrO,MAAM;AACjB;AACA,MAAMkQ,iBAAiB,GAAIrU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAC1DC,MAAM,CAAC,qBAAqB,CAAC,GAC7B,0BAA2BA,MAAM,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmU,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOjU,MAAM,CAACkU,cAAc,CAACD,GAAG,EAAEF,iBAAiB,EAAE,CAAC,CAAC,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,aAAaA,CAACF,GAAG,EAAE;EACxB,OAAO,CAACnU,aAAa,CAACmU,GAAG,CAAC,IAAI,CAACA,GAAG,CAACH,cAAc,CAACC,iBAAiB,CAAC;AACxE;AACA,MAAM;EAAE/O;AAAO,CAAC,GAAGhF,MAAM;AACzB,SAASoU,UAAUA,CAACrU,CAAC,EAAE;EACnB,OAAO,CAAC,EAAEvB,KAAK,CAACuB,CAAC,CAAC,IAAIA,CAAC,CAACsU,MAAM,CAAC;AACnC;AACA,SAASC,kBAAkBA,CAACtM,EAAE,EAAEuI,OAAO,EAAEhR,KAAK,EAAE+S,GAAG,EAAE;EACjD,MAAM;IAAEpM,KAAK;IAAEsF,OAAO;IAAE3C;EAAQ,CAAC,GAAG0H,OAAO;EAC3C,MAAMgE,YAAY,GAAGhV,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC6B,EAAE,CAAC;EAC1C,IAAID,KAAK;EACT,SAASyM,KAAKA,CAAA,EAAG;IACb,IAAI,CAACD,YAAY,KAAK,EAAE7U,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC,IAAI,CAAC0S,GAAG,CAAC,EAAE;MACrE;MACA,IAAI/T,MAAM,EAAE;QACRG,GAAG,CAACa,KAAK,CAAC2G,KAAK,CAACC,KAAK,EAAE6B,EAAE,EAAE9B,KAAK,GAAGA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;MACpD,CAAC,MACI;QACD3G,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC6B,EAAE,CAAC,GAAG9B,KAAK,GAAGA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;MAChD;IACJ;IACA;IACA,MAAMuO,UAAU,GAAI/U,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK0S,GAAG;IAC3D;IACEnT,MAAM,CAACb,GAAG,CAAC4H,KAAK,GAAGA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GACzChH,MAAM,CAACI,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC6B,EAAE,CAAC,CAAC;IACnC,OAAOhD,MAAM,CAACyP,UAAU,EAAEjJ,OAAO,EAAExL,MAAM,CAACwI,IAAI,CAACK,OAAO,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,CAACyL,eAAe,EAAEnT,IAAI,KAAK;MAC5F,IAAK7B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK2B,IAAI,IAAIkT,UAAU,EAAE;QAC/DxS,OAAO,CAACqD,IAAI,CAAC,uGAAuG/D,IAAI,eAAeyG,EAAE,IAAI,CAAC;MAClJ;MACA0M,eAAe,CAACnT,IAAI,CAAC,GAAGnD,OAAO,CAACc,QAAQ,CAAC,MAAM;QAC3CI,cAAc,CAACC,KAAK,CAAC;QACrB;QACA,MAAMwI,KAAK,GAAGxI,KAAK,CAACgJ,EAAE,CAACQ,GAAG,CAACf,EAAE,CAAC;QAC9B;QACA;QACA,IAAIzJ,MAAM,IAAI,CAACwJ,KAAK,CAAC4M,EAAE,EACnB;QACJ;QACA;QACA;QACA,OAAO9L,OAAO,CAACtH,IAAI,CAAC,CAACpB,IAAI,CAAC4H,KAAK,EAAEA,KAAK,CAAC;MAC3C,CAAC,CAAC,CAAC;MACH,OAAO2M,eAAe;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX;EACA3M,KAAK,GAAG6M,gBAAgB,CAAC5M,EAAE,EAAEwM,KAAK,EAAEjE,OAAO,EAAEhR,KAAK,EAAE+S,GAAG,EAAE,IAAI,CAAC;EAC9D,OAAOvK,KAAK;AAChB;AACA,SAAS6M,gBAAgBA,CAAC1M,GAAG,EAAEsM,KAAK,EAAEjE,OAAO,GAAG,CAAC,CAAC,EAAEhR,KAAK,EAAE+S,GAAG,EAAEuC,cAAc,EAAE;EAC5E,IAAIhE,KAAK;EACT,MAAMiE,gBAAgB,GAAG9P,MAAM,CAAC;IAAEwG,OAAO,EAAE,CAAC;EAAE,CAAC,EAAE+E,OAAO,CAAC;EACzD;EACA,IAAK7Q,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACL,KAAK,CAACiS,EAAE,CAACuD,MAAM,EAAE;IAC7D,MAAM,IAAIjQ,KAAK,CAAC,iBAAiB,CAAC;EACtC;EACA;EACA,MAAMkQ,iBAAiB,GAAG;IAAEjG,IAAI,EAAE;EAAK,CAAC;EACxC;EACA,IAAKrP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACrB,MAAM,EAAE;IACpDyW,iBAAiB,CAACC,SAAS,GAAIrL,KAAK,IAAK;MACrC;MACA,IAAIsL,WAAW,EAAE;QACbC,cAAc,GAAGvL,KAAK;QACtB;MACJ,CAAC,MACI,IAAIsL,WAAW,IAAI,KAAK,IAAI,CAACnN,KAAK,CAACqN,YAAY,EAAE;QAClD;QACA;QACA,IAAI/M,KAAK,CAACqB,OAAO,CAACyL,cAAc,CAAC,EAAE;UAC/BA,cAAc,CAACtL,IAAI,CAACD,KAAK,CAAC;QAC9B,CAAC,MACI;UACD3H,OAAO,CAACC,KAAK,CAAC,kFAAkF,CAAC;QACrG;MACJ;IACJ,CAAC;EACL;EACA;EACA,IAAIgT,WAAW,CAAC,CAAC;EACjB,IAAIG,eAAe,CAAC,CAAC;EACrB,IAAItC,aAAa,GAAG,EAAE;EACtB,IAAIuC,mBAAmB,GAAG,EAAE;EAC5B,IAAIH,cAAc;EAClB,MAAMZ,YAAY,GAAGhV,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC;EAC3C;EACA;EACA,IAAI,CAAC2M,cAAc,IAAI,CAACN,YAAY,KAAK,EAAE7U,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC,IAAI,CAAC0S,GAAG,CAAC,EAAE;IACxF;IACA,IAAI/T,MAAM,EAAE;MACRG,GAAG,CAACa,KAAK,CAAC2G,KAAK,CAACC,KAAK,EAAE+B,GAAG,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC,MACI;MACD3I,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B;EACJ;EACA,MAAMqN,QAAQ,GAAGjX,GAAG,CAAC,CAAC,CAAC,CAAC;EACxB;EACA;EACA,IAAIkX,cAAc;EAClB,SAASC,MAAMA,CAACC,qBAAqB,EAAE;IACnC,IAAIC,oBAAoB;IACxBT,WAAW,GAAGG,eAAe,GAAG,KAAK;IACrC;IACA;IACA,IAAK3V,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;MACzCuV,cAAc,GAAG,EAAE;IACvB;IACA,IAAI,OAAOO,qBAAqB,KAAK,UAAU,EAAE;MAC7CA,qBAAqB,CAACnW,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,CAAC;MAC7CyN,oBAAoB,GAAG;QACnB1U,IAAI,EAAEZ,YAAY,CAAC+J,aAAa;QAChCzB,OAAO,EAAET,GAAG;QACZuB,MAAM,EAAE0L;MACZ,CAAC;IACL,CAAC,MACI;MACDzB,oBAAoB,CAACnU,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,EAAEwN,qBAAqB,CAAC;MACnEC,oBAAoB,GAAG;QACnB1U,IAAI,EAAEZ,YAAY,CAACgK,WAAW;QAC9B6B,OAAO,EAAEwJ,qBAAqB;QAC9B/M,OAAO,EAAET,GAAG;QACZuB,MAAM,EAAE0L;MACZ,CAAC;IACL;IACA,MAAMS,YAAY,GAAIJ,cAAc,GAAG3V,MAAM,CAAC,CAAE;IAChDZ,QAAQ,CAAC,CAAC,CAAC4W,IAAI,CAAC,MAAM;MAClB,IAAIL,cAAc,KAAKI,YAAY,EAAE;QACjCV,WAAW,GAAG,IAAI;MACtB;IACJ,CAAC,CAAC;IACFG,eAAe,GAAG,IAAI;IACtB;IACAhC,oBAAoB,CAACN,aAAa,EAAE4C,oBAAoB,EAAEpW,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,CAAC;EACrF;EACA,MAAM6D,MAAM,GAAG8I,cAAc,GACvB,SAAS9I,MAAMA,CAAA,EAAG;IAChB,MAAM;MAAE7F;IAAM,CAAC,GAAGqK,OAAO;IACzB,MAAMyB,QAAQ,GAAG9L,KAAK,GAAGA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IACrC;IACA,IAAI,CAACuP,MAAM,CAAEvM,MAAM,IAAK;MACpB;MACAlE,MAAM,CAACkE,MAAM,EAAE8I,QAAQ,CAAC;IAC5B,CAAC,CAAC;EACN,CAAC,GACC;EACGtS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAChC,MAAM;IACJ,MAAM,IAAIkF,KAAK,CAAC,cAAcoD,GAAG,oEAAoE,CAAC;EAC1G,CAAC,GACC2K,IAAI;EAClB,SAASrD,QAAQA,CAAA,EAAG;IAChBqB,KAAK,CAACc,IAAI,CAAC,CAAC;IACZoB,aAAa,GAAG,EAAE;IAClBuC,mBAAmB,GAAG,EAAE;IACxB/V,KAAK,CAACgJ,EAAE,CAACuN,MAAM,CAAC5N,GAAG,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACI,MAAMuD,MAAM,GAAGA,CAACsG,EAAE,EAAExQ,IAAI,GAAG,EAAE,KAAK;IAC9B,IAAIiS,aAAa,IAAIzB,EAAE,EAAE;MACrBA,EAAE,CAAC0B,WAAW,CAAC,GAAGlS,IAAI;MACtB,OAAOwQ,EAAE;IACb;IACA,MAAMgE,aAAa,GAAG,SAAAA,CAAA,EAAY;MAC9BzW,cAAc,CAACC,KAAK,CAAC;MACrB,MAAM6O,IAAI,GAAG/F,KAAK,CAACC,IAAI,CAAC+H,SAAS,CAAC;MAClC,MAAM2F,iBAAiB,GAAG,EAAE;MAC5B,MAAMC,mBAAmB,GAAG,EAAE;MAC9B,SAAS/H,KAAKA,CAAC8E,QAAQ,EAAE;QACrBgD,iBAAiB,CAACnM,IAAI,CAACmJ,QAAQ,CAAC;MACpC;MACA,SAAS7E,OAAOA,CAAC6E,QAAQ,EAAE;QACvBiD,mBAAmB,CAACpM,IAAI,CAACmJ,QAAQ,CAAC;MACtC;MACA;MACAK,oBAAoB,CAACiC,mBAAmB,EAAE;QACtClH,IAAI;QACJ7M,IAAI,EAAEwU,aAAa,CAACtC,WAAW,CAAC;QAChC1L,KAAK;QACLmG,KAAK;QACLC;MACJ,CAAC,CAAC;MACF,IAAI+H,GAAG;MACP,IAAI;QACAA,GAAG,GAAGnE,EAAE,CAAC3B,KAAK,CAAC,IAAI,IAAI,IAAI,CAAClI,GAAG,KAAKA,GAAG,GAAG,IAAI,GAAGH,KAAK,EAAEqG,IAAI,CAAC;QAC7D;MACJ,CAAC,CACD,OAAOlM,KAAK,EAAE;QACVmR,oBAAoB,CAAC4C,mBAAmB,EAAE/T,KAAK,CAAC;QAChD,MAAMA,KAAK;MACf;MACA,IAAIgU,GAAG,YAAYrP,OAAO,EAAE;QACxB,OAAOqP,GAAG,CACLL,IAAI,CAAE1P,KAAK,IAAK;UACjBkN,oBAAoB,CAAC2C,iBAAiB,EAAE7P,KAAK,CAAC;UAC9C,OAAOA,KAAK;QAChB,CAAC,CAAC,CACGgQ,KAAK,CAAEjU,KAAK,IAAK;UAClBmR,oBAAoB,CAAC4C,mBAAmB,EAAE/T,KAAK,CAAC;UAChD,OAAO2E,OAAO,CAACE,MAAM,CAAC7E,KAAK,CAAC;QAChC,CAAC,CAAC;MACN;MACA;MACAmR,oBAAoB,CAAC2C,iBAAiB,EAAEE,GAAG,CAAC;MAC5C,OAAOA,GAAG;IACd,CAAC;IACDH,aAAa,CAACvC,aAAa,CAAC,GAAG,IAAI;IACnCuC,aAAa,CAACtC,WAAW,CAAC,GAAGlS,IAAI,CAAC,CAAC;IACnC;IACA;IACA,OAAOwU,aAAa;EACxB,CAAC;EACD,MAAMpF,WAAW,GAAG,aAAcvS,OAAO,CAAC;IACtCoN,OAAO,EAAE,CAAC,CAAC;IACX3C,OAAO,EAAE,CAAC,CAAC;IACX3C,KAAK,EAAE,EAAE;IACTqP;EACJ,CAAC,CAAC;EACF,MAAMa,YAAY,GAAG;IACjB5F,EAAE,EAAEjR,KAAK;IACT;IACA2I,GAAG;IACH+F,SAAS,EAAE6E,eAAe,CAAC/E,IAAI,CAAC,IAAI,EAAEuH,mBAAmB,CAAC;IAC1DG,MAAM;IACN1J,MAAM;IACNiD,UAAUA,CAACgE,QAAQ,EAAEzC,OAAO,GAAG,CAAC,CAAC,EAAE;MAC/B,MAAM2C,kBAAkB,GAAGJ,eAAe,CAACC,aAAa,EAAEC,QAAQ,EAAEzC,OAAO,CAACrB,QAAQ,EAAE,MAAMmH,WAAW,CAAC,CAAC,CAAC;MAC1G,MAAMA,WAAW,GAAGxF,KAAK,CAACC,GAAG,CAAC,MAAM5S,KAAK,CAAC,MAAMqB,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,EAAGhC,KAAK,IAAK;QAC/E,IAAIqK,OAAO,CAACpB,KAAK,KAAK,MAAM,GAAGkG,eAAe,GAAGH,WAAW,EAAE;UAC1DlC,QAAQ,CAAC;YACLrK,OAAO,EAAET,GAAG;YACZjH,IAAI,EAAEZ,YAAY,CAAC8J,MAAM;YACzBV,MAAM,EAAE0L;UACZ,CAAC,EAAEjP,KAAK,CAAC;QACb;MACJ,CAAC,EAAElB,MAAM,CAAC,CAAC,CAAC,EAAEgQ,iBAAiB,EAAEzE,OAAO,CAAC,CAAC,CAAC;MAC3C,OAAO2C,kBAAkB;IAC7B,CAAC;IACD1D;EACJ,CAAC;EACD;EACA,IAAIjR,MAAM,EAAE;IACR;IACA6X,YAAY,CAACzB,EAAE,GAAG,KAAK;EAC3B;EACA,MAAM5M,KAAK,GAAGjJ,QAAQ,CAAEY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAO,CAAEF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOyR,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAE3R,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAU,GACvO0E,MAAM,CAAC;IACL2L,WAAW;IACXtH,iBAAiB,EAAEjL,OAAO,CAAC,IAAIwV,GAAG,CAAC,CAAC,CAAC,CAAE;EAC3C,CAAC,EAAEwC;EACH;EACA;EACA,CAAC,GACCA,YAAY,CAAC;EACnB;EACA;EACA7W,KAAK,CAACgJ,EAAE,CAAC7J,GAAG,CAACwJ,GAAG,EAAEH,KAAK,CAAC;EACxB,MAAMuO,cAAc,GAAI/W,KAAK,CAAC0R,EAAE,IAAI1R,KAAK,CAAC0R,EAAE,CAACqF,cAAc,IAAK/C,sBAAsB;EACtF;EACA,MAAMgD,UAAU,GAAGD,cAAc,CAAC,MAAM/W,KAAK,CAACiS,EAAE,CAACV,GAAG,CAAC,MAAM,CAACD,KAAK,GAAGxS,WAAW,CAAC,CAAC,EAAEyS,GAAG,CAAC,MAAM0D,KAAK,CAAC;IAAE/I;EAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACjH;EACA,KAAK,MAAMlE,GAAG,IAAIgP,UAAU,EAAE;IAC1B,MAAMC,IAAI,GAAGD,UAAU,CAAChP,GAAG,CAAC;IAC5B,IAAK/I,KAAK,CAACgY,IAAI,CAAC,IAAI,CAACpC,UAAU,CAACoC,IAAI,CAAC,IAAK/X,UAAU,CAAC+X,IAAI,CAAC,EAAE;MACxD;MACA,IAAK9W,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK0S,GAAG,EAAE;QAChD5T,GAAG,CAAC6W,QAAQ,CAACpP,KAAK,EAAEoB,GAAG,EAAExI,KAAK,CAACwX,UAAU,EAAEhP,GAAG,CAAC,CAAC;QAChD;QACA;MACJ,CAAC,MACI,IAAI,CAACsN,cAAc,EAAE;QACtB;QACA,IAAIN,YAAY,IAAIJ,aAAa,CAACqC,IAAI,CAAC,EAAE;UACrC,IAAIhY,KAAK,CAACgY,IAAI,CAAC,EAAE;YACbA,IAAI,CAACrQ,KAAK,GAAGoO,YAAY,CAAChN,GAAG,CAAC;UAClC,CAAC,MACI;YACD;YACA;YACAmM,oBAAoB,CAAC8C,IAAI,EAAEjC,YAAY,CAAChN,GAAG,CAAC,CAAC;UACjD;QACJ;QACA;QACA;QACA,IAAIhJ,MAAM,EAAE;UACRG,GAAG,CAACa,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,EAAEX,GAAG,EAAEiP,IAAI,CAAC;QAC1C,CAAC,MACI;UACDjX,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,CAACX,GAAG,CAAC,GAAGiP,IAAI;QACtC;MACJ;MACA;MACA,IAAK9W,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzC+Q,WAAW,CAACzK,KAAK,CAAC2D,IAAI,CAACtC,GAAG,CAAC;MAC/B;MACA;IACJ,CAAC,MACI,IAAI,OAAOiP,IAAI,KAAK,UAAU,EAAE;MACjC,MAAMC,WAAW,GAAI/W,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK0S,GAAG,GAAGkE,IAAI,GAAG/K,MAAM,CAAC+K,IAAI,EAAEjP,GAAG,CAAC;MAC7F;MACA;MACA;MACA,IAAIhJ,MAAM,EAAE;QACRG,GAAG,CAAC6X,UAAU,EAAEhP,GAAG,EAAEkP,WAAW,CAAC;MACrC,CAAC,MACI;QACD;QACAF,UAAU,CAAChP,GAAG,CAAC,GAAGkP,WAAW;MACjC;MACA;MACA,IAAK/W,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzC+Q,WAAW,CAACnF,OAAO,CAACjE,GAAG,CAAC,GAAGiP,IAAI;MACnC;MACA;MACA;MACA1B,gBAAgB,CAACtJ,OAAO,CAACjE,GAAG,CAAC,GAAGiP,IAAI;IACxC,CAAC,MACI,IAAK9W,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;MAC9C;MACA,IAAIwU,UAAU,CAACoC,IAAI,CAAC,EAAE;QAClB7F,WAAW,CAAC9H,OAAO,CAACtB,GAAG,CAAC,GAAGsN,cAAc;QACnC;QACEtE,OAAO,CAAC1H,OAAO,CAACtB,GAAG,CAAC,GACtBiP,IAAI;QACV,IAAIlW,SAAS,EAAE;UACX,MAAMuI,OAAO,GAAG0N,UAAU,CAACvN,QAAQ;UAC/B;UACCuN,UAAU,CAACvN,QAAQ,GAAG5K,OAAO,CAAC,EAAE,CAAC,CAAC;UACvCyK,OAAO,CAACgB,IAAI,CAACtC,GAAG,CAAC;QACrB;MACJ;IACJ;EACJ;EACA;EACA;EACA,IAAIhJ,MAAM,EAAE;IACRyB,MAAM,CAACwI,IAAI,CAAC+N,UAAU,CAAC,CAAC9J,OAAO,CAAElF,GAAG,IAAK;MACrC7I,GAAG,CAACqJ,KAAK,EAAER,GAAG,EAAEgP,UAAU,CAAChP,GAAG,CAAC,CAAC;IACpC,CAAC,CAAC;EACN,CAAC,MACI;IACDvC,MAAM,CAAC+C,KAAK,EAAEwO,UAAU,CAAC;IACzB;IACA;IACAvR,MAAM,CAAC/G,KAAK,CAAC8J,KAAK,CAAC,EAAEwO,UAAU,CAAC;EACpC;EACA;EACA;EACA;EACAvW,MAAM,CAACkU,cAAc,CAACnM,KAAK,EAAE,QAAQ,EAAE;IACnCgB,GAAG,EAAEA,CAAA,KAAQrJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK0S,GAAG,GAAGiD,QAAQ,CAACpP,KAAK,GAAG5G,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAE;IACrGxJ,GAAG,EAAGwH,KAAK,IAAK;MACZ;MACA,IAAKxG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK0S,GAAG,EAAE;QAChD,MAAM,IAAIxN,KAAK,CAAC,qBAAqB,CAAC;MAC1C;MACA2Q,MAAM,CAAEvM,MAAM,IAAK;QACf;QACAlE,MAAM,CAACkE,MAAM,EAAEhD,KAAK,CAAC;MACzB,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF;EACA;EACA,IAAKxG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;IACzCmI,KAAK,CAACsH,UAAU,GAAGjR,OAAO,CAAEkR,QAAQ,IAAK;MACrCvH,KAAK,CAACqN,YAAY,GAAG,IAAI;MACzB9F,QAAQ,CAACqB,WAAW,CAACzK,KAAK,CAACuG,OAAO,CAAEiK,QAAQ,IAAK;QAC7C,IAAIA,QAAQ,IAAI3O,KAAK,CAACmB,MAAM,EAAE;UAC1B,MAAMyN,cAAc,GAAGrH,QAAQ,CAACpG,MAAM,CAACwN,QAAQ,CAAC;UAChD,MAAME,cAAc,GAAG7O,KAAK,CAACmB,MAAM,CAACwN,QAAQ,CAAC;UAC7C,IAAI,OAAOC,cAAc,KAAK,QAAQ,IAClC7W,aAAa,CAAC6W,cAAc,CAAC,IAC7B7W,aAAa,CAAC8W,cAAc,CAAC,EAAE;YAC/BvM,WAAW,CAACsM,cAAc,EAAEC,cAAc,CAAC;UAC/C,CAAC,MACI;YACD;YACAtH,QAAQ,CAACpG,MAAM,CAACwN,QAAQ,CAAC,GAAGE,cAAc;UAC9C;QACJ;QACA;QACA;QACAlY,GAAG,CAACqJ,KAAK,EAAE2O,QAAQ,EAAE3X,KAAK,CAACuQ,QAAQ,CAACpG,MAAM,EAAEwN,QAAQ,CAAC,CAAC;MAC1D,CAAC,CAAC;MACF;MACA1W,MAAM,CAACwI,IAAI,CAACT,KAAK,CAACmB,MAAM,CAAC,CAACuD,OAAO,CAAEiK,QAAQ,IAAK;QAC5C,IAAI,EAAEA,QAAQ,IAAIpH,QAAQ,CAACpG,MAAM,CAAC,EAAE;UAChClK,GAAG,CAAC+I,KAAK,EAAE2O,QAAQ,CAAC;QACxB;MACJ,CAAC,CAAC;MACF;MACAxB,WAAW,GAAG,KAAK;MACnBG,eAAe,GAAG,KAAK;MACvB9V,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,GAAGnJ,KAAK,CAACuQ,QAAQ,CAACqB,WAAW,EAAE,UAAU,CAAC;MAChE0E,eAAe,GAAG,IAAI;MACtBpW,QAAQ,CAAC,CAAC,CAAC4W,IAAI,CAAC,MAAM;QAClBX,WAAW,GAAG,IAAI;MACtB,CAAC,CAAC;MACF,KAAK,MAAMpF,UAAU,IAAIR,QAAQ,CAACqB,WAAW,CAACnF,OAAO,EAAE;QACnD,MAAMqL,QAAQ,GAAGvH,QAAQ,CAACQ,UAAU,CAAC;QACrCpR,GAAG,CAACqJ,KAAK,EAAE+H,UAAU,EAAErE,MAAM,CAACoL,QAAQ,EAAE/G,UAAU,CAAC,CAAC;MACxD;MACA;MACA,KAAK,MAAM1G,UAAU,IAAIkG,QAAQ,CAACqB,WAAW,CAAC9H,OAAO,EAAE;QACnD,MAAMiO,MAAM,GAAGxH,QAAQ,CAACqB,WAAW,CAAC9H,OAAO,CAACO,UAAU,CAAC;QACvD,MAAM2N,WAAW,GAAGlC,cAAc;QAC5B;QACE3V,QAAQ,CAAC,MAAM;UACXI,cAAc,CAACC,KAAK,CAAC;UACrB,OAAOuX,MAAM,CAAC3W,IAAI,CAAC4H,KAAK,EAAEA,KAAK,CAAC;QACpC,CAAC,CAAC,GACJ+O,MAAM;QACZpY,GAAG,CAACqJ,KAAK,EAAEqB,UAAU,EAAE2N,WAAW,CAAC;MACvC;MACA;MACA/W,MAAM,CAACwI,IAAI,CAACT,KAAK,CAAC4I,WAAW,CAAC9H,OAAO,CAAC,CAAC4D,OAAO,CAAElF,GAAG,IAAK;QACpD,IAAI,EAAEA,GAAG,IAAI+H,QAAQ,CAACqB,WAAW,CAAC9H,OAAO,CAAC,EAAE;UACxC7J,GAAG,CAAC+I,KAAK,EAAER,GAAG,CAAC;QACnB;MACJ,CAAC,CAAC;MACF;MACAvH,MAAM,CAACwI,IAAI,CAACT,KAAK,CAAC4I,WAAW,CAACnF,OAAO,CAAC,CAACiB,OAAO,CAAElF,GAAG,IAAK;QACpD,IAAI,EAAEA,GAAG,IAAI+H,QAAQ,CAACqB,WAAW,CAACnF,OAAO,CAAC,EAAE;UACxCxM,GAAG,CAAC+I,KAAK,EAAER,GAAG,CAAC;QACnB;MACJ,CAAC,CAAC;MACF;MACAQ,KAAK,CAAC4I,WAAW,GAAGrB,QAAQ,CAACqB,WAAW;MACxC5I,KAAK,CAACiB,QAAQ,GAAGsG,QAAQ,CAACtG,QAAQ;MAClCjB,KAAK,CAACqN,YAAY,GAAG,KAAK;IAC9B,CAAC,CAAC;EACN;EACA,IAAK,CAAE1V,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOyR,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAE3R,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAS,EAAE;IAC3K,MAAM0W,aAAa,GAAG;MAClBC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,IAAI;MAClB;MACAC,UAAU,EAAE;IAChB,CAAC;IACD,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC1K,OAAO,CAAE2K,CAAC,IAAK;MAClEpX,MAAM,CAACkU,cAAc,CAACnM,KAAK,EAAEqP,CAAC,EAAEpS,MAAM,CAAC;QAAEmB,KAAK,EAAE4B,KAAK,CAACqP,CAAC;MAAE,CAAC,EAAEJ,aAAa,CAAC,CAAC;IAC/E,CAAC,CAAC;EACN;EACA;EACA,IAAIzY,MAAM,EAAE;IACR;IACAwJ,KAAK,CAAC4M,EAAE,GAAG,IAAI;EACnB;EACA;EACApV,KAAK,CAACiR,EAAE,CAAC/D,OAAO,CAAE4K,QAAQ,IAAK;IAC3B;IACA,IAAK,CAAE3X,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOyR,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAE3R,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAS,EAAE;MAC3K,MAAMgX,UAAU,GAAGzG,KAAK,CAACC,GAAG,CAAC,MAAMuG,QAAQ,CAAC;QACxCtP,KAAK,EAAEA,KAAK;QACZ8C,GAAG,EAAEtL,KAAK,CAAC0R,EAAE;QACb1R,KAAK;QACLgR,OAAO,EAAEuE;MACb,CAAC,CAAC,CAAC;MACH9U,MAAM,CAACwI,IAAI,CAAC8O,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC7K,OAAO,CAAElF,GAAG,IAAKQ,KAAK,CAACsB,iBAAiB,CAACwK,GAAG,CAACtM,GAAG,CAAC,CAAC;MAChFvC,MAAM,CAAC+C,KAAK,EAAEuP,UAAU,CAAC;IAC7B,CAAC,MACI;MACDtS,MAAM,CAAC+C,KAAK,EAAE8I,KAAK,CAACC,GAAG,CAAC,MAAMuG,QAAQ,CAAC;QACnCtP,KAAK,EAAEA,KAAK;QACZ8C,GAAG,EAAEtL,KAAK,CAAC0R,EAAE;QACb1R,KAAK;QACLgR,OAAO,EAAEuE;MACb,CAAC,CAAC,CAAC,CAAC;IACR;EACJ,CAAC,CAAC;EACF,IAAKpV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACtCmI,KAAK,CAACmB,MAAM,IACZ,OAAOnB,KAAK,CAACmB,MAAM,KAAK,QAAQ,IAChC,OAAOnB,KAAK,CAACmB,MAAM,CAACqO,WAAW,KAAK,UAAU,IAC9C,CAACxP,KAAK,CAACmB,MAAM,CAACqO,WAAW,CAACrX,QAAQ,CAAC,CAAC,CAAC0F,QAAQ,CAAC,eAAe,CAAC,EAAE;IAChE3D,OAAO,CAACqD,IAAI,CAAC,0DAA0D,GACnE,gCAAgC,GAChC,mBAAmByC,KAAK,CAACG,GAAG,IAAI,CAAC;EACzC;EACA;EACA,IAAIqM,YAAY,IACZM,cAAc,IACdtE,OAAO,CAACiH,OAAO,EAAE;IACjBjH,OAAO,CAACiH,OAAO,CAACzP,KAAK,CAACmB,MAAM,EAAEqL,YAAY,CAAC;EAC/C;EACAW,WAAW,GAAG,IAAI;EAClBG,eAAe,GAAG,IAAI;EACtB,OAAOtN,KAAK;AAChB;AACA;AACA;AACA,SAAS0P,WAAWA;AACpB;AACAC,WAAW,EAAElD,KAAK,EAAEmD,YAAY,EAAE;EAC9B,IAAI3P,EAAE;EACN,IAAIuI,OAAO;EACX,MAAMqH,YAAY,GAAG,OAAOpD,KAAK,KAAK,UAAU;EAChD,IAAI,OAAOkD,WAAW,KAAK,QAAQ,EAAE;IACjC1P,EAAE,GAAG0P,WAAW;IAChB;IACAnH,OAAO,GAAGqH,YAAY,GAAGD,YAAY,GAAGnD,KAAK;EACjD,CAAC,MACI;IACDjE,OAAO,GAAGmH,WAAW;IACrB1P,EAAE,GAAG0P,WAAW,CAAC1P,EAAE;IACnB,IAAKtI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,OAAOoI,EAAE,KAAK,QAAQ,EAAE;MACnE,MAAM,IAAIlD,KAAK,CAAC,wEAAwE,CAAC;IAC7F;EACJ;EACA,SAAS4N,QAAQA,CAACnT,KAAK,EAAE+S,GAAG,EAAE;IAC1B,MAAMuF,UAAU,GAAG9Z,mBAAmB,CAAC,CAAC;IACxCwB,KAAK;IACD;IACA;IACA,CAAEG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAKP,WAAW,IAAIA,WAAW,CAACoR,QAAQ,GAAG,IAAI,GAAGlR,KAAK,MACnFsY,UAAU,GAAG7Z,MAAM,CAACyB,WAAW,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;IACvD,IAAIF,KAAK,EACLD,cAAc,CAACC,KAAK,CAAC;IACzB,IAAKG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACP,WAAW,EAAE;MACzD,MAAM,IAAIyF,KAAK,CAAC,qIAAqI,GACjJ,oFAAoF,GACpF,+BAA+B,CAAC;IACxC;IACAvF,KAAK,GAAGF,WAAW;IACnB,IAAI,CAACE,KAAK,CAACgJ,EAAE,CAACgF,GAAG,CAACvF,EAAE,CAAC,EAAE;MACnB;MACA,IAAI4P,YAAY,EAAE;QACdhD,gBAAgB,CAAC5M,EAAE,EAAEwM,KAAK,EAAEjE,OAAO,EAAEhR,KAAK,CAAC;MAC/C,CAAC,MACI;QACD+U,kBAAkB,CAACtM,EAAE,EAAEuI,OAAO,EAAEhR,KAAK,CAAC;MAC1C;MACA;MACA,IAAKG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzC;QACA8S,QAAQ,CAACF,MAAM,GAAGjT,KAAK;MAC3B;IACJ;IACA,MAAMwI,KAAK,GAAGxI,KAAK,CAACgJ,EAAE,CAACQ,GAAG,CAACf,EAAE,CAAC;IAC9B,IAAKtI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK0S,GAAG,EAAE;MAChD,MAAMwF,KAAK,GAAG,QAAQ,GAAG9P,EAAE;MAC3B,MAAMsH,QAAQ,GAAGsI,YAAY,GACvBhD,gBAAgB,CAACkD,KAAK,EAAEtD,KAAK,EAAEjE,OAAO,EAAEhR,KAAK,EAAE,IAAI,CAAC,GACpD+U,kBAAkB,CAACwD,KAAK,EAAE9S,MAAM,CAAC,CAAC,CAAC,EAAEuL,OAAO,CAAC,EAAEhR,KAAK,EAAE,IAAI,CAAC;MACjE+S,GAAG,CAACjD,UAAU,CAACC,QAAQ,CAAC;MACxB;MACA,OAAO/P,KAAK,CAAC2G,KAAK,CAACC,KAAK,CAAC2R,KAAK,CAAC;MAC/BvY,KAAK,CAACgJ,EAAE,CAACuN,MAAM,CAACgC,KAAK,CAAC;IAC1B;IACA,IAAKpY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKU,SAAS,EAAE;MACtD,MAAMyX,eAAe,GAAGlZ,kBAAkB,CAAC,CAAC;MAC5C;MACA,IAAIkZ,eAAe,IACfA,eAAe,CAAC3L,KAAK;MACrB;MACA,CAACkG,GAAG,EAAE;QACN,MAAM0F,EAAE,GAAGD,eAAe,CAAC3L,KAAK;QAChC,MAAM6L,KAAK,GAAG,UAAU,IAAID,EAAE,GAAGA,EAAE,CAAC1L,QAAQ,GAAI0L,EAAE,CAAC1L,QAAQ,GAAG,CAAC,CAAE;QACjE2L,KAAK,CAACjQ,EAAE,CAAC,GAAGD,KAAK;MACrB;IACJ;IACA;IACA,OAAOA,KAAK;EAChB;EACA2K,QAAQ,CAACxK,GAAG,GAAGF,EAAE;EACjB,OAAO0K,QAAQ;AACnB;AAEA,IAAIwF,cAAc,GAAG,OAAO;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,MAAM,CAAC;AAAA,EAChC;EACEF,cAAc,GAAGE,MAAM;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAC,GAAGvL,MAAM,EAAE;EAC1B,IAAKpN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKyI,KAAK,CAACqB,OAAO,CAACoD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IACrE7K,OAAO,CAACqD,IAAI,CAAC,qFAAqF,GAC9F,WAAW,GACX,6CAA6C,GAC7C,QAAQ,GACR,2CAA2C,GAC3C,4CAA4C,CAAC;IACjDwH,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;EACtB;EACA,OAAOA,MAAM,CAAC7D,MAAM,CAAC,CAACqP,OAAO,EAAE5F,QAAQ,KAAK;IACxC;IACA4F,OAAO,CAAC5F,QAAQ,CAACxK,GAAG,GAAGgQ,cAAc,CAAC,GAAG,YAAY;MACjD,OAAOxF,QAAQ,CAAC,IAAI,CAACzF,MAAM,CAAC;IAChC,CAAC;IACD,OAAOqL,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC7F,QAAQ,EAAE8F,YAAY,EAAE;EACtC,OAAOnQ,KAAK,CAACqB,OAAO,CAAC8O,YAAY,CAAC,GAC5BA,YAAY,CAACvP,MAAM,CAAC,CAACqP,OAAO,EAAE/Q,GAAG,KAAK;IACpC+Q,OAAO,CAAC/Q,GAAG,CAAC,GAAG,YAAY;MACvB;MACA,OAAOmL,QAAQ,CAAC,IAAI,CAACzF,MAAM,CAAC,CAAC1F,GAAG,CAAC;IACrC,CAAC;IACD,OAAO+Q,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC,GACJtY,MAAM,CAACwI,IAAI,CAACgQ,YAAY,CAAC,CAACvP,MAAM,CAAC,CAACqP,OAAO,EAAE/Q,GAAG,KAAK;IACjD;IACA+Q,OAAO,CAAC/Q,GAAG,CAAC,GAAG,YAAY;MACvB,MAAMQ,KAAK,GAAG2K,QAAQ,CAAC,IAAI,CAACzF,MAAM,CAAC;MACnC,MAAMwL,QAAQ,GAAGD,YAAY,CAACjR,GAAG,CAAC;MAClC;MACA;MACA,OAAO,OAAOkR,QAAQ,KAAK,UAAU,GAC/BA,QAAQ,CAACtY,IAAI,CAAC,IAAI,EAAE4H,KAAK,CAAC;MAC1B;MACEA,KAAK,CAAC0Q,QAAQ,CAAC;IAC3B,CAAC;IACD,OAAOH,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;AACA;AACA;AACA;AACA;AACA,MAAMI,UAAU,GAAGH,QAAQ;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,UAAUA,CAACjG,QAAQ,EAAE8F,YAAY,EAAE;EACxC,OAAOnQ,KAAK,CAACqB,OAAO,CAAC8O,YAAY,CAAC,GAC5BA,YAAY,CAACvP,MAAM,CAAC,CAACqP,OAAO,EAAE/Q,GAAG,KAAK;IACpC;IACA+Q,OAAO,CAAC/Q,GAAG,CAAC,GAAG,UAAU,GAAG6G,IAAI,EAAE;MAC9B;MACA,OAAOsE,QAAQ,CAAC,IAAI,CAACzF,MAAM,CAAC,CAAC1F,GAAG,CAAC,CAAC,GAAG6G,IAAI,CAAC;IAC9C,CAAC;IACD,OAAOkK,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC,GACJtY,MAAM,CAACwI,IAAI,CAACgQ,YAAY,CAAC,CAACvP,MAAM,CAAC,CAACqP,OAAO,EAAE/Q,GAAG,KAAK;IACjD;IACA+Q,OAAO,CAAC/Q,GAAG,CAAC,GAAG,UAAU,GAAG6G,IAAI,EAAE;MAC9B;MACA,OAAOsE,QAAQ,CAAC,IAAI,CAACzF,MAAM,CAAC,CAACuL,YAAY,CAACjR,GAAG,CAAC,CAAC,CAAC,GAAG6G,IAAI,CAAC;IAC5D,CAAC;IACD,OAAOkK,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,gBAAgBA,CAAClG,QAAQ,EAAE8F,YAAY,EAAE;EAC9C,OAAOnQ,KAAK,CAACqB,OAAO,CAAC8O,YAAY,CAAC,GAC5BA,YAAY,CAACvP,MAAM,CAAC,CAACqP,OAAO,EAAE/Q,GAAG,KAAK;IACpC+Q,OAAO,CAAC/Q,GAAG,CAAC,GAAG;MACXwB,GAAGA,CAAA,EAAG;QACF,OAAO2J,QAAQ,CAAC,IAAI,CAACzF,MAAM,CAAC,CAAC1F,GAAG,CAAC;MACrC,CAAC;MACD7I,GAAGA,CAACyH,KAAK,EAAE;QACP,OAAQuM,QAAQ,CAAC,IAAI,CAACzF,MAAM,CAAC,CAAC1F,GAAG,CAAC,GAAGpB,KAAK;MAC9C;IACJ,CAAC;IACD,OAAOmS,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC,GACJtY,MAAM,CAACwI,IAAI,CAACgQ,YAAY,CAAC,CAACvP,MAAM,CAAC,CAACqP,OAAO,EAAE/Q,GAAG,KAAK;IACjD+Q,OAAO,CAAC/Q,GAAG,CAAC,GAAG;MACXwB,GAAGA,CAAA,EAAG;QACF,OAAO2J,QAAQ,CAAC,IAAI,CAACzF,MAAM,CAAC,CAACuL,YAAY,CAACjR,GAAG,CAAC,CAAC;MACnD,CAAC;MACD7I,GAAGA,CAACyH,KAAK,EAAE;QACP,OAAQuM,QAAQ,CAAC,IAAI,CAACzF,MAAM,CAAC,CAACuL,YAAY,CAACjR,GAAG,CAAC,CAAC,GAAGpB,KAAK;MAC5D;IACJ,CAAC;IACD,OAAOmS,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,WAAWA,CAAC9Q,KAAK,EAAE;EACxB;EACA;EACA,IAAIxJ,MAAM,EAAE;IACR;IACA,OAAOY,MAAM,CAAC4I,KAAK,CAAC;EACxB,CAAC,MACI;IACD,MAAM+Q,QAAQ,GAAG7a,KAAK,CAAC8J,KAAK,CAAC;IAC7B,MAAMgR,IAAI,GAAG,CAAC,CAAC;IACf,KAAK,MAAMxR,GAAG,IAAIuR,QAAQ,EAAE;MACxB,MAAM3S,KAAK,GAAG2S,QAAQ,CAACvR,GAAG,CAAC;MAC3B;MACA;MACA,IAAIpB,KAAK,CAACkO,MAAM,EAAE;QACd;QACA0E,IAAI,CAACxR,GAAG,CAAC;QACL;QACArI,QAAQ,CAAC;UACL6J,GAAG,EAAEA,CAAA,KAAMhB,KAAK,CAACR,GAAG,CAAC;UACrB7I,GAAGA,CAACyH,KAAK,EAAE;YACP4B,KAAK,CAACR,GAAG,CAAC,GAAGpB,KAAK;UACtB;QACJ,CAAC,CAAC;MACV,CAAC,MACI,IAAI3H,KAAK,CAAC2H,KAAK,CAAC,IAAI1H,UAAU,CAAC0H,KAAK,CAAC,EAAE;QACxC;QACA4S,IAAI,CAACxR,GAAG,CAAC;QACL;QACAxI,KAAK,CAACgJ,KAAK,EAAER,GAAG,CAAC;MACzB;IACJ;IACA,OAAOwR,IAAI;EACf;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACnC;EACA;EACAA,IAAI,CAACC,KAAK,CAAC;IACPC,YAAYA,CAAA,EAAG;MACX,MAAM5I,OAAO,GAAG,IAAI,CAAC6I,QAAQ;MAC7B,IAAI7I,OAAO,CAAChR,KAAK,EAAE;QACf,MAAMA,KAAK,GAAGgR,OAAO,CAAChR,KAAK;QAC3B;QACA;QACA,IAAI,CAAC,IAAI,CAAC8Z,SAAS,EAAE;UACjB,MAAMC,YAAY,GAAG,CAAC,CAAC;UACvBtZ,MAAM,CAACkU,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE;YACrCnL,GAAG,EAAEA,CAAA,KAAMuQ,YAAY;YACvB5a,GAAG,EAAG6a,CAAC,IAAKvZ,MAAM,CAACgF,MAAM,CAACsU,YAAY,EAAEC,CAAC;UAC7C,CAAC,CAAC;QACN;QACA,IAAI,CAACF,SAAS,CAAC5Z,WAAW,CAAC,GAAGF,KAAK;QACnC;QACA;QACA;QACA,IAAI,CAAC,IAAI,CAAC0N,MAAM,EAAE;UACd,IAAI,CAACA,MAAM,GAAG1N,KAAK;QACvB;QACAA,KAAK,CAAC0R,EAAE,GAAG,IAAI;QACf,IAAI3Q,SAAS,EAAE;UACX;UACA;UACAhB,cAAc,CAACC,KAAK,CAAC;QACzB;QACA,IAAK,CAAEG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOyR,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAE3R,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAS,EAAE;UAC3KsK,qBAAqB,CAACrL,KAAK,CAAC0R,EAAE,EAAE1R,KAAK,CAAC;QAC1C;MACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAAC0N,MAAM,IAAIsD,OAAO,CAACiJ,MAAM,IAAIjJ,OAAO,CAACiJ,MAAM,CAACvM,MAAM,EAAE;QAC9D,IAAI,CAACA,MAAM,GAAGsD,OAAO,CAACiJ,MAAM,CAACvM,MAAM;MACvC;IACJ,CAAC;IACDwM,SAASA,CAAA,EAAG;MACR,OAAO,IAAI,CAACnN,QAAQ;IACxB;EACJ,CAAC,CAAC;AACN,CAAC;AAED,SAASjM,YAAY,EAAE2Y,cAAc,EAAE5G,eAAe,EAAExB,WAAW,EAAE6G,WAAW,EAAE/F,YAAY,EAAElS,cAAc,EAAEmZ,UAAU,EAAED,UAAU,EAAEH,QAAQ,EAAEF,SAAS,EAAEO,gBAAgB,EAAEtZ,cAAc,EAAE6Y,iBAAiB,EAAEhE,aAAa,EAAEH,WAAW,EAAE6E,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}