"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authMiddleware = void 0;
const crypto_1 = __importDefault(require("crypto"));
const User_1 = __importDefault(require("../models/User"));
const userService_1 = require("../services/userService");
// Middleware to authenticate Telegram Web App users
const authMiddleware = async (req, res, next) => {
    try {
        const initData = req.headers['x-tg-init-data'];
        if (!initData) {
            return res.status(401).json({ success: false, message: 'No init data provided' });
        }
        // Validate the init data
        const isValid = validateInitData(initData);
        if (!isValid) {
            return res.status(401).json({ success: false, message: 'Invalid init data' });
        }
        // Parse the init data
        const parsedData = parseInitData(initData);
        if (!parsedData.user) {
            return res.status(401).json({ success: false, message: 'No user data in init data' });
        }
        const chatId = parsedData.user.id;
        // Find or create user
        let user = await User_1.default.findOne({ chat_id: chatId });
        if (!user) {
            // Create new user using user service
            user = await userService_1.userService.createUser({
                chat_id: chatId,
                username: parsedData.user.username,
                firstname: parsedData.user.first_name,
                lastname: parsedData.user.last_name,
                role: 'user'
            });
        }
        else {
            // Update user data if needed using user service
            await userService_1.userService.updateUserIfNeeded(user, {
                username: parsedData.user.username,
                firstname: parsedData.user.first_name,
                lastname: parsedData.user.last_name
            });
        }
        // Check if user is banned
        if (user.banned) {
            return res.status(403).json({ success: false, message: 'User is banned' });
        }
        // Add user to request object
        req.user = user;
        next();
    }
    catch (error) {
        console.error('Auth middleware error:', error);
        res.status(500).json({ success: false, message: 'Authentication error' });
    }
};
exports.authMiddleware = authMiddleware;
// Helper function to validate Telegram init data
function validateInitData(initData) {
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    if (!botToken) {
        console.error('TELEGRAM_BOT_TOKEN is not defined');
        return false;
    }
    const params = new URLSearchParams(initData);
    const hash = params.get('hash');
    if (!hash)
        return false;
    params.delete('hash');
    const dataCheckString = Array.from(params.entries())
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([key, value]) => `${key}=${value}`)
        .join('\n');
    const secretKey = crypto_1.default.createHmac('sha256', 'WebAppData').update(botToken).digest();
    const calculatedHash = crypto_1.default.createHmac('sha256', secretKey).update(dataCheckString).digest('hex');
    return calculatedHash === hash;
}
// Helper function to parse Telegram init data
function parseInitData(initData) {
    const params = new URLSearchParams(initData);
    const result = {};
    if (params.has('query_id'))
        result.query_id = params.get('query_id') || undefined;
    if (params.has('user')) {
        try {
            result.user = JSON.parse(params.get('user') || '{}');
        }
        catch (e) {
            console.error('Error parsing user data:', e);
        }
    }
    if (params.has('auth_date'))
        result.auth_date = parseInt(params.get('auth_date') || '0', 10);
    if (params.has('hash'))
        result.hash = params.get('hash') || undefined;
    return result;
}
