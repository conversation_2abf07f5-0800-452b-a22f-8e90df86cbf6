import { Telegraf, Context } from 'telegraf';
import mongoose from 'mongoose';
import User from '../models/User';
import Task from '../models/Task';
import CompletedTask from '../models/CompletedTask';

// Состояния для добавления заданий
const addTaskStates = new Map<number, { waitingForData: boolean }>();
import { userService } from '../services/userService';

// Функция для проверки валидности ObjectId
const isValidObjectId = (id: string): boolean => {
  return mongoose.Types.ObjectId.isValid(id);
};

// Глобальная переменная для режима "только для администраторов"
let ADMIN_ONLY_MODE = false;

// Функция для установки режима "только для администраторов"
export const setAdminOnlyMode = (mode: boolean): void => {
  ADMIN_ONLY_MODE = mode;
  console.log(`Admin-only mode set to: ${mode}`);
  // Добавляем дополнительное логирование для отладки
  console.log(`ADMIN_ONLY_MODE global variable is now: ${ADMIN_ONLY_MODE}`);
};

// Setup bot commands and handlers
export const setupBot = (bot: Telegraf<Context>) => {
  // Check if user is admin
  const isAdmin = async (ctx: Context): Promise<boolean> => {
    if (!ctx.from) {
      return false;
    }

    const chatId = ctx.from.id;
    const user = await User.findOne({ chat_id: chatId });
    return user?.role === 'admin';
  };

  // Middleware для проверки режима "только для администраторов"
  // ВАЖНО: регистрируем middleware ДО обработчиков команд
  bot.use(async (ctx, next) => {
    // Пропускаем обновления, которые не являются сообщениями
    if (!ctx.message) {
      return next();
    }

    // Если режим выключен, пропускаем все сообщения
    if (!ADMIN_ONLY_MODE) {
      return next();
    }

    // Проверяем, соответствует ли chat_id пользователя значению ADMIN_CHAT_ID
    const isAdminFromEnv = process.env.ADMIN_CHAT_ID && Number(process.env.ADMIN_CHAT_ID) === ctx.from?.id;

    // Если пользователь указан как администратор в переменной окружения, пропускаем его сообщения
    if (isAdminFromEnv) {
      return next();
    }

    // Если режим включен, проверяем, является ли пользователь администратором в базе данных
    const isUserAdmin = await isAdmin(ctx);

    if (isUserAdmin) {
      return next();
    }

    // Если это команда /start, обрабатываем её для регистрации пользователя,
    // но блокируем ответ
    if ('text' in ctx.message && ctx.message.text.startsWith('/start')) {
      // Сохраняем оригинальный метод reply
      const originalReply = ctx.reply;

      // Переопределяем метод reply, чтобы он ничего не делал
      ctx.reply = () => Promise.resolve({} as any);

      // Вызываем next() для обработки команды /start
      await next();

      // Восстанавливаем оригинальный метод reply
      ctx.reply = originalReply;

      // Логируем регистрацию пользователя в режиме "только для администраторов"
      console.log(`User registered in admin-only mode: ${ctx.from?.id} (${ctx.from?.username})`);

      return;
    }

    // Если пользователь не администратор и режим включен, игнорируем сообщение
    // Не отправляем никакого ответа, чтобы не спамить пользователей
    return;
  });

  // Start command
  bot.start(async (ctx) => {
    try {
      const chatId = ctx.from.id;
      const username = ctx.from.username;
      const firstname = ctx.from.first_name;
      const lastname = ctx.from.last_name;

      // Check if user exists
      let user = await User.findOne({ chat_id: chatId });

      if (!user) {
        // Проверяем, является ли пользователь администратором из переменной окружения
        const isAdminFromEnv = process.env.ADMIN_CHAT_ID && Number(process.env.ADMIN_CHAT_ID) === chatId;

        // Check if this is a referral
        const startParam = ctx.payload;
        let referrerId: number | undefined;
        if (startParam && !isNaN(Number(startParam))) {
          const potentialReferrerId = Number(startParam);
          const referrer = await User.findOne({ chat_id: potentialReferrerId });

          if (referrer && potentialReferrerId !== chatId) {
            referrerId = potentialReferrerId;
          }
        }

        // Create new user using user service
        user = await userService.createUser({
          chat_id: chatId,
          username,
          firstname,
          lastname,
          role: isAdminFromEnv ? 'admin' : 'user',
          referrer: referrerId
        });

        // Логируем информацию о создании администратора
        if (isAdminFromEnv) {
          console.log(`User with chat_id ${chatId} has been created as admin from environment variable`);
        }

        ctx.reply(`Привет, ${firstname}! Добро пожаловать в КиноБот.`);
      } else {
        // Update user's last activity
        user.last_activity = new Date();
        if (user.username !== username || user.firstname !== firstname || user.lastname !== lastname) {
          user.username = username;
          user.firstname = firstname;
          user.lastname = lastname;
        }

        // Проверяем, должен ли пользователь быть администратором
        const isAdminFromEnv = process.env.ADMIN_CHAT_ID && Number(process.env.ADMIN_CHAT_ID) === chatId;
        if (isAdminFromEnv && user.role !== 'admin') {
          user.role = 'admin';
          console.log(`Existing user with chat_id ${chatId} has been updated to admin from environment variable`);
        }

        await user.save();

        ctx.reply(`С возвращением, ${firstname}!`);
      }
    } catch (error) {
      console.error('Error in start command:', error);
      ctx.reply('Произошла ошибка при обработке команды.');
    }
  });

  // Help command
  bot.help((ctx) => {
    ctx.reply(
      'Доступные команды:\n' +
      '/start - Начать работу с ботом\n' +
      '/help - Показать справку\n' +
      '/referral - Получить реферальную ссылку'
    );
  });

  // Admin help command - only for admins
  bot.command('admin', async (ctx) => {
    if (!await isAdmin(ctx)) {
      return; // Silently ignore for non-admins
    }

    const modeStatus = ADMIN_ONLY_MODE ? '✅ Включен' : '❌ Выключен';

    ctx.reply(
      '🔧 Команды администратора:\n\n' +
      '📝 /addtask - Добавить задание (поддерживает формат списка)\n' +
      '📋 /listtasks - Список заданий\n' +
      '✅ /activatetask [ID] - Активировать задание\n' +
      '❌ /deactivatetask [ID] - Деактивировать задание\n' +
      '🗑 /deletetask [ID] - Удалить задание\n' +
      '👑 /makeadmin [ID] - Сделать пользователя администратором\n\n' +
      `🔒 Режим "Только для администраторов": ${modeStatus}\n` +
      '🔐 /adminonly on - Включить режим "Только для администраторов"\n' +
      '🔓 /adminonly off - Выключить режим "Только для администраторов"\n\n' +
      '💡 Для добавления задания просто напишите /addtask и следуйте инструкциям'
    );
  });

  // Referral command
  bot.command('referral', async (ctx) => {
    try {
      const chatId = ctx.from.id;
      const botUsername = process.env.TELEGRAM_BOT_USERNAME || (await bot.telegram.getMe()).username;

      const referralLink = `https://t.me/${botUsername}?start=${chatId}`;

      ctx.reply(
        'Ваша реферальная ссылка:\n\n' +
        `${referralLink}\n\n` +
        'Поделитесь ею с друзьями, чтобы они могли присоединиться к боту через вас.'
      );
    } catch (error) {
      console.error('Error in referral command:', error);
      ctx.reply('Произошла ошибка при создании реферальной ссылки.');
    }
  });

  // Admin commands

  // Команда для отмены добавления задания
  bot.command('cancel', async (ctx) => {
    const userId = ctx.from.id;
    if (addTaskStates.has(userId)) {
      addTaskStates.delete(userId);
      return ctx.reply('❌ Добавление задания отменено.');
    }
    return ctx.reply('Нет активных операций для отмены.');
  });

  // Add task command
  bot.command('addtask', async (ctx) => {
    if (!await isAdmin(ctx)) {
      return; // Silently ignore for non-admins
    }

    const messageText = ctx.message.text;
    const content = messageText.replace('/addtask', '').trim();

    // Если нет содержимого, показываем инструкцию и устанавливаем состояние ожидания
    if (!content) {
      const userId = ctx.from.id;
      addTaskStates.set(userId, { waitingForData: true });

      return ctx.reply(
        '📝 Добавление нового задания\n\n' +
        'Отправьте данные в формате списка:\n\n' +
        '1. Название задания\n' +
        '2. URL логотипа\n' +
        '3. URL задания\n' +
        '4. Баллы\n\n' +
        '💡 Пример:\n' +
        '1. Подписаться на канал\n' +
        '2. https://example.com/logo.png\n' +
        '3. https://t.me/channel\n' +
        '4. 10\n\n' +
        '❌ Для отмены отправьте /cancel'
      );
    }

    try {
      let title, logoUrl, link, points;

      // Проверяем формат списка (должен начинаться с "1.")
      if (!content.trim().startsWith('1.')) {
        return ctx.reply(
          '❌ Неверный формат. Используйте формат списка:\n\n' +
          '1. Название задания\n' +
          '2. URL логотипа\n' +
          '3. URL задания\n' +
          '4. Баллы'
        );
      }

      const lines = content.split('\n').map(line => line.trim()).filter(line => line);

      if (lines.length !== 4) {
        return ctx.reply(
          '❌ Неверное количество данных. Нужно ровно 4 строки:\n' +
          '1. Название задания\n' +
          '2. URL логотипа\n' +
          '3. URL задания\n' +
          '4. Баллы'
        );
      }

      // Парсим строки списка
      title = lines[0].replace(/^1\.\s*/, '').trim();
      logoUrl = lines[1].replace(/^2\.\s*/, '').trim();
      link = lines[2].replace(/^3\.\s*/, '').trim();
      const pointsStr = lines[3].replace(/^4\.\s*/, '').trim();
      points = parseInt(pointsStr, 10);

      // Валидация данных
      if (!title) {
        return ctx.reply('❌ Название задания не может быть пустым.');
      }
      if (!logoUrl) {
        return ctx.reply('❌ URL логотипа не может быть пустым.');
      }
      if (!link) {
        return ctx.reply('❌ URL задания не может быть пустым.');
      }
      if (!pointsStr) {
        return ctx.reply('❌ Баллы не могут быть пустыми.');
      }
      if (isNaN(points) || points <= 0) {
        return ctx.reply('❌ Баллы должны быть положительным числом.');
      }

      const task = new Task({
        title,
        logo_url: logoUrl,
        link,
        is_active: true,
        points
      });

      await task.save();

      // Очищаем состояние ожидания, если оно было установлено
      const userId = ctx.from.id;
      addTaskStates.delete(userId);

      ctx.reply(
        '✅ Задание успешно добавлено!\n\n' +
        `📝 Название: ${title}\n` +
        `🖼 Логотип: ${logoUrl}\n` +
        `🔗 Ссылка: ${link}\n` +
        `⭐ Баллы: ${points}\n` +
        `🆔 ID: ${task._id}`
      );
    } catch (error) {
      console.error('Error in addtask command:', error);
      // Очищаем состояние ожидания при ошибке
      const userId = ctx.from.id;
      addTaskStates.delete(userId);
      ctx.reply('❌ Произошла ошибка при добавлении задания.');
    }
  });

  // List tasks command
  bot.command('listtasks', async (ctx) => {
    if (!await isAdmin(ctx)) {
      return; // Silently ignore for non-admins
    }

    try {
      const tasks = await Task.find().sort({ created_at: -1 });

      if (tasks.length === 0) {
        return ctx.reply('📋 Список заданий пуст.');
      }

      let message = '📋 Список заданий:\n\n';

      tasks.forEach((task, index) => {
        const statusIcon = task.is_active ? '✅' : '❌';
        message += `${index + 1}. ${statusIcon} ${task.title}\n` +
                  `🆔 ID: ${task._id}\n` +
                  `⭐ Баллы: ${task.points}\n` +
                  `🔗 ${task.link}\n\n`;
      });

      ctx.reply(message);
    } catch (error) {
      console.error('Error in listtasks command:', error);
      ctx.reply('❌ Произошла ошибка при получении списка заданий.');
    }
  });

  // Activate task command
  bot.command('activatetask', async (ctx) => {
    if (!await isAdmin(ctx)) {
      return; // Silently ignore for non-admins
    }

    const taskId = ctx.message.text.split(' ')[1];

    if (!taskId) {
      return ctx.reply('Укажите ID задания: /activatetask [ID]');
    }

    if (!isValidObjectId(taskId)) {
      return ctx.reply('❌ Неверный формат ID задания. ID должен быть корректным ObjectId.');
    }

    try {
      const task = await Task.findById(taskId);

      if (!task) {
        return ctx.reply('Задание не найдено.');
      }

      task.is_active = true;
      await task.save();

      ctx.reply(`Задание "${task.title}" успешно активировано.`);
    } catch (error) {
      console.error('Error in activatetask command:', error);
      ctx.reply('Произошла ошибка при активации задания.');
    }
  });

  // Deactivate task command
  bot.command('deactivatetask', async (ctx) => {
    if (!await isAdmin(ctx)) {
      return; // Silently ignore for non-admins
    }

    const taskId = ctx.message.text.split(' ')[1];

    if (!taskId) {
      return ctx.reply('Укажите ID задания: /deactivatetask [ID]');
    }

    if (!isValidObjectId(taskId)) {
      return ctx.reply('❌ Неверный формат ID задания. ID должен быть корректным ObjectId.');
    }

    try {
      const task = await Task.findById(taskId);

      if (!task) {
        return ctx.reply('Задание не найдено.');
      }

      task.is_active = false;
      await task.save();

      ctx.reply(`Задание "${task.title}" успешно деактивировано.`);
    } catch (error) {
      console.error('Error in deactivatetask command:', error);
      ctx.reply('Произошла ошибка при деактивации задания.');
    }
  });

  // Delete task command
  bot.command('deletetask', async (ctx) => {
    if (!await isAdmin(ctx)) {
      return; // Silently ignore for non-admins
    }

    const taskId = ctx.message.text.split(' ')[1];

    if (!taskId) {
      return ctx.reply('Укажите ID задания: /deletetask [ID]');
    }

    if (!isValidObjectId(taskId)) {
      return ctx.reply('❌ Неверный формат ID задания. ID должен быть корректным ObjectId.');
    }

    try {
      const task = await Task.findById(taskId);

      if (!task) {
        return ctx.reply('Задание не найдено.');
      }

      await Task.deleteOne({ _id: taskId });

      ctx.reply(`Задание "${task.title}" успешно удалено.`);
    } catch (error) {
      console.error('Error in deletetask command:', error);
      ctx.reply('Произошла ошибка при удалении задания.');
    }
  });

  // Make admin command
  bot.command('makeadmin', async (ctx) => {
    if (!await isAdmin(ctx)) {
      return; // Silently ignore for non-admins
    }

    const userId = ctx.message.text.split(' ')[1];

    if (!userId || isNaN(Number(userId))) {
      return ctx.reply('Укажите ID пользователя: /makeadmin [ID]');
    }

    try {
      const user = await User.findOne({ chat_id: Number(userId) });

      if (!user) {
        return ctx.reply('Пользователь не найден.');
      }

      user.role = 'admin';
      await user.save();

      ctx.reply(`Пользователь ${user.firstname} ${user.lastname} (${user.username}) теперь администратор.`);
    } catch (error) {
      console.error('Error in makeadmin command:', error);
      ctx.reply('Произошла ошибка при назначении администратора.');
    }
  });

  // Admin only mode command
  bot.command('adminonly', async (ctx) => {
    const isUserAdmin = await isAdmin(ctx);

    if (!isUserAdmin) {
      return; // Silently ignore for non-admins
    }

    const param = ctx.message.text.split(' ')[1]?.toLowerCase();

    if (!param || (param !== 'on' && param !== 'off')) {
      return ctx.reply('Укажите параметр: /adminonly on или /adminonly off');
    }

    try {
      const newMode = param === 'on';
      ADMIN_ONLY_MODE = newMode;

      ctx.reply(
        ADMIN_ONLY_MODE
          ? '✅ Режим "Только для администраторов" включен. Бот будет отвечать только администраторам.'
          : '❌ Режим "Только для администраторов" выключен. Бот будет отвечать всем пользователям.'
      );
    } catch (error) {
      console.error('Error in adminonly command:', error);
      ctx.reply('Произошла ошибка при изменении режима работы бота.');
    }
  });

  // Обработчик текстовых сообщений для добавления заданий
  bot.on('text', async (ctx) => {
    const userId = ctx.from.id;
    const userState = addTaskStates.get(userId);

    // Проверяем, ожидает ли пользователь ввода данных для задания
    if (userState && userState.waitingForData) {
      // Проверяем, является ли пользователь админом
      if (!await isAdmin(ctx)) {
        addTaskStates.delete(userId);
        return ctx.reply('Эта операция доступна только администраторам.');
      }

      const content = ctx.message.text.trim();

      // Игнорируем команды
      if (content.startsWith('/')) {
        return;
      }

      try {
        let title, logoUrl, link, points;

        // Проверяем формат списка (должен начинаться с "1.")
        if (!content.trim().startsWith('1.')) {
          return ctx.reply(
            '❌ Неверный формат. Используйте формат списка:\n\n' +
            '1. Название задания\n' +
            '2. URL логотипа\n' +
            '3. URL задания\n' +
            '4. Баллы\n\n' +
            '❌ Для отмены отправьте /cancel'
          );
        }

        const lines = content.split('\n').map(line => line.trim()).filter(line => line);

        if (lines.length !== 4) {
          return ctx.reply(
            '❌ Неверное количество данных. Нужно ровно 4 строки:\n' +
            '1. Название задания\n' +
            '2. URL логотипа\n' +
            '3. URL задания\n' +
            '4. Баллы\n\n' +
            '❌ Для отмены отправьте /cancel'
          );
        }

        // Парсим строки списка
        title = lines[0].replace(/^1\.\s*/, '').trim();
        logoUrl = lines[1].replace(/^2\.\s*/, '').trim();
        link = lines[2].replace(/^3\.\s*/, '').trim();
        const pointsStr = lines[3].replace(/^4\.\s*/, '').trim();
        points = parseInt(pointsStr, 10);

        // Валидация данных
        if (!title) {
          return ctx.reply('❌ Название задания не может быть пустым.\n\n❌ Для отмены отправьте /cancel');
        }
        if (!logoUrl) {
          return ctx.reply('❌ URL логотипа не может быть пустым.\n\n❌ Для отмены отправьте /cancel');
        }
        if (!link) {
          return ctx.reply('❌ URL задания не может быть пустым.\n\n❌ Для отмены отправьте /cancel');
        }
        if (!pointsStr) {
          return ctx.reply('❌ Баллы не могут быть пустыми.\n\n❌ Для отмены отправьте /cancel');
        }
        if (isNaN(points) || points <= 0) {
          return ctx.reply('❌ Баллы должны быть положительным числом.\n\n❌ Для отмены отправьте /cancel');
        }

        const task = new Task({
          title,
          logo_url: logoUrl,
          link,
          is_active: true,
          points
        });

        await task.save();

        // Удаляем состояние ожидания
        addTaskStates.delete(userId);

        ctx.reply(
          '✅ Задание успешно добавлено!\n\n' +
          `📝 Название: ${title}\n` +
          `🖼 Логотип: ${logoUrl}\n` +
          `🔗 Ссылка: ${link}\n` +
          `⭐ Баллы: ${points}\n` +
          `🆔 ID: ${task._id}`
        );
      } catch (error) {
        console.error('Error processing task data:', error);
        addTaskStates.delete(userId);
        ctx.reply('❌ Произошла ошибка при добавлении задания.');
      }
    }
  });


};
