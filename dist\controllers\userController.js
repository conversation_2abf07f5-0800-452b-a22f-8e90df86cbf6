"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getReferrals = exports.getCurrentUser = void 0;
const User_1 = __importDefault(require("../models/User"));
// Get current user info
const getCurrentUser = async (req, res) => {
    try {
        const userId = req.user.chat_id;
        const user = await User_1.default.findOne({ chat_id: userId });
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }
        res.json({
            success: true,
            data: {
                chat_id: user.chat_id,
                username: user.username,
                firstname: user.firstname,
                lastname: user.lastname,
                role: user.role,
                task_points: user.task_points,
                registration_date: user.created_at
            }
        });
    }
    catch (error) {
        console.error('Error in getCurrentUser controller:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch user information'
        });
    }
};
exports.getCurrentUser = getCurrentUser;
// Get user's referrals
const getReferrals = async (req, res) => {
    try {
        const userId = req.user.chat_id;
        const referrals = await User_1.default.find({ referrer: userId })
            .select('chat_id username firstname lastname created_at')
            .sort({ created_at: -1 });
        // Маппим createdAt в registration_date для совместимости с frontend
        const mappedReferrals = referrals.map(referral => ({
            chat_id: referral.chat_id,
            username: referral.username,
            firstname: referral.firstname,
            lastname: referral.lastname,
            registration_date: referral.created_at
        }));
        res.json({
            success: true,
            data: {
                count: mappedReferrals.length,
                referrals: mappedReferrals
            }
        });
    }
    catch (error) {
        console.error('Error in getReferrals controller:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch referrals'
        });
    }
};
exports.getReferrals = getReferrals;
