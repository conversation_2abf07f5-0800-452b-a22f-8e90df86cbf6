<template>
  <div class="profile-view">
    <div class="container">
      <h1 class="page-title">Личный кабинет</h1>

      <div class="under-construction">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="construction-icon">
          <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
          <polyline points="2 17 12 22 22 17"></polyline>
          <polyline points="2 12 12 17 22 12"></polyline>
        </svg>
        <h2>В разработке</h2>
        <p>Эта функция будет доступна в ближайшее время</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ProfileView'
});
</script>

<style scoped>
.profile-view {
  padding-bottom: 80px;
}

.page-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.under-construction {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;
  text-align: center;
}

.construction-icon {
  width: 80px;
  height: 80px;
  color: var(--tg-theme-button-color);
  margin-bottom: var(--spacing-md);
}

.under-construction h2 {
  font-size: 20px;
  margin-bottom: var(--spacing-sm);
}

.under-construction p {
  color: var(--tg-theme-hint-color);
  font-size: 16px;
}
</style>
