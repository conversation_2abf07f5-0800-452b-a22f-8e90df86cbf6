<template>
  <div class="profile-view">
    <div class="container">
      <h1 class="page-title">Личный кабинет</h1>

      <div class="debug-info">
        <h3>Debug Info:</h3>
        <p><strong>Platform:</strong> {{ platform }}</p>
        <p><strong>Version:</strong> {{ version }}</p>
        <p><strong>ViewportHeight:</strong> {{ viewportHeight }}</p>
        <p><strong>ViewportStableHeight:</strong> {{ viewportStableHeight }}</p>
      </div>

      <div class="under-construction">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="construction-icon">
          <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
          <polyline points="2 17 12 22 22 17"></polyline>
          <polyline points="2 12 12 17 22 12"></polyline>
        </svg>
        <h2>В разработке</h2>
        <p>Эта функция будет доступна в ближайшее время</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';

export default defineComponent({
  name: 'ProfileView',
  setup() {
    const platform = ref('');
    const version = ref('');
    const viewportHeight = ref(0);
    const viewportStableHeight = ref(0);

    onMounted(() => {
      if (window.Telegram?.WebApp) {
        platform.value = window.Telegram.WebApp.platform || 'unknown';
        version.value = window.Telegram.WebApp.version || 'unknown';
        viewportHeight.value = window.Telegram.WebApp.viewportHeight || 0;
        viewportStableHeight.value = window.Telegram.WebApp.viewportStableHeight || 0;
      } else {
        platform.value = 'WebApp not available';
      }
    });

    return {
      platform,
      version,
      viewportHeight,
      viewportStableHeight
    };
  }
});
</script>

<style scoped>
.profile-view {
  padding-bottom: 80px;
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: bold;
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.debug-info {
  background-color: var(--tg-theme-secondary-bg-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  font-size: 14px;
}

.debug-info h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 16px;
}

.debug-info p {
  margin: 4px 0;
}

.under-construction {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;
  text-align: center;
}

.construction-icon {
  width: 80px;
  height: 80px;
  color: var(--tg-theme-button-color);
  margin-bottom: var(--spacing-md);
}

.under-construction h2 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-sm);
}

.under-construction p {
  color: var(--tg-theme-hint-color);
}
</style>
