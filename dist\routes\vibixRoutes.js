"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const vibixController_1 = require("../controllers/vibixController");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const router = express_1.default.Router();
// Apply authentication middleware to all routes
router.use(authMiddleware_1.authMiddleware);
// Get movies by category
router.get('/movies/category/:category', vibixController_1.getMoviesByCategory);
// Get movie by ID
router.get('/movies/:id', vibixController_1.getMovie);
// Search movie by URL
router.get('/search', vibixController_1.searchByUrl);
exports.default = router;
