import { defineStore } from 'pinia';
import api from '@/services/api';

interface Task {
  _id: string;
  title: string;
  logo_url: string;
  link: string;
  type: 'telegram' | 'link';
  is_active: boolean;
  points: number;
  created_at: string;
}

export const useTasksStore = defineStore('tasks', {
  state: () => ({
    tasks: [] as Task[],
    loading: false,
    error: null as string | null
  }),

  actions: {
    async fetchTasks() {
      this.loading = true;
      this.error = null;

      try {
        const response = await api.get('/tasks');

        if (response.data.success) {
          this.tasks = response.data.data;
        } else {
          this.error = response.data.message || 'Failed to load tasks';
        }
      } catch (error: any) {
        this.error = error.response?.data?.message || error.message || 'Unknown error';
        console.error('Error fetching tasks:', error);
      } finally {
        this.loading = false;
      }
    },

    async completeTask(task_id: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await api.post('/tasks/complete', { task_id });

        if (response.data.success) {
          // Refresh tasks after completion
          await this.fetchTasks();
          return true;
        } else {
          this.error = response.data.message || 'Failed to complete task';
          return false;
        }
      } catch (error: any) {
        this.error = error.response?.data?.message || error.message || 'Unknown error';
        console.error('Error completing task:', error);
        return false;
      } finally {
        this.loading = false;
      }
    },

    openTaskLink(link: string) {
      // Check if we're in Telegram WebApp
      const tg = (window as any).Telegram?.WebApp;
      if (tg) {
        // For t.me links, use openTelegramLink to stay within Telegram
        if (link.includes('t.me/') || link.includes('telegram.me/')) {
          if (tg.openTelegramLink) {
            tg.openTelegramLink(link);
          } else {
            // Fallback for older versions
            tg.openLink(link);
          }
        } else {
          // For external links, use openLink to open in external browser
          tg.openLink(link);
        }
      } else {
        // Fallback for non-Telegram environments
        window.open(link, '_blank');
      }
    }
  }
});
