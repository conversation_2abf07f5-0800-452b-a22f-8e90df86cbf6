{"version": 3, "file": "js/69.a3595b21.js", "mappings": "yLAEA,MAAMA,EAAa,CCDZC,MAAM,cDEPC,EAAa,CCDVD,MAAM,aDETE,EAAa,CCAVF,MAAM,oBDCTG,EAAa,CCLnBC,IAAA,EAOuBJ,MAAM,gBDEvBK,EAAa,CCDNL,MAAM,mBDEbM,EAAa,CCAHN,MAAM,iBDChBO,EAAa,CCXnBH,IAAA,EAc0BJ,MAAM,qBDC1BQ,EAAa,CCfnBJ,IAAA,EAmB6BJ,MAAM,mBDA7BS,EAAa,CCnBnBL,IAAA,EAwB0CJ,MAAM,mBDD1CU,EAAc,CCvBpBN,IAAA,EAiCkBJ,MAAM,cDLlB,SAAUW,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAsBC,EAAAA,EAAAA,IAAkB,YAE9C,OAAQC,EAAAA,EAAAA,OC9BRC,EAAAA,EAAAA,IAoCM,MApCNtB,EAoCM,EAnCJuB,EAAAA,EAAAA,IAkCM,MAlCNrB,EAkCM,CDHJY,EAAO,KAAOA,EAAO,IC9BrBS,EAAAA,EAAAA,IAAmC,MAA/BtB,MAAM,cAAa,WAAO,KAC9BsB,EAAAA,EAAAA,IAAyH,IAAzHpB,EAAyH,CD+BvHW,EAAO,KAAOA,EAAO,ICnC7BU,EAAAA,EAAAA,IAIkC,qCAA+BD,EAAAA,EAAAA,IAA0D,KAAvDE,KAAK,IAAIxB,MAAM,YAAayB,QAAKZ,EAAA,KAAAA,EAAA,IAJrGa,EAAAA,EAAAA,KAI+F,QAAc,eAAC,eAG7Fd,EAAAe,ODoCNP,EAAAA,EAAAA,OCpCLC,EAAAA,EAAAA,IAKM,MALNlB,EAKM,EAJJmB,EAAAA,EAAAA,IAGM,MAHNjB,EAGM,CDkCAQ,EAAO,KAAOA,EAAO,ICpCzBS,EAAAA,EAAAA,IAAkD,QAA5CtB,MAAM,iBAAgB,mBAAe,KAC3CsB,EAAAA,EAAAA,IAAyD,OAAzDhB,GAAyDsB,EAAAA,EAAAA,IAA1BhB,EAAAe,KAAKE,aAAW,SAVzDC,EAAAA,EAAAA,IAAA,OAciBlB,EAAAmB,UDqCNX,EAAAA,EAAAA,OCrCLC,EAAAA,EAAAA,IAGM,MAHNd,EAGMM,EAAA,KAAAA,EAAA,KAFJS,EAAAA,EAAAA,IAAyC,OAApCtB,MAAM,yBAAuB,UAClCsB,EAAAA,EAAAA,IAA0B,SAAvB,uBAAmB,OAGRV,EAAAoB,QDqCTZ,EAAAA,EAAAA,OCrCPC,EAAAA,EAAAA,IAGM,MAHNb,EAGM,EAFJc,EAAAA,EAAAA,IAAkB,UAAAM,EAAAA,EAAAA,IAAZhB,EAAAoB,OAAK,IACXV,EAAAA,EAAAA,IAAmE,UAA1DG,QAAKZ,EAAA,KAAAA,EAAA,GDwCtB,IAAIoB,ICxCoBrB,EAAAsB,YAAAtB,EAAAsB,cAAAD,IAAYjC,MAAM,gBAAe,gBAGlB,IAAjBY,EAAAuB,MAAMC,SD0CbhB,EAAAA,EAAAA,OC1CTC,EAAAA,EAAAA,IAOM,MAPNZ,EAOMI,EAAA,KAAAA,EAAA,KANJS,EAAAA,EAAAA,IAIM,OAJDe,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,QAAQxC,MAAM,cDmDzJ,EClDTsB,EAAAA,EAAAA,IAAwC,UAAhCmB,GAAG,KAAKC,GAAG,KAAKC,EAAE,QAC1BrB,EAAAA,EAAAA,IAA4C,QAAtCsB,GAAG,KAAKC,GAAG,IAAIC,GAAG,KAAKC,GAAG,QAChCzB,EAAAA,EAAAA,IAAgD,QAA1CsB,GAAG,KAAKC,GAAG,KAAKC,GAAG,QAAQC,GAAG,SDkE1B,IChEZzB,EAAAA,EAAAA,IAA4B,SAAzB,yBAAqB,SDmEjBF,EAAAA,EAAAA,OChETC,EAAAA,EAAAA,IAEM,MAFNX,EAEM,GD+DKU,EAAAA,EAAAA,KAAW,IChEpBC,EAAAA,EAAAA,IAA+D2B,EAAAA,GAAA,MAlCvEC,EAAAA,EAAAA,IAkCiCrC,EAAAuB,OAARe,KDiEC9B,EAAAA,EAAAA,OCjElB+B,EAAAA,EAAAA,IAA+DjC,EAAA,CAA9Bd,IAAK8C,EAAKE,IAAMF,KAAMA,GDoE1C,KAAM,EAAG,CAAC,YACX,YAIpB,CEzGA,MAAMnD,EAAa,CCAVC,MAAM,aDCTC,ECHN,cDIMC,EAAa,CCJnBE,IAAA,EAIkBJ,MAAM,oBDIlBG,EAAa,CCIVH,MAAM,aDHTK,EAAa,CCITL,MAAM,cDHVM,EAAa,CCIRN,MAAM,eDHXO,EAAa,CCKVP,MAAM,eDJTQ,EAAa,CCZnBJ,IAAA,EAiB0BJ,MAAM,mBDD1BS,EAAa,CChBnBL,IAAA,EAkBkBiC,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,QAAQxC,MAAM,gBDU3K,SAAUW,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OAAQG,EAAAA,EAAAA,OC5BRC,EAAAA,EAAAA,IAqBM,OArBDrB,OADPqD,EAAAA,EAAAA,IAAA,CACa,YAAW,CAAAtB,QAAsCnB,EAAAmB,WAApCN,QAAKZ,EAAA,KAAAA,EAAA,GDgC/B,IAAIoB,IChC6BrB,EAAA0C,UAAA1C,EAAA0C,YAAArB,KDiC5B,EChCDX,EAAAA,EAAAA,IASM,MATNvB,EASM,CAROa,EAAAsC,KAAKK,WDkCXnC,EAAAA,EAAAA,OClCLC,EAAAA,EAAAA,IAAiE,OAHvEjB,IAAA,EAGiCoD,IAAK5C,EAAAsC,KAAKK,SAAWE,IAAK7C,EAAAsC,KAAKQ,ODsCnD,KAAM,ECzCnBzD,MD0CWmB,EAAAA,EAAAA,OCtCLC,EAAAA,EAAAA,IAMM,MANNnB,EAMMW,EAAA,KAAAA,EAAA,KALJS,EAAAA,EAAAA,IAIM,OAJDe,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SD8C/I,EC7CLlB,EAAAA,EAAAA,IAAwC,UAAhCmB,GAAG,KAAKC,GAAG,KAAKC,EAAE,QAC1BrB,EAAAA,EAAAA,IAA4C,QAAtCsB,GAAG,KAAKC,GAAG,IAAIC,GAAG,KAAKC,GAAG,QAChCzB,EAAAA,EAAAA,IAA4C,QAAtCsB,GAAG,IAAIC,GAAG,KAAKC,GAAG,KAAKC,GAAG,SD6D1B,UCzDZzB,EAAAA,EAAAA,IAGM,MAHNnB,EAGM,EAFJmB,EAAAA,EAAAA,IAA4C,KAA5CjB,GAA4CuB,EAAAA,EAAAA,IAAlBhB,EAAAsC,KAAKQ,OAAK,IACpCpC,EAAAA,EAAAA,IAA0D,MAA1DhB,GAA0DsB,EAAAA,EAAAA,IAA9BhB,EAAAsC,KAAKS,QAAS,aAAU,MAEtDrC,EAAAA,EAAAA,IAKM,MALNf,EAKM,CAJOK,EAAAmB,UD6DNX,EAAAA,EAAAA,OC7DLC,EAAAA,EAAAA,IAAkD,MAAlDb,MD8DKY,EAAAA,EAAAA,OC7DLC,EAAAA,EAAAA,IAEM,MAFNZ,EAEMI,EAAA,KAAAA,EAAA,KADJS,EAAAA,EAAAA,IAA6C,YAAnCqC,OAAO,kBAAgB,gBDgEpC,EACL,C,+BEvEO,MAAMC,GAAgBC,EAAAA,EAAAA,IAAY,QAAS,CAChDC,MAAOA,KAAA,CACL3B,MAAO,GACPJ,SAAS,EACTC,MAAO,OAGT+B,QAAS,CACP,gBAAM7B,GACJ8B,KAAKjC,SAAU,EACfiC,KAAKhC,MAAQ,KAEb,IACE,MAAMiC,QAAiBC,EAAAA,EAAIC,IAAI,UAE3BF,EAASG,KAAKC,QAChBL,KAAK7B,MAAQ8B,EAASG,KAAKA,KAE3BJ,KAAKhC,MAAQiC,EAASG,KAAKE,SAAW,sBAE1C,CAAE,MAAOtC,GACPgC,KAAKhC,MAAQA,EAAMiC,UAAUG,MAAME,SAAWtC,EAAMsC,SAAW,gBAC/DC,QAAQvC,MAAM,wBAAyBA,EACzC,CAAE,QACAgC,KAAKjC,SAAU,CACjB,CACF,EAEA,kBAAMyC,CAAaC,GACjBT,KAAKjC,SAAU,EACfiC,KAAKhC,MAAQ,KAEb,IACE,MAAMiC,QAAiBC,EAAAA,EAAIQ,KAAK,kBAAmB,CAAED,YAErD,OAAIR,EAASG,KAAKC,eAEVL,KAAK9B,cACJ,IAEP8B,KAAKhC,MAAQiC,EAASG,KAAKE,SAAW,2BAC/B,EAEX,CAAE,MAAOtC,GAGP,OAFAgC,KAAKhC,MAAQA,EAAMiC,UAAUG,MAAME,SAAWtC,EAAMsC,SAAW,gBAC/DC,QAAQvC,MAAM,yBAA0BA,IACjC,CACT,CAAE,QACAgC,KAAKjC,SAAU,CACjB,CACF,EAEA4C,YAAAA,CAAaC,GAEX,MAAMC,EAAMC,OAAeC,UAAUC,OACjCH,GAEED,EAAKK,SAAS,UAAYL,EAAKK,SAAS,kBACtCJ,EAAGK,iBACLL,EAAGK,iBAAiBN,GAOtBC,EAAGM,SAASP,GAIdE,OAAOM,KAAKR,EAAM,SAEtB,KDxDJ,GAAeS,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,WACNC,MAAO,CACLrC,KAAM,CACJsC,KAAMC,OACNC,UAAU,IAGdC,KAAAA,CAAMJ,GACJ,MAAMK,EAAahC,IACb7B,GAAU8D,EAAAA,EAAAA,KAAI,GAGdvC,EAAWwC,UACf/D,EAAQgE,OAAQ,EAEhB,IAEEH,EAAWjB,aAAaY,EAAMrC,KAAK0B,YAG7BgB,EAAWpB,aAAae,EAAMrC,KAAKE,IAC3C,CAAE,MAAOpB,GACPuC,QAAQvC,MAAM,yBAA0BA,EAC1C,CAAE,QACAD,EAAQgE,OAAQ,CAClB,GAGF,MAAO,CACLzC,WACAvB,UAEJ,I,aEvDF,MAAMiE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,I,aJqCA,SAAeX,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,YACNW,WAAY,CACVC,SAAQA,GAEVP,KAAAA,GACE,MAAMC,EAAahC,IACbuC,GAAYC,EAAAA,EAAAA,KAGZjE,GAAQkE,EAAAA,EAAAA,KAAS,IAAMT,EAAWzD,QAClCJ,GAAUsE,EAAAA,EAAAA,KAAS,IAAMT,EAAW7D,UACpCC,GAAQqE,EAAAA,EAAAA,KAAS,IAAMT,EAAW5D,QAClCL,GAAO0E,EAAAA,EAAAA,KAAS,IAAMF,EAAUxE,OAGhCO,EAAa4D,gBACXF,EAAW1D,YAAY,EAO/B,OAJAoE,EAAAA,EAAAA,KAAU,KACRpE,GAAY,IAGP,CACLC,QACAJ,UACAC,QACAL,OACAO,aAEJ,IKtEI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASvB,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://cinema-bot-frontend/./src/views/TasksView.vue?54a1", "webpack://cinema-bot-frontend/./src/views/TasksView.vue", "webpack://cinema-bot-frontend/./src/components/TaskCard.vue?b487", "webpack://cinema-bot-frontend/./src/components/TaskCard.vue", "webpack://cinema-bot-frontend/./src/store/tasks.ts", "webpack://cinema-bot-frontend/./src/components/TaskCard.vue?dd6a", "webpack://cinema-bot-frontend/./src/views/TasksView.vue?c273"], "sourcesContent": ["import { createElementVNode as _createElementVNode, withModifiers as _withModifiers, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, resolveComponent as _resolveComponent, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"tasks-view\" }\nconst _hoisted_2 = { class: \"container\" }\nconst _hoisted_3 = { class: \"page-description\" }\nconst _hoisted_4 = {\n  key: 0,\n  class: \"user-balance\"\n}\nconst _hoisted_5 = { class: \"balance-content\" }\nconst _hoisted_6 = { class: \"balance-value\" }\nconst _hoisted_7 = {\n  key: 1,\n  class: \"loading-container\"\n}\nconst _hoisted_8 = {\n  key: 2,\n  class: \"error-container\"\n}\nconst _hoisted_9 = {\n  key: 3,\n  class: \"empty-container\"\n}\nconst _hoisted_10 = {\n  key: 4,\n  class: \"tasks-list\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_TaskCard = _resolveComponent(\"TaskCard\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[6] || (_cache[6] = _createElementVNode(\"h1\", { class: \"page-title\" }, \"Задания\", -1)),\n      _createElementVNode(\"p\", _hoisted_3, [\n        _cache[2] || (_cache[2] = _createTextVNode(\"Выполняйте задания и получайте \")),\n        _createElementVNode(\"a\", {\n          href: \"#\",\n          class: \"cefi-link\",\n          onClick: _cache[0] || (_cache[0] = _withModifiers(() => {}, [\"prevent\"]))\n        }, \"$CEFIcoin\")\n      ]),\n      (_ctx.user)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n            _createElementVNode(\"div\", _hoisted_5, [\n              _cache[3] || (_cache[3] = _createElementVNode(\"span\", { class: \"balance-label\" }, \"Ваши $CEFIcoin:\", -1)),\n              _createElementVNode(\"span\", _hoisted_6, _toDisplayString(_ctx.user.task_points), 1)\n            ])\n          ]))\n        : _createCommentVNode(\"\", true),\n      (_ctx.loading)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, _cache[4] || (_cache[4] = [\n            _createElementVNode(\"div\", { class: \"loading-spinner large\" }, null, -1),\n            _createElementVNode(\"p\", null, \"Загрузка заданий...\", -1)\n          ])))\n        : (_ctx.error)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n              _createElementVNode(\"p\", null, _toDisplayString(_ctx.error), 1),\n              _createElementVNode(\"button\", {\n                onClick: _cache[1] || (_cache[1] = \n//@ts-ignore\n(...args) => (_ctx.fetchTasks && _ctx.fetchTasks(...args))),\n                class: \"retry-button\"\n              }, \"Повторить\")\n            ]))\n          : (_ctx.tasks.length === 0)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, _cache[5] || (_cache[5] = [\n                _createElementVNode(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  \"stroke-width\": \"2\",\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  class: \"empty-icon\"\n                }, [\n                  _createElementVNode(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\"\n                  }),\n                  _createElementVNode(\"line\", {\n                    x1: \"12\",\n                    y1: \"8\",\n                    x2: \"12\",\n                    y2: \"12\"\n                  }),\n                  _createElementVNode(\"line\", {\n                    x1: \"12\",\n                    y1: \"16\",\n                    x2: \"12.01\",\n                    y2: \"16\"\n                  })\n                ], -1),\n                _createElementVNode(\"p\", null, \"Нет доступных заданий\", -1)\n              ])))\n            : (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n                (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.tasks, (task) => {\n                  return (_openBlock(), _createBlock(_component_TaskCard, {\n                    key: task._id,\n                    task: task\n                  }, null, 8, [\"task\"]))\n                }), 128))\n              ]))\n    ])\n  ]))\n}", "<template>\n  <div class=\"tasks-view\">\n    <div class=\"container\">\n      <h1 class=\"page-title\">Задания</h1>\n      <p class=\"page-description\">Выполняйте задания и получайте <a href=\"#\" class=\"cefi-link\" @click.prevent>$CEFIcoin</a></p>\n\n      <!-- Блок с балансом пользователя -->\n      <div v-if=\"user\" class=\"user-balance\">\n        <div class=\"balance-content\">\n          <span class=\"balance-label\">Ваши $CEFIcoin:</span>\n          <span class=\"balance-value\">{{ user.task_points }}</span>\n        </div>\n      </div>\n\n      <div v-if=\"loading\" class=\"loading-container\">\n        <div class=\"loading-spinner large\"></div>\n        <p>Загрузка заданий...</p>\n      </div>\n\n      <div v-else-if=\"error\" class=\"error-container\">\n        <p>{{ error }}</p>\n        <button @click=\"fetchTasks\" class=\"retry-button\">Повторить</button>\n      </div>\n\n      <div v-else-if=\"tasks.length === 0\" class=\"empty-container\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"empty-icon\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n          <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n          <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\n        </svg>\n        <p>Нет доступных заданий</p>\n      </div>\n\n      <div v-else class=\"tasks-list\">\n        <TaskCard v-for=\"task in tasks\" :key=\"task._id\" :task=\"task\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, onMounted, computed } from 'vue';\nimport TaskCard from '@/components/TaskCard.vue';\nimport { useTasksStore } from '@/store/tasks';\nimport { useUserStore } from '@/store/user';\n\nexport default defineComponent({\n  name: 'TasksView',\n  components: {\n    TaskCard\n  },\n  setup() {\n    const tasksStore = useTasksStore();\n    const userStore = useUserStore();\n\n    // Computed properties\n    const tasks = computed(() => tasksStore.tasks);\n    const loading = computed(() => tasksStore.loading);\n    const error = computed(() => tasksStore.error);\n    const user = computed(() => userStore.user);\n\n    // Fetch tasks on mount\n    const fetchTasks = async () => {\n      await tasksStore.fetchTasks();\n    };\n\n    onMounted(() => {\n      fetchTasks();\n    });\n\n    return {\n      tasks,\n      loading,\n      error,\n      user,\n      fetchTasks\n    };\n  }\n});\n</script>\n\n<style scoped>\n.tasks-view {\n  padding-bottom: 80px;\n}\n\n.page-title {\n  font-size: var(--font-size-xl);\n  font-weight: bold;\n  margin-bottom: var(--spacing-xs);\n  text-align: center;\n}\n\n.page-description {\n  color: var(--tg-theme-hint-color);\n  text-align: center;\n  margin-bottom: var(--spacing-lg);\n}\n\n.cefi-link {\n  color: var(--tg-theme-link-color, #2481cc);\n  text-decoration: none;\n  cursor: pointer;\n}\n\n.cefi-link:hover {\n  text-decoration: underline;\n}\n\n.user-balance {\n  background-color: var(--tg-theme-secondary-bg-color, rgba(0, 0, 0, 0.05));\n  border-radius: 12px;\n  padding: var(--spacing-md);\n  margin-bottom: var(--spacing-lg);\n  border: 1px solid var(--tg-theme-section-separator-color, rgba(0, 0, 0, 0.1));\n}\n\n.balance-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.balance-label {\n  font-size: 16px;\n  color: var(--tg-theme-text-color);\n  font-weight: 500;\n}\n\n.balance-value {\n  font-size: 20px;\n  font-weight: bold;\n  color: var(--tg-theme-button-color);\n}\n\n.loading-container, .error-container, .empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-xl) 0;\n  color: var(--tg-theme-hint-color);\n}\n\n.loading-spinner {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  border: 2px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top-color: var(--tg-theme-button-color);\n  animation: spin 1s ease-in-out infinite;\n  margin-bottom: var(--spacing-md);\n}\n\n.loading-spinner.large {\n  width: 48px;\n  height: 48px;\n  border-width: 3px;\n}\n\n.empty-icon {\n  width: 48px;\n  height: 48px;\n  color: var(--tg-theme-hint-color);\n  margin-bottom: var(--spacing-md);\n}\n\n.retry-button {\n  margin-top: var(--spacing-md);\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n</style>\n", "import { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass } from \"vue\"\n\nconst _hoisted_1 = { class: \"task-logo\" }\nconst _hoisted_2 = [\"src\", \"alt\"]\nconst _hoisted_3 = {\n  key: 1,\n  class: \"logo-placeholder\"\n}\nconst _hoisted_4 = { class: \"task-info\" }\nconst _hoisted_5 = { class: \"task-title\" }\nconst _hoisted_6 = { class: \"task-points\" }\nconst _hoisted_7 = { class: \"task-action\" }\nconst _hoisted_8 = {\n  key: 0,\n  class: \"loading-spinner\"\n}\nconst _hoisted_9 = {\n  key: 1,\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  \"stroke-width\": \"2\",\n  \"stroke-linecap\": \"round\",\n  \"stroke-linejoin\": \"round\",\n  class: \"chevron-icon\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"task-card\", { loading: _ctx.loading }]),\n    onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.openTask && _ctx.openTask(...args)))\n  }, [\n    _createElementVNode(\"div\", _hoisted_1, [\n      (_ctx.task.logo_url)\n        ? (_openBlock(), _createElementBlock(\"img\", {\n            key: 0,\n            src: _ctx.task.logo_url,\n            alt: _ctx.task.title\n          }, null, 8, _hoisted_2))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[1] || (_cache[1] = [\n            _createElementVNode(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              \"stroke-width\": \"2\",\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\"\n            }, [\n              _createElementVNode(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\"\n              }),\n              _createElementVNode(\"line\", {\n                x1: \"12\",\n                y1: \"8\",\n                x2: \"12\",\n                y2: \"16\"\n              }),\n              _createElementVNode(\"line\", {\n                x1: \"8\",\n                y1: \"12\",\n                x2: \"16\",\n                y2: \"12\"\n              })\n            ], -1)\n          ])))\n    ]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createElementVNode(\"h3\", _hoisted_5, _toDisplayString(_ctx.task.title), 1),\n      _createElementVNode(\"div\", _hoisted_6, _toDisplayString(_ctx.task.points) + \" $CEFIcoin\", 1)\n    ]),\n    _createElementVNode(\"div\", _hoisted_7, [\n      (_ctx.loading)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8))\n        : (_openBlock(), _createElementBlock(\"svg\", _hoisted_9, _cache[2] || (_cache[2] = [\n            _createElementVNode(\"polyline\", { points: \"9 18 15 12 9 6\" }, null, -1)\n          ])))\n    ])\n  ], 2))\n}", "<template>\n  <div class=\"task-card\" @click=\"openTask\" :class=\"{ loading: loading }\">\n    <div class=\"task-logo\">\n      <img v-if=\"task.logo_url\" :src=\"task.logo_url\" :alt=\"task.title\">\n      <div v-else class=\"logo-placeholder\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n          <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"16\"></line>\n          <line x1=\"8\" y1=\"12\" x2=\"16\" y2=\"12\"></line>\n        </svg>\n      </div>\n    </div>\n    <div class=\"task-info\">\n      <h3 class=\"task-title\">{{ task.title }}</h3>\n      <div class=\"task-points\">{{ task.points }} $CEFIcoin</div>\n    </div>\n    <div class=\"task-action\">\n      <div v-if=\"loading\" class=\"loading-spinner\"></div>\n      <svg v-else xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"chevron-icon\">\n        <polyline points=\"9 18 15 12 9 6\"></polyline>\n      </svg>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref } from 'vue';\nimport { useTasksStore } from '@/store/tasks';\n\nexport default defineComponent({\n  name: 'TaskCard',\n  props: {\n    task: {\n      type: Object,\n      required: true\n    }\n  },\n  setup(props) {\n    const tasksStore = useTasksStore();\n    const loading = ref(false);\n\n    // Open task link and mark as completed\n    const openTask = async () => {\n      loading.value = true;\n\n      try {\n        // Open the link in Telegram browser\n        tasksStore.openTaskLink(props.task.link);\n\n        // Mark task as completed\n        await tasksStore.completeTask(props.task._id);\n      } catch (error) {\n        console.error('Error completing task:', error);\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    return {\n      openTask,\n      loading\n    };\n  }\n});\n</script>\n\n<style scoped>\n.task-card {\n  display: flex;\n  align-items: center;\n  background-color: var(--tg-theme-secondary-bg-color);\n  border-radius: var(--border-radius);\n  padding: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n  box-shadow: var(--shadow-sm);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  user-select: none;\n}\n\n.task-card:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.task-card:active {\n  transform: translateY(0);\n  box-shadow: var(--shadow-sm);\n}\n\n.task-card.loading {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.task-logo {\n  width: 48px;\n  height: 48px;\n  min-width: 48px;\n  border-radius: 50%;\n  overflow: hidden;\n  background-color: var(--tg-theme-bg-color);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.task-logo img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.logo-placeholder {\n  width: 24px;\n  height: 24px;\n  color: var(--tg-theme-hint-color);\n}\n\n.task-info {\n  flex: 1;\n  margin: 0 var(--spacing-md);\n  min-width: 0; /* Allows text to truncate */\n}\n\n.task-title {\n  font-size: 18px;\n  margin: 0 0 6px 0;\n  font-weight: 600;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  line-height: 1.3;\n}\n\n.task-points {\n  font-size: 15px;\n  color: var(--tg-theme-hint-color);\n  font-weight: 500;\n}\n\n.task-action {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  min-width: 24px;\n}\n\n.chevron-icon {\n  width: 20px;\n  height: 20px;\n  color: var(--tg-theme-hint-color);\n  transition: transform 0.2s ease;\n}\n\n.task-card:hover .chevron-icon {\n  transform: translateX(2px);\n  color: var(--tg-theme-button-color);\n}\n\n.loading-spinner {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  border: 2px solid var(--tg-theme-hint-color);\n  border-radius: 50%;\n  border-top-color: var(--tg-theme-button-color);\n  animation: spin 1s ease-in-out infinite;\n}\n\n\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n</style>\n", "import { defineStore } from 'pinia';\nimport api from '@/services/api';\n\ninterface Task {\n  _id: string;\n  title: string;\n  logo_url: string;\n  link: string;\n  is_active: boolean;\n  points: number;\n  created_at: string;\n}\n\nexport const useTasksStore = defineStore('tasks', {\n  state: () => ({\n    tasks: [] as Task[],\n    loading: false,\n    error: null as string | null\n  }),\n\n  actions: {\n    async fetchTasks() {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.get('/tasks');\n\n        if (response.data.success) {\n          this.tasks = response.data.data;\n        } else {\n          this.error = response.data.message || 'Failed to load tasks';\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error fetching tasks:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async completeTask(task_id: string) {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.post('/tasks/complete', { task_id });\n\n        if (response.data.success) {\n          // Refresh tasks after completion\n          await this.fetchTasks();\n          return true;\n        } else {\n          this.error = response.data.message || 'Failed to complete task';\n          return false;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error completing task:', error);\n        return false;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    openTaskLink(link: string) {\n      // Check if we're in Telegram WebApp\n      const tg = (window as any).Telegram?.WebApp;\n      if (tg) {\n        // For t.me links, use openTelegramLink to stay within Telegram\n        if (link.includes('t.me/') || link.includes('telegram.me/')) {\n          if (tg.openTelegramLink) {\n            tg.openTelegramLink(link);\n          } else {\n            // Fallback for older versions\n            tg.openLink(link);\n          }\n        } else {\n          // For external links, use openLink to open in external browser\n          tg.openLink(link);\n        }\n      } else {\n        // Fallback for non-Telegram environments\n        window.open(link, '_blank');\n      }\n    }\n  }\n});\n", "import { render } from \"./TaskCard.vue?vue&type=template&id=58e4ad20&scoped=true&ts=true\"\nimport script from \"./TaskCard.vue?vue&type=script&lang=ts\"\nexport * from \"./TaskCard.vue?vue&type=script&lang=ts\"\n\nimport \"./TaskCard.vue?vue&type=style&index=0&id=58e4ad20&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-58e4ad20\"]])\n\nexport default __exports__", "import { render } from \"./TasksView.vue?vue&type=template&id=0c17373f&scoped=true&ts=true\"\nimport script from \"./TasksView.vue?vue&type=script&lang=ts\"\nexport * from \"./TasksView.vue?vue&type=script&lang=ts\"\n\nimport \"./TasksView.vue?vue&type=style&index=0&id=0c17373f&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0c17373f\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "key", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_TaskCard", "_resolveComponent", "_openBlock", "_createElementBlock", "_createElementVNode", "_createTextVNode", "href", "onClick", "_withModifiers", "user", "_toDisplayString", "task_points", "_createCommentVNode", "loading", "error", "args", "fetchTasks", "tasks", "length", "xmlns", "viewBox", "fill", "stroke", "cx", "cy", "r", "x1", "y1", "x2", "y2", "_Fragment", "_renderList", "task", "_createBlock", "_id", "_normalizeClass", "openTask", "logo_url", "src", "alt", "title", "points", "useTasksStore", "defineStore", "state", "actions", "this", "response", "api", "get", "data", "success", "message", "console", "completeTask", "task_id", "post", "openTaskLink", "link", "tg", "window", "Telegram", "WebApp", "includes", "openTelegramLink", "openLink", "open", "defineComponent", "name", "props", "type", "Object", "required", "setup", "tasksStore", "ref", "async", "value", "__exports__", "components", "TaskCard", "userStore", "useUserStore", "computed", "onMounted"], "sourceRoot": ""}