<template>
  <div class="movie-row" ref="rowRef">
    <h2 class="category-title">{{ title }}</h2>

    <div class="movies-container">
      <button v-if="canScrollLeft && showScrollButtons" class="scroll-button left" @click="scrollLeft">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="15 18 9 12 15 6"></polyline>
        </svg>
      </button>

      <div class="movies-scroll" ref="scrollContainer">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
        </div>

        <div v-else-if="error" class="error-container">
          <p>{{ error }}</p>
          <button @click="loadMovies" class="retry-button">Повторить</button>
        </div>

        <div v-else-if="movies.length === 0" class="empty-container">
          <p>Фильмы не найдены</p>
        </div>

        <div v-else class="movies-list">
          <MovieTile
            v-for="movie in movies"
            :key="movie.id"
            :movie="movie"
            :is-exclusive="isExclusive(movie)"
          />

          <!-- Кнопка "Следующая страница" -->
          <div v-if="hasMorePages" class="next-page-button-container">
            <button
              class="next-page-button"
              @click="loadNextPage"
              :disabled="loadingNextPage"
            >
              <div v-if="loadingNextPage" class="loading-spinner small"></div>
              <span v-else>Ещё</span>
            </button>
          </div>
        </div>
      </div>

      <button v-if="canScrollRight && showScrollButtons" class="scroll-button right" @click="scrollRight">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount, PropType } from 'vue';
import MovieTile from './MovieTile.vue';
import { Movie, MovieCategory } from '@/services/vibixService';
import { useMoviesStore } from '@/store/movies';
import { isMobilePlatform } from '@/utils/platform';


export default defineComponent({
  name: 'MovieRow',
  components: {
    MovieTile
  },
  props: {
    title: {
      type: String,
      required: true
    },
    category: {
      type: String as PropType<MovieCategory>,
      required: true
    }
  },
  setup(props) {
    const movies = ref<Movie[]>([]);
    const loading = ref(true);
    const error = ref<string | null>(null);
    const scrollContainer = ref<HTMLElement | null>(null);
    const canScrollLeft = ref(false);
    const canScrollRight = ref(true); // По умолчанию показываем правую стрелку
    const moviesStore = useMoviesStore();

    // Проверяем платформу и скрываем кнопки для мобильных устройств
    const showScrollButtons = ref(!isMobilePlatform());

    // Состояние для пагинации
    const currentPage = ref(1);
    const hasMorePages = ref(true);
    const loadingNextPage = ref(false);

    // Load movies for the category
    const loadMovies = async () => {
      loading.value = true;
      error.value = null;

      // Сбрасываем состояние пагинации
      currentPage.value = 1;
      hasMorePages.value = true;

      try {
        // Используем хранилище для получения фильмов (первая страница)
        const categoryMovies = await moviesStore.getMoviesByCategory(
          props.category as MovieCategory,
          currentPage.value
        );
        movies.value = categoryMovies;



        // Проверяем, есть ли еще страницы (предполагаем, что если получили меньше 20 фильмов, то больше нет)
        hasMorePages.value = categoryMovies.length >= 20;
      } catch (err) {
        error.value = 'Не удалось загрузить фильмы';
      } finally {
        loading.value = false;
        // Обновляем кнопки прокрутки с небольшой задержкой для корректного рендеринга
        setTimeout(() => {
          updateScrollButtons();
        }, 100);
      }
    };

    // Check if movie should be marked as exclusive
    const isExclusive = (movie: Movie) => {
      // For demo purposes, mark every 5th movie as exclusive
      return movie.id.length > 0 && parseInt(movie.id) % 5 === 0;
    };

    // Scroll functions
    const updateScrollButtons = () => {
      if (!scrollContainer.value) return;

      const { scrollLeft, scrollWidth, clientWidth } = scrollContainer.value;
      canScrollLeft.value = scrollLeft > 0;
      canScrollRight.value = scrollLeft < scrollWidth - clientWidth - 10; // 10px buffer

      // Если контент помещается в контейнер, скрываем правую стрелку
      if (scrollWidth <= clientWidth) {
        canScrollRight.value = false;
      }
    };

    const scrollLeft = () => {
      if (!scrollContainer.value) return;

      const scrollAmount = scrollContainer.value.clientWidth * 0.8;
      scrollContainer.value.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
    };

    const scrollRight = () => {
      if (!scrollContainer.value) return;

      const scrollAmount = scrollContainer.value.clientWidth * 0.8;
      scrollContainer.value.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    };

    // Watch for scroll events
    const handleScroll = () => {
      updateScrollButtons();
    };

    // Watch for window resize to update scroll buttons
    const handleResize = () => {
      updateScrollButtons();
    };

    // Use Intersection Observer to load movies only when the row becomes visible
    const rowRef = ref<HTMLElement | null>(null);
    const isVisible = ref(false);

    onMounted(() => {
      // Создаем Intersection Observer для ленивой загрузки
      const observer = new IntersectionObserver((entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && !isVisible.value) {
          isVisible.value = true;
          loadMovies();

          // После загрузки отключаем observer
          if (rowRef.value) {
            observer.unobserve(rowRef.value);
          }
        }
      }, {
        rootMargin: '200px', // Load a bit before it becomes visible
        threshold: 0.1
      });

      // Observe the row element
      if (rowRef.value) {
        observer.observe(rowRef.value);
      }

      // Set up scroll event listener
      if (scrollContainer.value) {
        scrollContainer.value.addEventListener('scroll', handleScroll);
        // Обновляем кнопки прокрутки с задержкой для корректного рендеринга
        setTimeout(() => {
          updateScrollButtons();
        }, 200);
      }

      // Set up window resize listener
      window.addEventListener('resize', handleResize);

      // Clean up observer on unmount
      onBeforeUnmount(() => {
        if (rowRef.value) {
          observer.unobserve(rowRef.value);
        }
      });
    });

    // Загрузка следующей страницы фильмов
    const loadNextPage = async () => {
      if (!hasMorePages.value || loadingNextPage.value) return;

      loadingNextPage.value = true;

      try {
        // Увеличиваем номер страницы
        currentPage.value++;

        // Загружаем следующую страницу
        const nextPageMovies = await moviesStore.getMoviesByCategory(
          props.category as MovieCategory,
          currentPage.value,
          20,
          false // Параметр, указывающий не использовать кэш
        );

        // Добавляем новые фильмы к существующим
        if (nextPageMovies.length > 0) {
          movies.value = [...movies.value, ...nextPageMovies];
        }

        // Проверяем, есть ли еще страницы
        hasMorePages.value = nextPageMovies.length >= 20;
      } catch (err) {
        // В случае ошибки возвращаем номер страницы назад
        currentPage.value--;
      } finally {
        loadingNextPage.value = false;
        // Обновляем состояние кнопок прокрутки с небольшой задержкой
        setTimeout(() => {
          updateScrollButtons();
        }, 100);
      }
    };

    // Clean up event listeners
    onBeforeUnmount(() => {
      if (scrollContainer.value) {
        scrollContainer.value.removeEventListener('scroll', handleScroll);
      }
      window.removeEventListener('resize', handleResize);
    });

    return {
      movies,
      loading,
      error,
      scrollContainer,
      rowRef,
      isVisible,
      canScrollLeft,
      canScrollRight,
      showScrollButtons,
      loadMovies,
      isExclusive,
      scrollLeft,
      scrollRight,
      loadNextPage,
      hasMorePages,
      loadingNextPage,
      currentPage
    };
  }
});
</script>

<style scoped>
.movie-row {
  margin-bottom: 30px;
}

.category-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
  padding-left: 16px;
}

.movies-container {
  position: relative;
  display: flex;
  align-items: center;
}

.movies-scroll {
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  padding: 0 16px;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* Плавная прокрутка на iOS */
}

.movies-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.movies-list {
  display: inline-flex;
  padding: 4px 0;
}

.scroll-button {
  position: absolute;
  z-index: 2;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--tg-theme-button-color);
  color: var(--tg-theme-button-text-color);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
}

.scroll-button.left {
  left: 8px;
}

.scroll-button.right {
  right: 8px;
}

.scroll-button svg {
  width: 20px;
  height: 20px;
}

/* Временно отключаем CSS медиа-запросы для отладки */
/*
@media (max-width: 768px) {
  .scroll-button {
    display: none !important;
  }
}

@media (pointer: coarse) {
  .scroll-button {
    display: none !important;
  }
}
*/

.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  width: 100%;
  color: var(--tg-theme-hint-color);
}

.loading-spinner {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--tg-theme-button-color);
  animation: spin 1s ease-in-out infinite;
}

.retry-button {
  margin-top: 10px;
  background-color: var(--tg-theme-button-color);
  color: var(--tg-theme-button-text-color);
  border: none;
  border-radius: var(--border-radius);
  padding: 8px 16px;
  cursor: pointer;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.next-page-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
  height: 200px;
  margin-right: 10px;
}

.next-page-button {
  width: 100px;
  height: 40px;
  background-color: var(--tg-theme-button-color);
  color: var(--tg-theme-button-text-color);
  border: none;
  border-radius: var(--border-radius, 8px);
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.next-page-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}
</style>
