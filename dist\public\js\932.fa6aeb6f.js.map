{"version": 3, "file": "js/932.fa6aeb6f.js", "mappings": "kLAEA,MAAMA,EAAa,CCDZC,MAAM,cDEPC,EAAa,CCDVD,MAAM,aDETE,EAAa,CCJnBC,IAAA,EAM0BH,MAAM,qBDE1BI,EAAa,CCRnBD,IAAA,EAW6BH,MAAM,mBDC7BK,EAAa,CCZnBF,IAAA,EAgB0CH,MAAM,mBDA1CM,EAAa,CChBnBH,IAAA,EAyBkBH,MAAM,cDLlBO,EAAa,CCpBnBJ,IAAA,EA6BuBH,MAAM,eDLvBQ,EAAa,CCMUR,MAAM,gBDJ7B,SAAUS,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAsBC,EAAAA,EAAAA,IAAkB,YAE9C,OAAQC,EAAAA,EAAAA,OC5BRC,EAAAA,EAAAA,IAgCM,MAhCNpB,EAgCM,EA/BJqB,EAAAA,EAAAA,IA8BM,MA9BNnB,EA8BM,CDDJU,EAAO,KAAOA,EAAO,IC5BrBS,EAAAA,EAAAA,IAAmC,MAA/BpB,MAAM,cAAa,WAAO,ID6B9BW,EAAO,KAAOA,EAAO,IC5BrBS,EAAAA,EAAAA,IAAoE,KAAjEpB,MAAM,oBAAmB,wCAAoC,IAErDU,EAAAW,UD4BNH,EAAAA,EAAAA,OC5BLC,EAAAA,EAAAA,IAGM,MAHNjB,EAGMS,EAAA,KAAAA,EAAA,KAFJS,EAAAA,EAAAA,IAAyC,OAApCpB,MAAM,yBAAuB,UAClCoB,EAAAA,EAAAA,IAA0B,SAAvB,uBAAmB,OAGRV,EAAAY,QD4BTJ,EAAAA,EAAAA,OC5BPC,EAAAA,EAAAA,IAGM,MAHNf,EAGM,EAFJgB,EAAAA,EAAAA,IAAkB,UAAAG,EAAAA,EAAAA,IAAZb,EAAAY,OAAK,IACXF,EAAAA,EAAAA,IAAmE,UAA1DI,QAAKb,EAAA,KAAAA,EAAA,GD+BtB,IAAIc,IC/BoBf,EAAAgB,YAAAhB,EAAAgB,cAAAD,IAAYzB,MAAM,gBAAe,gBAGlB,IAAjBU,EAAAiB,MAAMC,SDiCbV,EAAAA,EAAAA,OCjCTC,EAAAA,EAAAA,IAOM,MAPNd,EAOMM,EAAA,KAAAA,EAAA,KANJS,EAAAA,EAAAA,IAIM,OAJDS,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,QAAQhC,MAAM,cD0CzJ,ECzCToB,EAAAA,EAAAA,IAAwC,UAAhCa,GAAG,KAAKC,GAAG,KAAKC,EAAE,QAC1Bf,EAAAA,EAAAA,IAA4C,QAAtCgB,GAAG,KAAKC,GAAG,IAAIC,GAAG,KAAKC,GAAG,QAChCnB,EAAAA,EAAAA,IAAgD,QAA1CgB,GAAG,KAAKC,GAAG,KAAKC,GAAG,QAAQC,GAAG,SDyD1B,ICvDZnB,EAAAA,EAAAA,IAA4B,SAAzB,yBAAqB,SD0DjBF,EAAAA,EAAAA,OCvDTC,EAAAA,EAAAA,IAEM,MAFNb,EAEM,GDsDKY,EAAAA,EAAAA,KAAW,ICvDpBC,EAAAA,EAAAA,IAA+DqB,EAAAA,GAAA,MA1BvEC,EAAAA,EAAAA,IA0BiC/B,EAAAiB,OAARe,KDwDCxB,EAAAA,EAAAA,OCxDlByB,EAAAA,EAAAA,IAA+D3B,EAAA,CAA9Bb,IAAKuC,EAAKE,IAAMF,KAAMA,GD2D1C,KAAM,EAAG,CAAC,YACX,SCzDHhC,EAAAmC,OD4DN3B,EAAAA,EAAAA,OC5DLC,EAAAA,EAAAA,IAEM,MAFNZ,EAEM,EADJa,EAAAA,EAAAA,IAA2E,UD6DrET,EAAO,KAAOA,EAAO,IC3FnCmC,EAAAA,EAAAA,IA8BW,kBAAY1B,EAAAA,EAAAA,IAAwD,OAAxDZ,GAAwDe,EAAAA,EAAAA,IAA1Bb,EAAAmC,KAAKE,aAAW,SA9BrEC,EAAAA,EAAAA,IAAA,UDkGA,CEhGA,MAAMjD,EAAa,CCDZC,MAAM,aDEPC,EAAa,CCDVD,MAAM,aDETE,ECJN,cDKME,EAAa,CCLnBD,IAAA,EAIkBH,MAAM,oBDKlBK,EAAa,CCGVL,MAAM,aDFTM,EAAa,CCGTN,MAAM,cDFVO,EAAa,CCGRP,MAAM,eDFXQ,ECZN,aDaMyC,EAAa,CCbnB9C,IAAA,GDcM+C,EAAc,CCdpB/C,IAAA,EAkBmBH,MAAM,mBDCnB,SAAUS,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OAAQG,EAAAA,EAAAA,OCnBRC,EAAAA,EAAAA,IAmBM,MAnBNpB,EAmBM,EAlBJqB,EAAAA,EAAAA,IASM,MATNnB,EASM,CAROS,EAAAgC,KAAKS,WDoBXjC,EAAAA,EAAAA,OCpBLC,EAAAA,EAAAA,IAAiE,OAHvEhB,IAAA,EAGiCiD,IAAK1C,EAAAgC,KAAKS,SAAWE,IAAK3C,EAAAgC,KAAKY,ODwBnD,KAAM,EC3BnBpD,MD4BWgB,EAAAA,EAAAA,OCxBLC,EAAAA,EAAAA,IAMM,MANNf,EAMMO,EAAA,KAAAA,EAAA,KALJS,EAAAA,EAAAA,IAIM,OAJDS,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDgC/I,EC/BLZ,EAAAA,EAAAA,IAAwC,UAAhCa,GAAG,KAAKC,GAAG,KAAKC,EAAE,QAC1Bf,EAAAA,EAAAA,IAA4C,QAAtCgB,GAAG,KAAKC,GAAG,IAAIC,GAAG,KAAKC,GAAG,QAChCnB,EAAAA,EAAAA,IAA4C,QAAtCgB,GAAG,IAAIC,GAAG,KAAKC,GAAG,KAAKC,GAAG,SD+C1B,UC3CZnB,EAAAA,EAAAA,IAGM,MAHNf,EAGM,EAFJe,EAAAA,EAAAA,IAA4C,KAA5Cd,GAA4CiB,EAAAA,EAAAA,IAAlBb,EAAAgC,KAAKY,OAAK,IACpClC,EAAAA,EAAAA,IAAiE,MAAjEb,GAAiEgB,EAAAA,EAAAA,IAArCb,EAAAgC,KAAKa,QAAS,KAAChC,EAAAA,EAAAA,IAAGb,EAAA8C,YAAU,MAE1DpC,EAAAA,EAAAA,IAGS,UAHDpB,MAAM,cAAewB,QAAKb,EAAA,KAAAA,EAAA,GDkDtC,IAAIc,IClDoCf,EAAA+C,UAAA/C,EAAA+C,YAAAhC,IAAWiC,SAAUhD,EAAAW,SDoDtD,CCnDYX,EAAAW,UDsDRH,EAAAA,EAAAA,OCrDLC,EAAAA,EAAAA,IAA4C,OAA5C+B,MDoDKhC,EAAAA,EAAAA,OCrDLC,EAAAA,EAAAA,IAAsC,OAjB5C8B,EAiB4B,eDuDrB,ECxEPzC,ID0EA,C,+BE7DO,MAAMmD,GAAgBC,EAAAA,EAAAA,IAAY,QAAS,CAChDC,MAAOA,KAAA,CACLlC,MAAO,GACPN,SAAS,EACTC,MAAO,OAGTwC,QAAS,CACP,gBAAMpC,GACJqC,KAAK1C,SAAU,EACf0C,KAAKzC,MAAQ,KAEb,IACE,MAAM0C,QAAiBC,EAAAA,EAAIC,IAAI,UAE3BF,EAASG,KAAKC,QAChBL,KAAKpC,MAAQqC,EAASG,KAAKA,KAE3BJ,KAAKzC,MAAQ0C,EAASG,KAAKE,SAAW,sBAE1C,CAAE,MAAO/C,GACPyC,KAAKzC,MAAQA,EAAM0C,UAAUG,MAAME,SAAW/C,EAAM+C,SAAW,gBAC/DC,QAAQhD,MAAM,wBAAyBA,EACzC,CAAE,QACAyC,KAAK1C,SAAU,CACjB,CACF,EAEA,kBAAMkD,CAAaC,GACjBT,KAAK1C,SAAU,EACf0C,KAAKzC,MAAQ,KAEb,IACE,MAAM0C,QAAiBC,EAAAA,EAAIQ,KAAK,kBAAmB,CAAED,YAErD,OAAIR,EAASG,KAAKC,eAEVL,KAAKrC,cACJ,IAEPqC,KAAKzC,MAAQ0C,EAASG,KAAKE,SAAW,2BAC/B,EAEX,CAAE,MAAO/C,GAGP,OAFAyC,KAAKzC,MAAQA,EAAM0C,UAAUG,MAAME,SAAW/C,EAAM+C,SAAW,gBAC/DC,QAAQhD,MAAM,yBAA0BA,IACjC,CACT,CAAE,QACAyC,KAAK1C,SAAU,CACjB,CACF,EAEAqD,YAAAA,CAAaC,GAEPC,OAAOC,UAAUC,QAAQC,SAC3BH,OAAOC,SAASC,OAAOC,SAASJ,GAEhCC,OAAOI,KAAKL,EAAM,SAEtB,KD7CJ,GAAeM,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,WACNC,MAAO,CACLzC,KAAM,CACJ0C,KAAMC,OACNC,UAAU,IAGdC,KAAAA,CAAMJ,GACJ,MAAMK,EAAa7B,IACbtC,GAAUoE,EAAAA,EAAAA,KAAI,GAGdjC,GAAakC,EAAAA,EAAAA,KAAS,KAC1B,MAAMnC,EAAS4B,EAAMzC,KAAKa,OAC1B,OAAIA,EAAS,KAAO,GAAKA,EAAS,MAAQ,GACjC,OACE,CAAC,EAAG,EAAG,GAAGoC,SAASpC,EAAS,MAAQ,CAAC,GAAI,GAAI,IAAIoC,SAASpC,EAAS,KACrE,QAEA,QACT,IAIIE,EAAWmC,UACfvE,EAAQwE,OAAQ,EAEhB,IAEEL,EAAWd,aAAaS,EAAMzC,KAAKiC,YAG7Ba,EAAWjB,aAAaY,EAAMzC,KAAKE,IAC3C,CAAE,MAAOtB,GACPgD,QAAQhD,MAAM,yBAA0BA,EAC1C,CAAE,QACAD,EAAQwE,OAAQ,CAClB,GAGF,MAAO,CACLrC,aACAC,WACApC,UAEJ,I,aElEF,MAAMyE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,I,aJiCA,SAAeb,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,YACNa,WAAY,CACVC,SAAQA,GAEVT,KAAAA,GACE,MAAMC,EAAa7B,IACbsC,GAAYC,EAAAA,EAAAA,KAGZvE,GAAQ+D,EAAAA,EAAAA,KAAS,IAAMF,EAAW7D,QAClCN,GAAUqE,EAAAA,EAAAA,KAAS,IAAMF,EAAWnE,UACpCC,GAAQoE,EAAAA,EAAAA,KAAS,IAAMF,EAAWlE,QAClCuB,GAAO6C,EAAAA,EAAAA,KAAS,IAAMO,EAAUpD,OAGhCnB,EAAakE,gBACXJ,EAAW9D,YAAY,EAO/B,OAJAyE,EAAAA,EAAAA,KAAU,KACRzE,GAAY,IAGP,CACLC,QACAN,UACAC,QACAuB,OACAnB,aAEJ,IKlEI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASjB,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://cinema-bot-frontend/./src/views/TasksView.vue?4688", "webpack://cinema-bot-frontend/./src/views/TasksView.vue", "webpack://cinema-bot-frontend/./src/components/TaskCard.vue?419f", "webpack://cinema-bot-frontend/./src/components/TaskCard.vue", "webpack://cinema-bot-frontend/./src/store/tasks.ts", "webpack://cinema-bot-frontend/./src/components/TaskCard.vue?dd6a", "webpack://cinema-bot-frontend/./src/views/TasksView.vue?c273"], "sourcesContent": ["import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, resolveComponent as _resolveComponent, createBlock as _createBlock, createTextVNode as _createTextVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"tasks-view\" }\nconst _hoisted_2 = { class: \"container\" }\nconst _hoisted_3 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_4 = {\n  key: 1,\n  class: \"error-container\"\n}\nconst _hoisted_5 = {\n  key: 2,\n  class: \"empty-container\"\n}\nconst _hoisted_6 = {\n  key: 3,\n  class: \"tasks-list\"\n}\nconst _hoisted_7 = {\n  key: 4,\n  class: \"user-points\"\n}\nconst _hoisted_8 = { class: \"points-value\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_TaskCard = _resolveComponent(\"TaskCard\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[4] || (_cache[4] = _createElementVNode(\"h1\", { class: \"page-title\" }, \"Задания\", -1)),\n      _cache[5] || (_cache[5] = _createElementVNode(\"p\", { class: \"page-description\" }, \"Выполняйте задания и получайте баллы\", -1)),\n      (_ctx.loading)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[1] || (_cache[1] = [\n            _createElementVNode(\"div\", { class: \"loading-spinner large\" }, null, -1),\n            _createElementVNode(\"p\", null, \"Загрузка заданий...\", -1)\n          ])))\n        : (_ctx.error)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n              _createElementVNode(\"p\", null, _toDisplayString(_ctx.error), 1),\n              _createElementVNode(\"button\", {\n                onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.fetchTasks && _ctx.fetchTasks(...args))),\n                class: \"retry-button\"\n              }, \"Повторить\")\n            ]))\n          : (_ctx.tasks.length === 0)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _cache[2] || (_cache[2] = [\n                _createElementVNode(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  \"stroke-width\": \"2\",\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  class: \"empty-icon\"\n                }, [\n                  _createElementVNode(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\"\n                  }),\n                  _createElementVNode(\"line\", {\n                    x1: \"12\",\n                    y1: \"8\",\n                    x2: \"12\",\n                    y2: \"12\"\n                  }),\n                  _createElementVNode(\"line\", {\n                    x1: \"12\",\n                    y1: \"16\",\n                    x2: \"12.01\",\n                    y2: \"16\"\n                  })\n                ], -1),\n                _createElementVNode(\"p\", null, \"Нет доступных заданий\", -1)\n              ])))\n            : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n                (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.tasks, (task) => {\n                  return (_openBlock(), _createBlock(_component_TaskCard, {\n                    key: task._id,\n                    task: task\n                  }, null, 8, [\"task\"]))\n                }), 128))\n              ])),\n      (_ctx.user)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [\n            _createElementVNode(\"p\", null, [\n              _cache[3] || (_cache[3] = _createTextVNode(\"Ваши баллы: \")),\n              _createElementVNode(\"span\", _hoisted_8, _toDisplayString(_ctx.user.task_points), 1)\n            ])\n          ]))\n        : _createCommentVNode(\"\", true)\n    ])\n  ]))\n}", "<template>\n  <div class=\"tasks-view\">\n    <div class=\"container\">\n      <h1 class=\"page-title\">Задания</h1>\n      <p class=\"page-description\">Выполняйте задания и получайте баллы</p>\n      \n      <div v-if=\"loading\" class=\"loading-container\">\n        <div class=\"loading-spinner large\"></div>\n        <p>Загрузка заданий...</p>\n      </div>\n      \n      <div v-else-if=\"error\" class=\"error-container\">\n        <p>{{ error }}</p>\n        <button @click=\"fetchTasks\" class=\"retry-button\">Повторить</button>\n      </div>\n      \n      <div v-else-if=\"tasks.length === 0\" class=\"empty-container\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"empty-icon\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n          <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n          <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\n        </svg>\n        <p>Нет доступных заданий</p>\n      </div>\n      \n      <div v-else class=\"tasks-list\">\n        <TaskCard v-for=\"task in tasks\" :key=\"task._id\" :task=\"task\" />\n      </div>\n      \n      <div v-if=\"user\" class=\"user-points\">\n        <p>Ваши баллы: <span class=\"points-value\">{{ user.task_points }}</span></p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, onMounted, computed } from 'vue';\nimport TaskCard from '@/components/TaskCard.vue';\nimport { useTasksStore } from '@/store/tasks';\nimport { useUserStore } from '@/store/user';\n\nexport default defineComponent({\n  name: 'TasksView',\n  components: {\n    TaskCard\n  },\n  setup() {\n    const tasksStore = useTasksStore();\n    const userStore = useUserStore();\n    \n    // Computed properties\n    const tasks = computed(() => tasksStore.tasks);\n    const loading = computed(() => tasksStore.loading);\n    const error = computed(() => tasksStore.error);\n    const user = computed(() => userStore.user);\n    \n    // Fetch tasks on mount\n    const fetchTasks = async () => {\n      await tasksStore.fetchTasks();\n    };\n    \n    onMounted(() => {\n      fetchTasks();\n    });\n    \n    return {\n      tasks,\n      loading,\n      error,\n      user,\n      fetchTasks\n    };\n  }\n});\n</script>\n\n<style scoped>\n.tasks-view {\n  padding-bottom: 80px;\n}\n\n.page-title {\n  font-size: var(--font-size-xl);\n  font-weight: bold;\n  margin-bottom: var(--spacing-xs);\n  text-align: center;\n}\n\n.page-description {\n  color: var(--tg-theme-hint-color);\n  text-align: center;\n  margin-bottom: var(--spacing-lg);\n}\n\n.loading-container, .error-container, .empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-xl) 0;\n  color: var(--tg-theme-hint-color);\n}\n\n.loading-spinner {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  border: 2px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top-color: var(--tg-theme-button-color);\n  animation: spin 1s ease-in-out infinite;\n  margin-bottom: var(--spacing-md);\n}\n\n.loading-spinner.large {\n  width: 48px;\n  height: 48px;\n  border-width: 3px;\n}\n\n.empty-icon {\n  width: 48px;\n  height: 48px;\n  color: var(--tg-theme-hint-color);\n  margin-bottom: var(--spacing-md);\n}\n\n.retry-button {\n  margin-top: var(--spacing-md);\n}\n\n.user-points {\n  margin-top: var(--spacing-lg);\n  text-align: center;\n  font-size: var(--font-size-md);\n}\n\n.points-value {\n  font-weight: bold;\n  color: var(--tg-theme-button-color);\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n</style>\n", "import { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString } from \"vue\"\n\nconst _hoisted_1 = { class: \"task-card\" }\nconst _hoisted_2 = { class: \"task-logo\" }\nconst _hoisted_3 = [\"src\", \"alt\"]\nconst _hoisted_4 = {\n  key: 1,\n  class: \"logo-placeholder\"\n}\nconst _hoisted_5 = { class: \"task-info\" }\nconst _hoisted_6 = { class: \"task-title\" }\nconst _hoisted_7 = { class: \"task-points\" }\nconst _hoisted_8 = [\"disabled\"]\nconst _hoisted_9 = { key: 0 }\nconst _hoisted_10 = {\n  key: 1,\n  class: \"loading-spinner\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      (_ctx.task.logo_url)\n        ? (_openBlock(), _createElementBlock(\"img\", {\n            key: 0,\n            src: _ctx.task.logo_url,\n            alt: _ctx.task.title\n          }, null, 8, _hoisted_3))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_4, _cache[1] || (_cache[1] = [\n            _createElementVNode(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              \"stroke-width\": \"2\",\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\"\n            }, [\n              _createElementVNode(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\"\n              }),\n              _createElementVNode(\"line\", {\n                x1: \"12\",\n                y1: \"8\",\n                x2: \"12\",\n                y2: \"16\"\n              }),\n              _createElementVNode(\"line\", {\n                x1: \"8\",\n                y1: \"12\",\n                x2: \"16\",\n                y2: \"12\"\n              })\n            ], -1)\n          ])))\n    ]),\n    _createElementVNode(\"div\", _hoisted_5, [\n      _createElementVNode(\"h3\", _hoisted_6, _toDisplayString(_ctx.task.title), 1),\n      _createElementVNode(\"div\", _hoisted_7, _toDisplayString(_ctx.task.points) + \" \" + _toDisplayString(_ctx.pointsText), 1)\n    ]),\n    _createElementVNode(\"button\", {\n      class: \"task-button\",\n      onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.openTask && _ctx.openTask(...args))),\n      disabled: _ctx.loading\n    }, [\n      (!_ctx.loading)\n        ? (_openBlock(), _createElementBlock(\"span\", _hoisted_9, \"Выполнить\"))\n        : (_openBlock(), _createElementBlock(\"span\", _hoisted_10))\n    ], 8, _hoisted_8)\n  ]))\n}", "<template>\n  <div class=\"task-card\">\n    <div class=\"task-logo\">\n      <img v-if=\"task.logo_url\" :src=\"task.logo_url\" :alt=\"task.title\">\n      <div v-else class=\"logo-placeholder\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n          <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"16\"></line>\n          <line x1=\"8\" y1=\"12\" x2=\"16\" y2=\"12\"></line>\n        </svg>\n      </div>\n    </div>\n    <div class=\"task-info\">\n      <h3 class=\"task-title\">{{ task.title }}</h3>\n      <div class=\"task-points\">{{ task.points }} {{ pointsText }}</div>\n    </div>\n    <button class=\"task-button\" @click=\"openTask\" :disabled=\"loading\">\n      <span v-if=\"!loading\">Выполнить</span>\n      <span v-else class=\"loading-spinner\"></span>\n    </button>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, computed, ref } from 'vue';\nimport { useTasksStore } from '@/store/tasks';\n\nexport default defineComponent({\n  name: 'TaskCard',\n  props: {\n    task: {\n      type: Object,\n      required: true\n    }\n  },\n  setup(props) {\n    const tasksStore = useTasksStore();\n    const loading = ref(false);\n    \n    // Format points text based on count\n    const pointsText = computed(() => {\n      const points = props.task.points;\n      if (points % 10 === 1 && points % 100 !== 11) {\n        return 'балл';\n      } else if ([2, 3, 4].includes(points % 10) && ![12, 13, 14].includes(points % 100)) {\n        return 'балла';\n      } else {\n        return 'баллов';\n      }\n    });\n    \n    // Open task link and mark as completed\n    const openTask = async () => {\n      loading.value = true;\n      \n      try {\n        // Open the link in Telegram browser\n        tasksStore.openTaskLink(props.task.link);\n        \n        // Mark task as completed\n        await tasksStore.completeTask(props.task._id);\n      } catch (error) {\n        console.error('Error completing task:', error);\n      } finally {\n        loading.value = false;\n      }\n    };\n    \n    return {\n      pointsText,\n      openTask,\n      loading\n    };\n  }\n});\n</script>\n\n<style scoped>\n.task-card {\n  display: flex;\n  align-items: center;\n  background-color: var(--tg-theme-secondary-bg-color);\n  border-radius: var(--border-radius);\n  padding: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n  box-shadow: var(--shadow-sm);\n}\n\n.task-logo {\n  width: 48px;\n  height: 48px;\n  min-width: 48px;\n  border-radius: 50%;\n  overflow: hidden;\n  background-color: var(--tg-theme-bg-color);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.task-logo img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.logo-placeholder {\n  width: 24px;\n  height: 24px;\n  color: var(--tg-theme-hint-color);\n}\n\n.task-info {\n  flex: 1;\n  margin: 0 var(--spacing-md);\n}\n\n.task-title {\n  font-size: var(--font-size-md);\n  margin: 0 0 4px 0;\n}\n\n.task-points {\n  font-size: var(--font-size-sm);\n  color: var(--tg-theme-hint-color);\n}\n\n.task-button {\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  border: none;\n  border-radius: var(--border-radius);\n  padding: var(--spacing-sm) var(--spacing-md);\n  font-size: var(--font-size-sm);\n  cursor: pointer;\n  min-width: 100px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.task-button:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.loading-spinner {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top-color: white;\n  animation: spin 1s ease-in-out infinite;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n</style>\n", "import { defineStore } from 'pinia';\nimport api from '@/services/api';\n\ninterface Task {\n  _id: string;\n  title: string;\n  logo_url: string;\n  link: string;\n  is_active: boolean;\n  points: number;\n  created_at: string;\n}\n\nexport const useTasksStore = defineStore('tasks', {\n  state: () => ({\n    tasks: [] as Task[],\n    loading: false,\n    error: null as string | null\n  }),\n\n  actions: {\n    async fetchTasks() {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.get('/tasks');\n\n        if (response.data.success) {\n          this.tasks = response.data.data;\n        } else {\n          this.error = response.data.message || 'Failed to load tasks';\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error fetching tasks:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async completeTask(task_id: string) {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.post('/tasks/complete', { task_id });\n\n        if (response.data.success) {\n          // Refresh tasks after completion\n          await this.fetchTasks();\n          return true;\n        } else {\n          this.error = response.data.message || 'Failed to complete task';\n          return false;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error completing task:', error);\n        return false;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    openTaskLink(link: string) {\n      // Open link using Telegram Web App\n      if (window.Telegram?.WebApp?.openLink) {\n        window.Telegram.WebApp.openLink(link);\n      } else {\n        window.open(link, '_blank');\n      }\n    }\n  }\n});\n", "import { render } from \"./TaskCard.vue?vue&type=template&id=1af51492&scoped=true&ts=true\"\nimport script from \"./TaskCard.vue?vue&type=script&lang=ts\"\nexport * from \"./TaskCard.vue?vue&type=script&lang=ts\"\n\nimport \"./TaskCard.vue?vue&type=style&index=0&id=1af51492&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-1af51492\"]])\n\nexport default __exports__", "import { render } from \"./TasksView.vue?vue&type=template&id=51e058c4&scoped=true&ts=true\"\nimport script from \"./TasksView.vue?vue&type=script&lang=ts\"\nexport * from \"./TasksView.vue?vue&type=script&lang=ts\"\n\nimport \"./TasksView.vue?vue&type=style&index=0&id=51e058c4&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-51e058c4\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "key", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_TaskCard", "_resolveComponent", "_openBlock", "_createElementBlock", "_createElementVNode", "loading", "error", "_toDisplayString", "onClick", "args", "fetchTasks", "tasks", "length", "xmlns", "viewBox", "fill", "stroke", "cx", "cy", "r", "x1", "y1", "x2", "y2", "_Fragment", "_renderList", "task", "_createBlock", "_id", "user", "_createTextVNode", "task_points", "_createCommentVNode", "_hoisted_9", "_hoisted_10", "logo_url", "src", "alt", "title", "points", "pointsText", "openTask", "disabled", "useTasksStore", "defineStore", "state", "actions", "this", "response", "api", "get", "data", "success", "message", "console", "completeTask", "task_id", "post", "openTaskLink", "link", "window", "Telegram", "WebApp", "openLink", "open", "defineComponent", "name", "props", "type", "Object", "required", "setup", "tasksStore", "ref", "computed", "includes", "async", "value", "__exports__", "components", "TaskCard", "userStore", "useUserStore", "onMounted"], "sourceRoot": ""}