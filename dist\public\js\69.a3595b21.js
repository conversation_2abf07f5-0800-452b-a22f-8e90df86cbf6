"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[69],{69:(s,e,a)=>{a.r(e),a.d(e,{default:()=>U});var n=a(768),t=a(130),o=a(232);const r={class:"tasks-view"},l={class:"container"},i={class:"page-description"},c={key:0,class:"user-balance"},k={class:"balance-content"},d={class:"balance-value"},u={key:1,class:"loading-container"},p={key:2,class:"error-container"},g={key:3,class:"empty-container"},v={key:4,class:"tasks-list"};function h(s,e,a,h,y,w){const L=(0,n.g2)("TaskCard");return(0,n.uX)(),(0,n.CE)("div",r,[(0,n.Lk)("div",l,[e[6]||(e[6]=(0,n.Lk)("h1",{class:"page-title"},"Задания",-1)),(0,n.Lk)("p",i,[e[2]||(e[2]=(0,n.eW)("Выполняйте задания и получайте ")),(0,n.Lk)("a",{href:"#",class:"cefi-link",onClick:e[0]||(e[0]=(0,t.D$)((()=>{}),["prevent"]))},"$CEFIcoin")]),s.user?((0,n.uX)(),(0,n.CE)("div",c,[(0,n.Lk)("div",k,[e[3]||(e[3]=(0,n.Lk)("span",{class:"balance-label"},"Ваши $CEFIcoin:",-1)),(0,n.Lk)("span",d,(0,o.v_)(s.user.task_points),1)])])):(0,n.Q3)("",!0),s.loading?((0,n.uX)(),(0,n.CE)("div",u,e[4]||(e[4]=[(0,n.Lk)("div",{class:"loading-spinner large"},null,-1),(0,n.Lk)("p",null,"Загрузка заданий...",-1)]))):s.error?((0,n.uX)(),(0,n.CE)("div",p,[(0,n.Lk)("p",null,(0,o.v_)(s.error),1),(0,n.Lk)("button",{onClick:e[1]||(e[1]=(...e)=>s.fetchTasks&&s.fetchTasks(...e)),class:"retry-button"},"Повторить")])):0===s.tasks.length?((0,n.uX)(),(0,n.CE)("div",g,e[5]||(e[5]=[(0,n.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"empty-icon"},[(0,n.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,n.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),(0,n.Lk)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1),(0,n.Lk)("p",null,"Нет доступных заданий",-1)]))):((0,n.uX)(),(0,n.CE)("div",v,[((0,n.uX)(!0),(0,n.CE)(n.FK,null,(0,n.pI)(s.tasks,(s=>((0,n.uX)(),(0,n.Wv)(L,{key:s._id,task:s},null,8,["task"])))),128))]))])])}const y={class:"task-logo"},w=["src","alt"],L={key:1,class:"logo-placeholder"},m={class:"task-info"},C={class:"task-title"},f={class:"task-points"},E={class:"task-action"},T={key:0,class:"loading-spinner"},_={key:1,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"chevron-icon"};function x(s,e,a,t,r,l){return(0,n.uX)(),(0,n.CE)("div",{class:(0,o.C4)(["task-card",{loading:s.loading}]),onClick:e[0]||(e[0]=(...e)=>s.openTask&&s.openTask(...e))},[(0,n.Lk)("div",y,[s.task.logo_url?((0,n.uX)(),(0,n.CE)("img",{key:0,src:s.task.logo_url,alt:s.task.title},null,8,w)):((0,n.uX)(),(0,n.CE)("div",L,e[1]||(e[1]=[(0,n.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,n.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,n.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),(0,n.Lk)("line",{x1:"8",y1:"12",x2:"16",y2:"12"})],-1)])))]),(0,n.Lk)("div",m,[(0,n.Lk)("h3",C,(0,o.v_)(s.task.title),1),(0,n.Lk)("div",f,(0,o.v_)(s.task.points)+" $CEFIcoin",1)]),(0,n.Lk)("div",E,[s.loading?((0,n.uX)(),(0,n.CE)("div",T)):((0,n.uX)(),(0,n.CE)("svg",_,e[2]||(e[2]=[(0,n.Lk)("polyline",{points:"9 18 15 12 9 6"},null,-1)])))])],2)}var b=a(144),X=a(657),W=a(526);const F=(0,X.nY)("tasks",{state:()=>({tasks:[],loading:!1,error:null}),actions:{async fetchTasks(){this.loading=!0,this.error=null;try{const s=await W.A.get("/tasks");s.data.success?this.tasks=s.data.data:this.error=s.data.message||"Failed to load tasks"}catch(s){this.error=s.response?.data?.message||s.message||"Unknown error",console.error("Error fetching tasks:",s)}finally{this.loading=!1}},async completeTask(s){this.loading=!0,this.error=null;try{const e=await W.A.post("/tasks/complete",{task_id:s});return e.data.success?(await this.fetchTasks(),!0):(this.error=e.data.message||"Failed to complete task",!1)}catch(e){return this.error=e.response?.data?.message||e.message||"Unknown error",console.error("Error completing task:",e),!1}finally{this.loading=!1}},openTaskLink(s){const e=window.Telegram?.WebApp;e?(s.includes("t.me/")||s.includes("telegram.me/"))&&e.openTelegramLink?e.openTelegramLink(s):e.openLink(s):window.open(s,"_blank")}}}),I=(0,n.pM)({name:"TaskCard",props:{task:{type:Object,required:!0}},setup(s){const e=F(),a=(0,b.KR)(!1),n=async()=>{a.value=!0;try{e.openTaskLink(s.task.link),await e.completeTask(s.task._id)}catch(n){console.error("Error completing task:",n)}finally{a.value=!1}};return{openTask:n,loading:a}}});var A=a(241);const j=(0,A.A)(I,[["render",x],["__scopeId","data-v-58e4ad20"]]),$=j;var B=a(652);const K=(0,n.pM)({name:"TasksView",components:{TaskCard:$},setup(){const s=F(),e=(0,B.k)(),a=(0,n.EW)((()=>s.tasks)),t=(0,n.EW)((()=>s.loading)),o=(0,n.EW)((()=>s.error)),r=(0,n.EW)((()=>e.user)),l=async()=>{await s.fetchTasks()};return(0,n.sV)((()=>{l()})),{tasks:a,loading:t,error:o,user:r,fetchTasks:l}}}),M=(0,A.A)(K,[["render",h],["__scopeId","data-v-0c17373f"]]),U=M}}]);
//# sourceMappingURL=69.a3595b21.js.map