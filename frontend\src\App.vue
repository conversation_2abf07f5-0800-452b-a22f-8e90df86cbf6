<template>
  <div v-if="isTelegramWebApp" class="app-wrapper" :class="colorScheme">
    <div class="app-container">
      <div class="scrollable-content">
        <router-view />
      </div>
    </div>
    <BottomMenu />
  </div>
  <TelegramError v-else :bot-username="botUsername" />
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import BottomMenu from '@/components/BottomMenu.vue';
import TelegramError from '@/components/TelegramError.vue';
import { useUserStore } from '@/store/user';

// Global declaration уже есть в services/api.ts

export default defineComponent({
  name: 'App',
  components: {
    BottomMenu,
    TelegramError
  },
  setup() {
    const router = useRouter();

    // Проверка наличия Telegram WebApp с дополнительной валидацией
    const isTelegramWebApp = computed(() => {
      // Проверяем наличие объекта Telegram и WebApp
      if (!window.Telegram || !window.Telegram.WebApp) {
        return false;
      }

      // Проверяем наличие initData - это ключевой признак, что приложение запущено из Telegram
      if (!window.Telegram.WebApp.initData) {
        return false;
      }

      // Проверяем наличие методов, которые должны быть в Telegram WebApp
      if (typeof window.Telegram.WebApp.close !== 'function' ||
          typeof window.Telegram.WebApp.expand !== 'function') {
        return false;
      }

      return true;
    });

    // Имя бота для ссылки в сообщении об ошибке
    const botUsername = process.env.VUE_APP_TELEGRAM_BOT_USERNAME || 'your_bot';

    // Значения по умолчанию, если Telegram WebApp недоступен
    const colorScheme = ref('light');
    const userStore = useUserStore();

    // Функция для парсинга параметров запуска мини-приложения
    const parseStartParam = (initData: string): string | null => {
      try {
        const params = new URLSearchParams(initData);
        return params.get('start_param');
      } catch (error) {
        console.error('Error parsing start_param:', error);
        return null;
      }
    };

    // Функция для обработки параметров запуска и перенаправления
    const handleStartParam = (startParam: string) => {
      // Проверяем, является ли параметр ссылкой на фильм
      const movieMatch = startParam.match(/^see_([a-z]+)_(.+)$/);
      if (movieMatch) {
        const [, source, id] = movieMatch;

        // Проверяем, что source является валидным (kp или imdb)
        if (source === 'kp' || source === 'imdb') {
          console.log(`Redirecting to movie: ${source}/${id}`);
          router.push(`/movie/${source}/${id}`);
          return;
        }
      }

      // Если параметр не распознан, логируем это
      console.log('Unknown start_param:', startParam);
    };

    // Initialize Telegram Web App
    onMounted(() => {
      if (isTelegramWebApp.value && window.Telegram && window.Telegram.WebApp) {
        // Используем non-null assertion operator, так как мы уже проверили наличие WebApp
        const tg = window.Telegram!.WebApp!;

        // Update color scheme
        colorScheme.value = tg.colorScheme || 'light';

        // Enable closing confirmation
        tg.enableClosingConfirmation();

        // Expand WebApp to full screen
        tg.expand();

        // Set theme class
        document.documentElement.className = tg.colorScheme;

        // Проверяем параметры запуска мини-приложения
        const startParam = parseStartParam(tg.initData);
        if (startParam) {
          console.log('Found start_param:', startParam);
          // Обрабатываем параметр запуска после небольшой задержки,
          // чтобы дать время роутеру инициализироваться
          setTimeout(() => {
            handleStartParam(startParam);
          }, 100);
        }

        // Initialize user data
        userStore.initUser();

        // Listen for theme changes
        tg.onEvent('themeChanged', () => {
          colorScheme.value = tg.colorScheme;
          document.documentElement.className = tg.colorScheme;
        });

        // Вызываем expand() только один раз при запуске приложения
        // Это достаточно, так как мы используем CSS для предотвращения сворачивания
      } else {
        // Установка светлой темы по умолчанию, если не в Telegram
        document.documentElement.className = 'light';
      }
    });

    return {
      isTelegramWebApp,
      colorScheme,
      botUsername
    };
  }
});
</script>

<style>
/* Глобальные стили для предотвращения проблем с прокруткой в iOS */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overscroll-behavior: none; /* Предотвращает "отскок" при прокрутке */
  touch-action: pan-y; /* Разрешает только вертикальную прокрутку */
}

/* Основной контейнер приложения */
.app-wrapper {
  font-family: 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  width: 100%;
  position: relative;
}

/* Контейнер с фиксированным позиционированием */
.app-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 60px); /* Вычитаем высоту нижнего меню */
  overflow: hidden;
  z-index: 1;
}

/* Контейнер с прокруткой */
.scrollable-content {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch; /* Плавная прокрутка на iOS */
  overscroll-behavior: contain; /* Предотвращает "отскок" при прокрутке */
  padding-bottom: 20px; /* Дополнительный отступ внизу */
}

/* Темы */
.app-wrapper.dark {
  background-color: var(--tg-theme-bg-color, #212121);
  color: var(--tg-theme-text-color, #ffffff);
}

.app-wrapper.light {
  background-color: var(--tg-theme-bg-color, #ffffff);
  color: var(--tg-theme-text-color, #000000);
}
</style>
