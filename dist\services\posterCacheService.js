"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MoviePoster_1 = __importDefault(require("../models/MoviePoster"));
const tmdbService_1 = __importDefault(require("./tmdbService"));
class PosterCacheService {
    constructor() {
        this.backgroundQueue = [];
        this.isProcessingBackground = false;
        this.maxConcurrentPosters = 3; // Максимум 3 постера одновременно
        this.backgroundDelay = 1000; // 1 секунда между запусками фоновых задач
    }
    // Основная функция для обогащения списка фильмов (параллельная обработка)
    async enrichMoviesWithPosters(movies) {
        // Получаем все кешированные постеры одним запросом
        const vibixIds = movies.map(m => String(m.id)); // Приводим к строке
        const cachedPosters = await this.getPostersFromCache(vibixIds);
        const posterMap = new Map(cachedPosters.map(p => [p.vibix_id, p]));
        // Обрабатываем все фильмы параллельно
        const enrichedMovies = movies.map(movie => {
            const cachedPoster = posterMap.get(String(movie.id)); // Приводим к строке
            if (cachedPoster) {
                // Есть запись в кеше
                if (cachedPoster.cache_status === 'found') {
                    // Постер найден - используем его
                    return {
                        ...movie,
                        poster_urls: cachedPoster.poster_urls
                    };
                }
                if (cachedPoster.cache_status === 'pending') {
                    // Уже обрабатывается - НЕ запускаем повторно
                    console.log(`[POSTER_CACHE] Movie ${movie.id} is pending, using fallback`);
                    return {
                        ...movie,
                        poster_urls: this.generateFallbackUrls()
                    };
                }
                if (cachedPoster.cache_status === 'error' && cachedPoster.retry_count < 3) {
                    // Ошибка с возможностью повтора - запускаем повторное получение
                    console.log(`[POSTER_CACHE] Movie ${movie.id} has error (retry ${cachedPoster.retry_count}/3), retrying`);
                    this.fetchPosterInBackground(movie);
                    return {
                        ...movie,
                        poster_urls: this.generateFallbackUrls()
                    };
                }
                // not_found или error с превышенным retry_count - используем fallback
                console.log(`[POSTER_CACHE] Movie ${movie.id} status: ${cachedPoster.cache_status}, retry_count: ${cachedPoster.retry_count}, using fallback`);
                return {
                    ...movie,
                    poster_urls: this.generateFallbackUrls()
                };
            }
            // Нет записи в кеше - запускаем фоновое получение
            console.log(`[POSTER_CACHE] No cache entry for movie ${movie.id}, starting background fetch`);
            this.fetchPosterInBackground(movie);
            // Возвращаем фильм с fallback
            return {
                ...movie,
                poster_urls: this.generateFallbackUrls()
            };
        });
        return enrichedMovies;
    }
    // Получение нескольких постеров из кеша одним запросом (только действительные)
    async getPostersFromCache(vibixIds) {
        try {
            // Сначала попробуем простой запрос для отладки
            const allCached = await MoviePoster_1.default.find({
                vibix_id: { $in: vibixIds }
            });
            // Фильтруем вручную для лучшего контроля
            const validCached = allCached.filter(poster => {
                if (poster.cache_status === 'found') {
                    // Для found проверяем expires_at
                    if (!poster.expires_at || poster.expires_at > new Date()) {
                        return true;
                    }
                    else {
                        return false;
                    }
                }
                if (poster.cache_status === 'pending') {
                    return true;
                }
                if (['error', 'not_found'].includes(poster.cache_status)) {
                    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
                    if (poster.updatedAt && poster.updatedAt > oneHourAgo) {
                        return true;
                    }
                }
                return false;
            });
            return validCached;
        }
        catch (error) {
            console.error(`Error getting posters from cache: ${error.message}`);
            return [];
        }
    }
    // Простая задержка
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    // Добавление задачи в фоновую очередь
    async addToBackgroundQueue(movie) {
        this.backgroundQueue.push(async () => {
            await this.fetchFromTMDB(movie);
        });
        this.processBackgroundQueue();
    }
    // Обработка фоновой очереди с контролем нагрузки
    async processBackgroundQueue() {
        if (this.isProcessingBackground || this.backgroundQueue.length === 0) {
            return;
        }
        this.isProcessingBackground = true;
        while (this.backgroundQueue.length > 0) {
            // Ограничиваем количество одновременно обрабатываемых постеров
            const batch = this.backgroundQueue.splice(0, this.maxConcurrentPosters);
            console.log(`[POSTER_CACHE] Processing batch of ${batch.length} posters, ${this.backgroundQueue.length} remaining in queue`);
            // Запускаем batch параллельно, но с задержкой между батчами
            await Promise.all(batch.map(task => task()));
            // Задержка между батчами для соблюдения rate limits
            if (this.backgroundQueue.length > 0) {
                await this.sleep(this.backgroundDelay);
            }
        }
        this.isProcessingBackground = false;
    }
    // Фоновое получение постера (не блокирует ответ)
    async fetchPosterInBackground(movie) {
        try {
            // Проверяем, нужно ли обновлять постер
            const existing = await MoviePoster_1.default.findOne({ vibix_id: movie.id });
            // Пропускаем если:
            // 1. Постер найден и еще действителен
            // 2. Уже в процессе обработки (pending)
            // 3. Ошибка или not_found произошли недавно (менее часа назад)
            if (existing) {
                const now = new Date();
                const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
                if ((existing.cache_status === 'found' && existing.expires_at && existing.expires_at > now) ||
                    existing.cache_status === 'pending' ||
                    (['error', 'not_found'].includes(existing.cache_status) && existing.updatedAt && existing.updatedAt > oneHourAgo)) {
                    return; // Не нужно обновлять
                }
            }
            // Создаем/обновляем запись со статусом pending
            await MoviePoster_1.default.findOneAndUpdate({ vibix_id: movie.id }, {
                vibix_id: movie.id,
                imdb_id: movie.imdb_id,
                kp_id: movie.kp_id,
                cache_status: 'pending',
                retry_count: 0
            }, { upsert: true });
            // Добавляем в очередь
            await this.addToBackgroundQueue(movie);
        }
        catch (error) {
            console.error(`Error in background fetch for ${movie.id}: ${error.message}`);
        }
    }
    // Получение постера из TMDB и сохранение в кеш
    async fetchFromTMDB(movie) {
        const timeStr = new Date().toLocaleTimeString('ru-RU', { hour12: false });
        try {
            let tmdbMovie = null;
            const enableDetailedLogging = process.env.NODE_ENV !== 'production';
            // Базовый объект для обновления
            const baseUpdate = {
                vibix_id: String(movie.id), // Приводим к строке
                imdb_id: movie.imdb_id,
                kp_id: movie.kp_id,
                retry_count: 0
            };
            // Сначала пробуем поиск по IMDB ID
            if (movie.imdb_id) {
                if (enableDetailedLogging) {
                    console.log(`[POSTER_CACHE] Searching by IMDB ID: ${movie.imdb_id}`);
                }
                tmdbMovie = await tmdbService_1.default.findMovieByImdbId(movie.imdb_id, enableDetailedLogging);
            }
            // Если не нашли по IMDB ID, пробуем по названию
            if (!tmdbMovie && movie.name_original) {
                if (enableDetailedLogging) {
                    console.log(`[POSTER_CACHE] Searching by original title: "${movie.name_original}", year: ${movie.year}`);
                }
                tmdbMovie = await tmdbService_1.default.searchMulti(movie.name_original, enableDetailedLogging);
            }
            // Если все еще не нашли, пробуем по русскому названию
            if (!tmdbMovie && movie.name_rus) {
                if (enableDetailedLogging) {
                    console.log(`[POSTER_CACHE] Searching by Russian title: "${movie.name_rus}", year: ${movie.year}`);
                }
                tmdbMovie = await tmdbService_1.default.searchMulti(movie.name_rus, enableDetailedLogging);
            }
            if (tmdbMovie && tmdbMovie.poster_path) {
                // Нашли постер - сохраняем в кеш
                const posterUrls = tmdbService_1.default.buildPosterUrls(tmdbMovie.poster_path);
                await MoviePoster_1.default.findOneAndUpdate({ vibix_id: String(movie.id) }, {
                    ...baseUpdate,
                    tmdb_id: tmdbMovie.id,
                    tmdb_poster_path: tmdbMovie.poster_path,
                    poster_urls: posterUrls,
                    cache_status: 'found',
                    error_message: undefined,
                    expires_at: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000) // 60 дней
                }, { upsert: true });
                console.log(`[${timeStr}] [POSTER_CACHE] ✅ SAVED poster for movie ${movie.id} (${movie.name_rus || movie.name_original}) - TMDB ID: ${tmdbMovie.id}`);
            }
            else {
                // Не нашли постер - помечаем как not_found
                const errorMessage = tmdbMovie ? 'Movie found but no poster_path' : 'Movie not found in TMDB';
                await MoviePoster_1.default.findOneAndUpdate({ vibix_id: String(movie.id) }, {
                    ...baseUpdate,
                    cache_status: 'not_found',
                    error_message: errorMessage
                }, { upsert: true });
                console.log(`[${timeStr}] [POSTER_CACHE] ❌ NOT FOUND poster for movie ${movie.id} (${movie.name_rus || movie.name_original}) - ${errorMessage}`);
            }
        }
        catch (error) {
            console.error(`[${timeStr}] [POSTER_CACHE] 💥 ERROR fetching poster for movie ${movie.id} (${movie.name_rus || movie.name_original}): ${error.message}`);
            // Сохраняем ошибку в кеш
            await MoviePoster_1.default.findOneAndUpdate({ vibix_id: String(movie.id) }, {
                $inc: { retry_count: 1 },
                cache_status: 'error',
                error_message: error.message || 'Unknown error'
            }, { upsert: true });
        }
    }
    // Fallback URLs если нет TMDB постера - всегда возвращаем пустые строки
    generateFallbackUrls() {
        // Не используем некорректные URL из Vibix API
        // Возвращаем пустые строки, чтобы фронтенд мог показать placeholder
        return {
            w500: '',
            original: ''
        };
    }
    // Повторная попытка получения постера (получаем полную информацию о фильме)
    async retryPoster(poster) {
        if (poster.retry_count >= 3) {
            return; // Превышено количество попыток
        }
        // Получаем полную информацию о фильме из Vibix API
        try {
            // Для retry используем минимальную информацию, которая у нас есть
            const movie = {
                id: poster.vibix_id,
                name_rus: '', // Будет заполнено при необходимости
                name_original: '',
                iframe_url: '',
                type: '',
                year: 0,
                imdb_id: poster.imdb_id,
                kp_id: poster.kp_id
            };
            await this.fetchFromTMDB(movie);
        }
        catch (error) {
            console.error(`Error retrying poster for ${poster.vibix_id}: ${error.message}`);
        }
    }
    // Очистка просроченных записей из кеша
    async cleanupExpiredCache() {
        try {
            const now = new Date();
            const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
            const result = await MoviePoster_1.default.deleteMany({
                $or: [
                    // Удаляем просроченные успешные записи
                    {
                        cache_status: 'found',
                        expires_at: { $lt: now }
                    },
                    // Удаляем старые ошибки и not_found (старше 1 часа)
                    {
                        cache_status: { $in: ['error', 'not_found'] },
                        updatedAt: { $lt: oneHourAgo }
                    }
                ]
            });
            if (result.deletedCount > 0) {
                console.log(`[POSTER_CACHE] Cleaned up ${result.deletedCount} expired cache entries`);
            }
        }
        catch (error) {
            console.error(`Error cleaning up expired cache: ${error.message}`);
        }
    }
}
exports.default = new PosterCacheService();
