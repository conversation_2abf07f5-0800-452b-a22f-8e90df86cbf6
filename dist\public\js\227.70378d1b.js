"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[227],{227:(e,t,o)=>{o.r(t),o.d(t,{default:()=>Le});var r=o(768);const a={class:"catalog-view"},i={class:"container"},l={ref:"lazyLoadTrigger",class:"lazy-load-trigger"};function s(e,t,o,s,n,c){const u=(0,r.g2)("BannerCarousel"),d=(0,r.g2)("MovieRow"),v=(0,r.g2)("SearchBar");return(0,r.uX)(),(0,r.CE)("div",a,[(0,r.Lk)("div",i,[(0,r.bF)(u),((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.visibleCategories,(e=>((0,r.uX)(),(0,r.CE)("div",{key:e.id},[(0,r.bF)(d,{title:e.title,category:e.category},null,8,["title","category"])])))),128)),(0,r.Lk)("div",l,null,512),e.showAdditionalCategories?((0,r.uX)(!0),(0,r.CE)(r.FK,{key:0},(0,r.pI)(e.additionalCategories,(e=>((0,r.uX)(),(0,r.CE)("div",{key:e.id},[(0,r.bF)(d,{title:e.title,category:e.category},null,8,["title","category"])])))),128)):(0,r.Q3)("",!0)]),(0,r.bF)(v)])}var n=o(144),c=o(232),u=o(130);const d=["disabled"],v={key:0,class:"loading-spinner main"},g={key:1,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},k={key:2,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"};function h(e,t,o,a,i,l){const s=(0,r.g2)("ErrorMessage");return(0,r.uX)(),(0,r.CE)("div",null,[(0,r.Lk)("div",{class:(0,c.C4)(["search-container",{expanded:e.isExpanded}]),onClick:t[4]||(t[4]=(0,u.D$)((()=>{}),["stop"]))},[(0,r.Lk)("button",{class:"search-toggle",onClick:t[0]||(t[0]=(0,u.D$)(((...t)=>e.handleMainButtonClick&&e.handleMainButtonClick(...t)),["stop"])),disabled:e.loading},[e.loading?((0,r.uX)(),(0,r.CE)("div",v)):e.searchQuery.trim()?((0,r.uX)(),(0,r.CE)("svg",g,t[5]||(t[5]=[(0,r.Lk)("line",{x1:"5",y1:"12",x2:"19",y2:"12"},null,-1),(0,r.Lk)("polyline",{points:"12 5 19 12 12 19"},null,-1)]))):((0,r.uX)(),(0,r.CE)("svg",k,t[6]||(t[6]=[(0,r.Lk)("circle",{cx:"11",cy:"11",r:"8"},null,-1),(0,r.Lk)("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"},null,-1)])))],8,d),(0,r.Lk)("div",{class:"search-form",onClick:t[3]||(t[3]=(0,u.D$)((()=>{}),["stop"]))},[(0,r.bo)((0,r.Lk)("input",{ref:"searchInput",type:"text","onUpdate:modelValue":t[1]||(t[1]=t=>e.searchQuery=t),placeholder:"Ссылка на кинопоиск или imdb",onKeyup:t[2]||(t[2]=(0,u.jR)(((...t)=>e.search&&e.search(...t)),["enter"]))},null,544),[[u.Jo,e.searchQuery]])])],2),e.moviesStore.error&&e.isExpanded?((0,r.uX)(),(0,r.Wv)(s,{key:0,show:!0,message:e.moviesStore.error,inline:!0,"show-close":!0,onClose:e.clearError,class:"search-error"},null,8,["message","onClose"])):(0,r.Q3)("",!0)])}var m=o(387),p=o(874);const y={class:"error-content"},A={key:0,class:"error-icon"},w={class:"error-text"},b={key:0,class:"error-title"},C={class:"error-description"},f=["disabled"],E={key:0,class:"loading-spinner"},x={key:1};function B(e,t,o,a,i,l){return e.show?((0,r.uX)(),(0,r.CE)("div",{key:0,class:(0,c.C4)(["error-message",{"error-message--inline":e.inline}])},[(0,r.Lk)("div",y,[e.inline?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("div",A,t[2]||(t[2]=[(0,r.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,r.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,r.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),(0,r.Lk)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))),(0,r.Lk)("div",w,[e.title&&!e.inline?((0,r.uX)(),(0,r.CE)("h3",b,(0,c.v_)(e.title),1)):(0,r.Q3)("",!0),(0,r.Lk)("p",C,(0,c.v_)(e.message),1)]),e.showRetry?((0,r.uX)(),(0,r.CE)("button",{key:1,onClick:t[0]||(t[0]=t=>e.$emit("retry")),class:"retry-button",disabled:e.loading},[e.loading?((0,r.uX)(),(0,r.CE)("span",E)):((0,r.uX)(),(0,r.CE)("span",x,(0,c.v_)(e.retryText),1))],8,f)):(0,r.Q3)("",!0),e.showClose?((0,r.uX)(),(0,r.CE)("button",{key:2,onClick:t[1]||(t[1]=t=>e.$emit("close")),class:"close-button","aria-label":"Закрыть"},t[3]||(t[3]=[(0,r.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,r.Lk)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,r.Lk)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})],-1)]))):(0,r.Q3)("",!0)])],2)):(0,r.Q3)("",!0)}const L=(0,r.pM)({name:"ErrorMessage",props:{show:{type:Boolean,default:!0},message:{type:String,required:!0},title:{type:String,default:"Ошибка"},inline:{type:Boolean,default:!1},showRetry:{type:Boolean,default:!1},showClose:{type:Boolean,default:!1},retryText:{type:String,default:"Повторить"},loading:{type:Boolean,default:!1}},emits:["retry","close"]});var R=o(241);const _=(0,R.A)(L,[["render",B],["__scopeId","data-v-7b2fd338"]]),M=_,S=(0,r.pM)({name:"SearchBar",components:{ErrorMessage:M},setup(){const e=(0,m.rd)(),t=(0,p.s)(),o=(0,n.KR)(""),a=(0,n.KR)(!1),i=(0,n.KR)(!1),l=(0,n.KR)(null),s=()=>{t.error=null},c=()=>{a.value=!a.value,a.value&&l.value&&setTimeout((()=>{l.value?.focus()}),300)},u=()=>{o.value.trim()?d():c()},d=async()=>{if(o.value.trim()&&!i.value){i.value=!0,s();try{const r=await t.searchMovie(o.value);if(r){let t="kp",i=r.kp_id;!i&&r.imdb_id&&(t="imdb",i=r.imdb_id),t&&i&&(e.push(`/movie/${t}/${i}`),o.value="",a.value=!1,s())}}catch(r){console.error("Error searching movie:",r)}finally{i.value=!1}}},v=()=>{a.value&&(a.value=!1,o.value="",s())};return(0,r.sV)((()=>{document.addEventListener("click",v)})),(0,r.xo)((()=>{document.removeEventListener("click",v)})),{searchQuery:o,isExpanded:a,loading:i,searchInput:l,moviesStore:t,toggleSearch:c,handleMainButtonClick:u,search:d,clearError:s}}}),U=(0,R.A)(S,[["render",h],["__scopeId","data-v-43d0478e"]]),X=U,K={class:"banner-carousel"},I={class:"carousel-container",ref:"carouselContainer"},T=["onClick"],$={class:"slide-overlay"},Q={class:"slide-title"},F={class:"slide-description"},D={class:"carousel-indicators"},V=["onClick"];function N(e,t,o,a,i,l){return(0,r.uX)(),(0,r.CE)("div",K,[(0,r.Lk)("div",I,[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.slides,((t,o)=>((0,r.uX)(),(0,r.CE)("div",{key:o,class:(0,c.C4)(["carousel-slide",{active:e.currentSlide===o}]),style:(0,c.Tr)([{transform:`translateX(${100*(o-e.currentSlide)}%)`},{cursor:"pointer"}]),onClick:o=>e.handleSlideClick(t)},[(0,r.Lk)("div",{class:"slide-content",style:(0,c.Tr)({backgroundImage:`url(${t.imageUrl})`})},[(0,r.Lk)("div",$,[(0,r.Lk)("h2",Q,(0,c.v_)(t.title),1),(0,r.Lk)("p",F,(0,c.v_)(t.description),1)])],4)],14,T)))),128))],512),(0,r.Lk)("div",D,[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.slides,((t,o)=>((0,r.uX)(),(0,r.CE)("button",{key:o,class:(0,c.C4)(["indicator-dot",{active:e.currentSlide===o}]),onClick:t=>e.goToSlide(o)},null,10,V)))),128))]),(0,r.Lk)("button",{class:"carousel-control prev",onClick:t[0]||(t[0]=(...t)=>e.prevSlide&&e.prevSlide(...t))},t[2]||(t[2]=[(0,r.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,r.Lk)("polyline",{points:"15 18 9 12 15 6"})],-1)])),(0,r.Lk)("button",{class:"carousel-control next",onClick:t[1]||(t[1]=(...t)=>e.nextSlide&&e.nextSlide(...t))},t[3]||(t[3]=[(0,r.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,r.Lk)("polyline",{points:"9 18 15 12 9 6"})],-1)]))])}var W=o(699);const P=(0,r.pM)({name:"BannerCarousel",setup(){function e(e,t){const o=[],r=new Set;while(o.length<t&&r.size<e.length){const t=Math.floor(Math.random()*e.length);r.has(t)||(o.push(e[t]),r.add(t))}return o}const t=(0,n.KR)([]),o=(0,n.KR)(0),a=(0,n.KR)(null),i=(0,n.KR)(null),l=(0,n.KR)(!0),s=(0,n.KR)(null),c=(0,p.s)(),u=(0,m.rd)(),d=async()=>{l.value=!0,s.value=null;try{const o=await c.getMoviesByCategory(W.B.POPULAR,1,20),r=(o||[]).filter((e=>e.poster_urls?.original||e.poster_urls?.w500)),a=e(r,3),i=a.map((e=>({title:e.name_rus||e.name_original||"",description:e.description_short||`${"serial"===e.type?"Сериал":"Фильм"} ${e.year}`,imageUrl:e.poster_urls?.original||e.poster_urls?.w500||"",link:`/movie/${e.kp_id?"kp":"imdb"}/${e.kp_id||e.imdb_id}`}))),l={title:"Реклама",description:"Специальное предложение для наших пользователей",imageUrl:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",isAd:!0};i.length>=2?i.splice(1,0,l):i.push(l),t.value=i}catch(o){console.error("Error loading featured movies for carousel:",o),s.value="Не удалось загрузить фильмы для карусели",t.value=[{title:"Популярные фильмы",description:"Смотрите лучшие фильмы и сериалы",imageUrl:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkmM9QDwADgQF/Vry1FAAAAABJRU5ErkJggg=="},{title:"Новинки",description:"Самые свежие релизы",imageUrl:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkWM9QDwADCAGEFYi4zQAAAABJRU5ErkJggg=="},{title:"Реклама",description:"Специальное предложение для наших пользователей",imageUrl:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",isAd:!0},{title:"Эксклюзивы",description:"Только у нас",imageUrl:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPivDwADCQGAjPbM3QAAAABJRU5ErkJggg=="}]}finally{l.value=!1}},v=()=>{o.value=(o.value+1)%t.value.length,A()},g=()=>{o.value=(o.value-1+t.value.length)%t.value.length,A()},k=e=>{o.value=e,A()},h=()=>{y(),i.value=window.setInterval((()=>{v()}),5e3)},y=()=>{null!==i.value&&(clearInterval(i.value),i.value=null)},A=()=>{y(),h()};let w=0,b=0;const C=e=>{w=e.changedTouches[0].screenX},f=e=>{b=e.changedTouches[0].screenX,E()},E=()=>{const e=50;b<w-e?v():b>w+e&&g()},x=e=>{e.isAd||e.link&&u.push(e.link)};return(0,r.sV)((()=>{d(),h(),a.value&&(a.value.addEventListener("touchstart",C,{passive:!0}),a.value.addEventListener("touchend",f,{passive:!0}))})),(0,r.xo)((()=>{y(),a.value&&(a.value.removeEventListener("touchstart",C),a.value.removeEventListener("touchend",f))})),{slides:t,currentSlide:o,carouselContainer:a,nextSlide:v,prevSlide:g,goToSlide:k,handleSlideClick:x}}}),J=(0,R.A)(P,[["render",N],["__scopeId","data-v-1518b395"]]),j=J,O={class:"movie-row",ref:"rowRef"},G={class:"category-title"},q={class:"movies-container"},Y={class:"movies-scroll",ref:"scrollContainer"},z={key:0,class:"loading-container"},H={key:1,class:"error-container"},Z={key:2,class:"empty-container"},ee={key:3,class:"movies-list"},te={key:0,class:"next-page-button-container"},oe=["disabled"],re={key:0,class:"loading-spinner small"},ae={key:1};function ie(e,t,o,a,i,l){const s=(0,r.g2)("MovieTile");return(0,r.uX)(),(0,r.CE)("div",O,[(0,r.Lk)("h2",G,(0,c.v_)(e.title),1),(0,r.Lk)("div",q,[e.canScrollLeft&&e.showScrollButtons?((0,r.uX)(),(0,r.CE)("button",{key:0,class:"scroll-button left",onClick:t[0]||(t[0]=(...t)=>e.scrollLeft&&e.scrollLeft(...t))},t[4]||(t[4]=[(0,r.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,r.Lk)("polyline",{points:"15 18 9 12 15 6"})],-1)]))):(0,r.Q3)("",!0),(0,r.Lk)("div",Y,[e.loading?((0,r.uX)(),(0,r.CE)("div",z,t[5]||(t[5]=[(0,r.Lk)("div",{class:"loading-spinner"},null,-1)]))):e.error?((0,r.uX)(),(0,r.CE)("div",H,[(0,r.Lk)("p",null,(0,c.v_)(e.error),1),(0,r.Lk)("button",{onClick:t[1]||(t[1]=(...t)=>e.loadMovies&&e.loadMovies(...t)),class:"retry-button"},"Повторить")])):0===e.movies.length?((0,r.uX)(),(0,r.CE)("div",Z,t[6]||(t[6]=[(0,r.Lk)("p",null,"Фильмы не найдены",-1)]))):((0,r.uX)(),(0,r.CE)("div",ee,[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.movies,(t=>((0,r.uX)(),(0,r.Wv)(s,{key:t.id,movie:t,"is-exclusive":e.isExclusive(t)},null,8,["movie","is-exclusive"])))),128)),e.hasMorePages?((0,r.uX)(),(0,r.CE)("div",te,[(0,r.Lk)("button",{class:"next-page-button",onClick:t[2]||(t[2]=(...t)=>e.loadNextPage&&e.loadNextPage(...t)),disabled:e.loadingNextPage},[e.loadingNextPage?((0,r.uX)(),(0,r.CE)("div",re)):((0,r.uX)(),(0,r.CE)("span",ae,"Ещё"))],8,oe)])):(0,r.Q3)("",!0)]))],512),e.canScrollRight&&e.showScrollButtons?((0,r.uX)(),(0,r.CE)("button",{key:1,class:"scroll-button right",onClick:t[3]||(t[3]=(...t)=>e.scrollRight&&e.scrollRight(...t))},t[7]||(t[7]=[(0,r.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,r.Lk)("polyline",{points:"9 18 15 12 9 6"})],-1)]))):(0,r.Q3)("",!0)])],512)}const le={class:"movie-poster"},se=["src","alt"],ne={key:1,class:"poster-placeholder"},ce={key:2,class:"exclusive-badge"},ue={key:3,class:"movie-rating"},de={key:4,class:"movie-type"},ve={class:"movie-info"},ge={class:"movie-title"},ke={class:"movie-subtitle"},he=["fill"];function me(e,t,o,a,i,l){return(0,r.uX)(),(0,r.CE)("div",{class:"movie-tile",onClick:t[1]||(t[1]=(...t)=>e.navigateToMovie&&e.navigateToMovie(...t))},[(0,r.Lk)("div",le,[e.posterUrl?((0,r.uX)(),(0,r.CE)("img",{key:0,src:e.posterUrl,alt:e.movie.name_rus,class:"poster-image"},null,8,se)):((0,r.uX)(),(0,r.CE)("div",ne,t[2]||(t[2]=[(0,r.Fv)('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" data-v-c5191ef2><rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18" data-v-c5191ef2></rect><circle cx="12" cy="12" r="4" data-v-c5191ef2></circle><line x1="8" y1="4" x2="16" y2="4" data-v-c5191ef2></line><line x1="4" y1="8" x2="4" y2="16" data-v-c5191ef2></line><line x1="20" y1="8" x2="20" y2="16" data-v-c5191ef2></line><line x1="8" y1="20" x2="16" y2="20" data-v-c5191ef2></line></svg>',1)]))),e.isExclusive?((0,r.uX)(),(0,r.CE)("div",ce,"эксклюзив")):(0,r.Q3)("",!0),e.movie.kp_rating||e.movie.imdb_rating?((0,r.uX)(),(0,r.CE)("div",ue,(0,c.v_)(e.movie.kp_rating||e.movie.imdb_rating),1)):(0,r.Q3)("",!0),"serial"===e.movie.type?((0,r.uX)(),(0,r.CE)("div",de,"сериал")):(0,r.Q3)("",!0)]),(0,r.Lk)("div",ve,[(0,r.Lk)("h3",ge,(0,c.v_)(e.movie.name_rus||e.movie.name_original),1),(0,r.Lk)("p",ke,(0,c.v_)(e.getSubtitle),1)]),(0,r.Lk)("button",{class:"bookmark-button",onClick:t[0]||(t[0]=(0,u.D$)(((...t)=>e.toggleBookmark&&e.toggleBookmark(...t)),["stop"]))},[((0,r.uX)(),(0,r.CE)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:e.isBookmarked?"currentColor":"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},t[3]||(t[3]=[(0,r.Lk)("path",{d:"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"},null,-1)]),8,he))])])}var pe=o(938),ye=o(522);const Ae=(0,r.pM)({name:"MovieTile",props:{movie:{type:Object,required:!0},isExclusive:{type:Boolean,default:!1}},setup(e){const t=(0,m.rd)(),o=(0,pe.a)(),a=(0,n.KR)(!1),i=(0,r.EW)((()=>(0,ye.Ib)(e.movie))),l=(0,r.EW)((()=>(0,ye.GO)(e.movie))),s=(0,r.EW)((()=>!(!i.value||!l.value)&&o.isBookmarked(l.value,i.value))),c=(0,r.EW)((()=>e.movie.poster_urls?.w500?e.movie.poster_urls.w500:e.movie.poster_url||null)),u=(0,r.EW)((()=>e.movie.year?`${e.movie.year}`:"Бесплатно")),d=()=>{i.value&&l.value&&t.push(`/movie/${i.value}/${l.value}`)},v=async e=>{if(e.stopPropagation(),i.value&&l.value){a.value=!0;try{if(s.value){const e=o.getBookmarkId(l.value,i.value);e&&await o.deleteBookmark(e)}else await o.addBookmark(l.value,i.value)}catch(t){console.error("Error toggling bookmark:",t)}finally{a.value=!1}}};return{source:i,movieId:l,isBookmarked:s,posterUrl:c,getSubtitle:u,navigateToMovie:d,toggleBookmark:v,loading:a}}}),we=(0,R.A)(Ae,[["render",me],["__scopeId","data-v-c5191ef2"]]),be=we,Ce=(0,r.pM)({name:"MovieRow",components:{MovieTile:be},props:{title:{type:String,required:!0},category:{type:String,required:!0}},setup(e){const t=(0,n.KR)([]),o=(0,n.KR)(!0),a=(0,n.KR)(null),i=(0,n.KR)(null),l=(0,n.KR)(!1),s=(0,n.KR)(!0),c=(0,p.s)(),u=(0,n.KR)(!0),d=(0,n.KR)(1),v=(0,n.KR)(!0),g=(0,n.KR)(!1),k=async()=>{o.value=!0,a.value=null,d.value=1,v.value=!0;try{const o=await c.getMoviesByCategory(e.category,d.value);t.value=o,v.value=o.length>=20}catch(r){a.value="Не удалось загрузить фильмы"}finally{o.value=!1,setTimeout((()=>{m()}),100)}},h=e=>e.id.length>0&&parseInt(e.id)%5===0,m=()=>{if(!i.value)return;const{scrollLeft:e,scrollWidth:t,clientWidth:o}=i.value;l.value=e>0,s.value=e<t-o-10,t<=o&&(s.value=!1)},y=()=>{if(!i.value)return;const e=.8*i.value.clientWidth;i.value.scrollBy({left:-e,behavior:"smooth"})},A=()=>{if(!i.value)return;const e=.8*i.value.clientWidth;i.value.scrollBy({left:e,behavior:"smooth"})},w=()=>{m()},b=()=>{m()},C=(0,n.KR)(null),f=(0,n.KR)(!1);(0,r.sV)((()=>{const e=new IntersectionObserver((t=>{const o=t[0];o.isIntersecting&&!f.value&&(f.value=!0,k(),C.value&&e.unobserve(C.value))}),{rootMargin:"200px",threshold:.1});C.value&&e.observe(C.value),i.value&&(i.value.addEventListener("scroll",w),setTimeout((()=>{m()}),200)),window.addEventListener("resize",b),(0,r.xo)((()=>{C.value&&e.unobserve(C.value)}))}));const E=async()=>{if(v.value&&!g.value){g.value=!0;try{d.value++;const o=await c.getMoviesByCategory(e.category,d.value,20,!1);o.length>0&&(t.value=[...t.value,...o]),v.value=o.length>=20}catch(o){d.value--}finally{g.value=!1,setTimeout((()=>{m()}),100)}}};return(0,r.xo)((()=>{i.value&&i.value.removeEventListener("scroll",w),window.removeEventListener("resize",b)})),{movies:t,loading:o,error:a,scrollContainer:i,rowRef:C,isVisible:f,canScrollLeft:l,canScrollRight:s,showScrollButtons:u,loadMovies:k,isExclusive:h,scrollLeft:y,scrollRight:A,loadNextPage:E,hasMorePages:v,loadingNextPage:g,currentPage:d}}}),fe=(0,R.A)(Ce,[["render",ie],["__scopeId","data-v-2848cd6b"]]),Ee=fe,xe=(0,r.pM)({name:"CatalogView",components:{SearchBar:X,BannerCarousel:j,MovieRow:Ee},setup(){const e=(0,pe.a)(),t=(0,n.KR)(null);let o=null;const a=[{id:1,title:"Популярные",category:W.B.POPULAR},{id:2,title:"Новинки",category:W.B.NEW},{id:3,title:"Триллеры",category:W.B.THRILLER},{id:4,title:"Драмы",category:W.B.DRAMA},{id:5,title:"Боевики",category:W.B.ACTION},{id:6,title:"Фантастика",category:W.B.SCIFI},{id:7,title:"Детективы",category:W.B.DETECTIVE}],i=3,l=(0,n.KR)(a.slice(0,i)),s=(0,n.KR)(a.slice(i)),c=(0,n.KR)(!1),u=()=>{c.value=!0,o&&(o.disconnect(),o=null)};return(0,r.sV)((async()=>{await e.fetchBookmarks(),o=new IntersectionObserver((e=>{const t=e[0];t.isIntersecting&&u()}),{rootMargin:"200px",threshold:.1}),t.value&&o.observe(t.value)})),(0,r.xo)((()=>{o&&(o.disconnect(),o=null)})),{visibleCategories:l,additionalCategories:s,showAdditionalCategories:c,lazyLoadTrigger:t}}}),Be=(0,R.A)(xe,[["render",s],["__scopeId","data-v-58f37f06"]]),Le=Be},522:(e,t,o)=>{function r(e){return e?e.kp_id?"kp":e.imdb_id?"imdb":null:null}function a(e){if(!e)return null;const t=r(e);return"kp"===t?e.kp_id||null:"imdb"===t&&e.imdb_id||null}function i(e){const t={movie:"Фильм",tv:"Сериал",anime:"Аниме","tv-series":"Сериал","anime-series":"Аниме-сериал"};return t[e]||e}function l(e){return e?e.poster_urls?.w500?e.poster_urls.w500:e.poster_url||null:null}o.d(t,{GO:()=>a,Ib:()=>r,nR:()=>l,uF:()=>i})},699:(e,t,o)=>{o.d(t,{A:()=>l,B:()=>r});var r,a=o(526);(function(e){e["POPULAR"]="popular",e["NEW"]="new",e["THRILLER"]="thriller",e["DRAMA"]="drama",e["ACTION"]="action",e["SCIFI"]="scifi",e["DETECTIVE"]="detective"})(r||(r={}));class i{baseUrl;constructor(){this.baseUrl="https://v2test.appkinobot.com/api/vibix"}async getMoviesByCategory(e,t=1,o=20){const r=await a.A.get(`${this.baseUrl}/movies/category/${e}`,{params:{limit:o,page:t}});return r.data}async getMovie(e){const t=await a.A.get(`${this.baseUrl}/movies/${e}`);return t.data}async searchByUrl(e){const t=await a.A.get(`${this.baseUrl}/search`,{params:{url:e}});return t.data}}const l=new i},874:(e,t,o)=>{o.d(t,{s:()=>l});var r=o(657),a=o(526),i=o(699);const l=(0,r.nY)("movies",{state:()=>({currentMovie:null,loading:!1,error:null,categoryMovies:{},categoryLoading:{},categoryError:{},pendingRequests:{}}),actions:{async fetchMovieById(e,t){if(this.loading=!0,this.error=null,this.currentMovie=null,!window.Telegram||!window.Telegram.WebApp)return this.error="Telegram WebApp is not available",void(this.loading=!1);try{const o=await a.A.get(`/vibix/movies/${t}`,{params:{source:e}});o.data?this.currentMovie=o.data:this.error="Failed to load movie"}catch(o){this.error=o.response?.data?.message||o.message||"Unknown error"}finally{this.loading=!1}},async searchMovie(e){if(this.loading=!0,this.error=null,this.currentMovie=null,!window.Telegram||!window.Telegram.WebApp)return this.error="Telegram WebApp is not available",this.loading=!1,null;try{const t=await a.A.get("/vibix/search",{params:{url:e}});return t.data.success?(this.currentMovie=t.data.data,t.data.data):(this.error=t.data.message||"Failed to search movie",null)}catch(t){return this.error=t.response?.data?.message||t.message||"Unknown error",null}finally{this.loading=!1}},setSearchQuery(e){console.log("Search query set:",e)},clearCurrentMovie(){this.currentMovie=null},async fetchMoviesByCategory(e,t=1,o=20){const r=`${e}_${t}_${o}`;this.categoryLoading={...this.categoryLoading,[e]:!0},this.categoryError={...this.categoryError,[e]:null};try{const r=await i.A.getMoviesByCategory(e,t,o);return r.data?.length>0?(1===t&&(this.categoryMovies[e]=r.data),r.data):(this.categoryError[e]="Не удалось загрузить фильмы для категории",[])}catch(a){return this.categoryError[e]=a.message||"Ошибка при загрузке фильмов",[]}finally{this.categoryLoading[e]=!1,delete this.pendingRequests[r]}},async getMoviesByCategory(e,t=1,o=20,r=!0){const a=`${e}_${t}_${o}`;if(r&&1===t&&this.categoryMovies[e]?.length>0)return this.categoryMovies[e];if(this.pendingRequests[a])return await this.pendingRequests[a];const i=this.fetchMoviesByCategory(e,t,o);return this.pendingRequests[a]=i,await i}}})},938:(e,t,o)=>{o.d(t,{a:()=>i});var r=o(657),a=o(526);const i=(0,r.nY)("bookmarks",{state:()=>({bookmarks:[],loading:!1,error:null,lastFetchTime:0}),actions:{async fetchBookmarks(e=!1){const t=Date.now(),o=3e5;if(!(!e&&this.bookmarks.length>0&&t-this.lastFetchTime<o)){this.loading=!0,this.error=null;try{const e=await a.A.get("/bookmarks");e.data.success?(this.bookmarks=e.data.data,this.lastFetchTime=t):this.error=e.data.message||"Failed to load bookmarks"}catch(r){this.error=r.response?.data?.message||r.message||"Unknown error",console.error("Error fetching bookmarks:",r)}finally{this.loading=!1}}},async addBookmark(e,t){this.loading=!0,this.error=null;try{const r=await a.A.post("/bookmarks",{movie_id:e,source:t});if(r.data.success){let i;try{if("kp"===t){const t=await a.A.get(`/vibix/movies/${e}?source=kp`);i=t.data}else{const t=await a.A.get(`/vibix/movies/${e}?source=imdb`);i=t.data}this.bookmarks.unshift({_id:r.data.data._id,source:t,details:i})}catch(o){console.error("Error fetching movie details:",o),this.bookmarks.unshift({_id:r.data.data._id,source:t,details:null})}return!0}return this.error=r.data.message||"Failed to add bookmark",!1}catch(r){return this.error=r.response?.data?.message||r.message||"Unknown error",console.error("Error adding bookmark:",r),!1}finally{this.loading=!1}},async deleteBookmark(e){this.loading=!0,this.error=null;try{const t=await a.A.delete(`/bookmarks/${e}`);return t.data.success?(this.bookmarks=this.bookmarks.filter((t=>t._id!==e)),!0):(this.error=t.data.message||"Failed to delete bookmark",!1)}catch(t){return this.error=t.response?.data?.message||t.message||"Unknown error",console.error("Error deleting bookmark:",t),!1}finally{this.loading=!1}},isBookmarked(e,t){return this.bookmarks.some((o=>!!o.details&&("kp"===t?o.details.kp_id===e&&o.source===t:"imdb"===t&&(o.details.imdb_id===e&&o.source===t))))},getBookmarkId(e,t){const o=this.bookmarks.find((o=>!!o.details&&("kp"===t?o.details.kp_id===e&&o.source===t:"imdb"===t&&(o.details.imdb_id===e&&o.source===t))));return o?o._id:null}}})}}]);
//# sourceMappingURL=227.70378d1b.js.map