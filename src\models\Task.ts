import mongoose, { Document, Schema } from 'mongoose';

export interface ITask extends Document {
  _id: mongoose.Types.ObjectId;
  title: string;
  logo_url: string;
  link: string;
  type: 'telegram' | 'link';
  is_active: boolean;
  points: number;
  created_at: Date;
  updated_at: Date;
}

const TaskSchema: Schema = new Schema({
  title: {
    type: String,
    required: true
  },
  logo_url: {
    type: String,
    required: true
  },
  link: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['telegram', 'link'],
    default: 'link'
  },
  is_active: {
    type: Boolean,
    default: true
  },
  points: {
    type: Number,
    default: 1
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

export default mongoose.model<ITask>('Task', TaskSchema);
