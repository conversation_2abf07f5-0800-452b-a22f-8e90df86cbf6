"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const bookmarkController_1 = require("../controllers/bookmarkController");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const router = express_1.default.Router();
// Apply auth middleware to all routes
router.use(authMiddleware_1.authMiddleware);
// Get user's bookmarks
router.get('/', bookmarkController_1.getBookmarks);
// Add bookmark
router.post('/', bookmarkController_1.addBookmark);
// Delete bookmark
router.delete('/:id', bookmarkController_1.deleteBookmark);
exports.default = router;
