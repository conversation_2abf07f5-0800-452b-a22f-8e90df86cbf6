"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
class TMDBService {
    constructor() {
        this.baseDelay = 300; // 300ms между запросами
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.apiKey = process.env.TMDB_API_KEY || '';
        this.baseUrl = 'https://api.themoviedb.org/3';
        this.imageBaseUrl = 'https://image.tmdb.org/t/p';
        if (!this.apiKey) {
            console.warn('TMDB_API_KEY not found in environment variables');
        }
    }
    // Простая задержка между запросами
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    // Добавление запроса в очередь для соблюдения rate limits
    async queueRequest(requestFn) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push(async () => {
                try {
                    const result = await requestFn();
                    resolve(result);
                }
                catch (error) {
                    reject(error);
                }
            });
            this.processQueue();
        });
    }
    // Обработка очереди запросов с соблюдением rate limits
    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }
        this.isProcessingQueue = true;
        while (this.requestQueue.length > 0) {
            // ПРИНУДИТЕЛЬНАЯ задержка между запросами
            await this.sleep(this.baseDelay);
            const requestFn = this.requestQueue.shift();
            if (requestFn) {
                await requestFn();
            }
        }
        this.isProcessingQueue = false;
    }
    // Основной метод для запросов с rate limiting
    async makeRequest(url, params, logDetails = false) {
        return this.queueRequest(async () => {
            // Логируем только в development режиме
            if (logDetails && process.env.NODE_ENV !== 'production') {
                const fullUrl = params ? `${url}?${new URLSearchParams(params).toString()}` : url;
                console.log(`[TMDB] Request: ${fullUrl}`);
            }
            try {
                const response = await axios_1.default.get(url, {
                    params: params,
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000
                });
                if (logDetails && process.env.NODE_ENV !== 'production') {
                    console.log(`[TMDB] Response status: ${response.status}, data keys:`, Object.keys(response.data));
                }
                return response.data;
            }
            catch (error) {
                if (error.response?.status === 429) {
                    // Rate limit hit - ждем дольше
                    console.log('TMDB rate limit hit, waiting 2 seconds');
                    await this.sleep(2000);
                    return this.makeRequest(url, params, logDetails); // Повторяем запрос
                }
                // При любой ошибке добавляем задержку для соблюдения rate limits
                await this.sleep(500);
                if (logDetails && process.env.NODE_ENV !== 'production') {
                    const fullUrl = params ? `${url}?${new URLSearchParams(params).toString()}` : url;
                    console.error(`[TMDB] Request failed: ${fullUrl}`);
                    console.error(`[TMDB] Error status: ${error.response?.status || 'Unknown'}, message: ${error.message}`);
                }
                else {
                    console.error(`TMDB API error for ${url}: ${error.response?.status || 'Unknown'} - ${error.message}`);
                }
                throw error;
            }
        });
    }
    // Поиск фильма по IMDB ID
    async findMovieByImdbId(imdbId, enableDetailedLogging = false) {
        try {
            if (!imdbId || !imdbId.startsWith('tt')) {
                if (enableDetailedLogging) {
                    console.log(`[TMDB] Invalid IMDB ID: ${imdbId}`);
                }
                return null;
            }
            const url = `${this.baseUrl}/find/${imdbId}`;
            const params = {
                external_source: 'imdb_id',
                language: 'ru'
            };
            const data = await this.makeRequest(url, params, enableDetailedLogging);
            if (enableDetailedLogging) {
                console.log(`[TMDB] Find response for ${imdbId}:`, {
                    movie_results_count: data.movie_results?.length || 0,
                    tv_results_count: data.tv_results?.length || 0,
                    person_results_count: data.person_results?.length || 0
                });
            }
            // Проверяем все массивы результатов
            const allResults = [
                ...(data.movie_results || []),
                ...(data.tv_results || [])
            ];
            if (allResults.length > 0) {
                const result = allResults[0];
                if (enableDetailedLogging) {
                    console.log(`[TMDB] Found result:`, {
                        id: result.id,
                        title: result.title || result.name,
                        poster_path: result.poster_path,
                        type: data.movie_results?.includes(result) ? 'movie' : 'tv'
                    });
                }
                // Нормализуем поля для совместимости
                return {
                    id: result.id,
                    title: result.title || result.name || '',
                    poster_path: result.poster_path,
                    release_date: result.release_date || result.first_air_date || '',
                    vote_average: result.vote_average || 0
                };
            }
            return null;
        }
        catch (error) {
            console.error(`Error finding movie by IMDB ID ${imdbId}: ${error.message}`);
            return null;
        }
    }
    // Поиск по названию через multi search (фильмы, сериалы, люди)
    async searchMulti(title, enableDetailedLogging = false) {
        try {
            if (!title || title.length < 2) {
                if (enableDetailedLogging) {
                    console.log(`[TMDB] Invalid title: "${title}"`);
                }
                return null;
            }
            const url = `${this.baseUrl}/search/multi`;
            const params = {
                query: title,
                include_adult: false,
                language: 'ru-RU',
                page: 1
            };
            const data = await this.makeRequest(url, params, enableDetailedLogging);
            if (enableDetailedLogging) {
                console.log(`[TMDB] Multi search response for "${title}":`, {
                    total_results: data.total_results || 0,
                    results_count: data.results?.length || 0
                });
            }
            if (data.results && data.results.length > 0) {
                // Фильтруем только фильмы и сериалы (исключаем людей)
                const mediaResults = data.results.filter(result => result.media_type === 'movie' || result.media_type === 'tv');
                if (enableDetailedLogging) {
                    console.log(`[TMDB] Media results (movies/tv):`, {
                        total_media_results: mediaResults.length,
                        types: mediaResults.map(r => r.media_type)
                    });
                }
                if (mediaResults.length > 0) {
                    const result = mediaResults[0];
                    if (enableDetailedLogging) {
                        console.log(`[TMDB] First media result:`, {
                            id: result.id,
                            media_type: result.media_type,
                            title: result.title || result.name,
                            poster_path: result.poster_path
                        });
                    }
                    // Нормализуем поля для совместимости
                    return {
                        id: result.id,
                        title: result.title || result.name || '',
                        poster_path: result.poster_path,
                        release_date: result.release_date || result.first_air_date || '',
                        vote_average: result.vote_average || 0
                    };
                }
            }
            return null;
        }
        catch (error) {
            console.error(`Error searching multi by title "${title}": ${error.message}`);
            return null;
        }
    }
    // Построение URL постеров
    buildPosterUrls(posterPath) {
        if (!posterPath) {
            return {
                w500: '',
                original: ''
            };
        }
        return {
            w500: `${this.imageBaseUrl}/w500${posterPath}`,
            original: `${this.imageBaseUrl}/original${posterPath}`
        };
    }
    // Получение статистики очереди запросов (для мониторинга)
    getQueueStats() {
        return {
            queueLength: this.requestQueue.length,
            isProcessing: this.isProcessingQueue
        };
    }
}
exports.default = new TMDBService();
