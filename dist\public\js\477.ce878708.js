"use strict";(self["webpackChunkcinema_bot_frontend"]=self["webpackChunkcinema_bot_frontend"]||[]).push([[477],{477:(s,e,a)=>{a.r(e),a.d(e,{default:()=>M});var t=a(768),n=a(130),o=a(232);const r={class:"tasks-view"},l={class:"container"},i={class:"page-description"},k={key:0,class:"loading-container"},c={key:1,class:"error-container"},d={key:2,class:"empty-container"},u={key:3,class:"tasks-list"},p={key:4,class:"user-points"},g={class:"points-value"};function v(s,e,a,v,h,y){const w=(0,t.g2)("TaskCard");return(0,t.uX)(),(0,t.CE)("div",r,[(0,t.Lk)("div",l,[e[6]||(e[6]=(0,t.Lk)("h1",{class:"page-title"},"Задания",-1)),(0,t.Lk)("p",i,[e[2]||(e[2]=(0,t.eW)("Выполняйте задания и получайте ")),(0,t.Lk)("a",{href:"#",class:"cefi-link",onClick:e[0]||(e[0]=(0,n.D$)((()=>{}),["prevent"]))},"$CEFIcoin")]),s.loading?((0,t.uX)(),(0,t.CE)("div",k,e[3]||(e[3]=[(0,t.Lk)("div",{class:"loading-spinner large"},null,-1),(0,t.Lk)("p",null,"Загрузка заданий...",-1)]))):s.error?((0,t.uX)(),(0,t.CE)("div",c,[(0,t.Lk)("p",null,(0,o.v_)(s.error),1),(0,t.Lk)("button",{onClick:e[1]||(e[1]=(...e)=>s.fetchTasks&&s.fetchTasks(...e)),class:"retry-button"},"Повторить")])):0===s.tasks.length?((0,t.uX)(),(0,t.CE)("div",d,e[4]||(e[4]=[(0,t.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"empty-icon"},[(0,t.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,t.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),(0,t.Lk)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1),(0,t.Lk)("p",null,"Нет доступных заданий",-1)]))):((0,t.uX)(),(0,t.CE)("div",u,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(s.tasks,(s=>((0,t.uX)(),(0,t.Wv)(w,{key:s._id,task:s},null,8,["task"])))),128))])),s.user?((0,t.uX)(),(0,t.CE)("div",p,[(0,t.Lk)("p",null,[e[5]||(e[5]=(0,t.eW)("Ваши $CEFIcoin: ")),(0,t.Lk)("span",g,(0,o.v_)(s.user.task_points),1)])])):(0,t.Q3)("",!0)])])}const h={class:"task-logo"},y=["src","alt"],w={key:1,class:"logo-placeholder"},L={class:"task-info"},m={class:"task-title"},C={class:"task-points"},f={class:"task-action"},E={key:0,class:"loading-spinner"},T={key:1,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"chevron-icon"};function _(s,e,a,n,r,l){return(0,t.uX)(),(0,t.CE)("div",{class:(0,o.C4)(["task-card",{loading:s.loading}]),onClick:e[0]||(e[0]=(...e)=>s.openTask&&s.openTask(...e))},[(0,t.Lk)("div",h,[s.task.logo_url?((0,t.uX)(),(0,t.CE)("img",{key:0,src:s.task.logo_url,alt:s.task.title},null,8,y)):((0,t.uX)(),(0,t.CE)("div",w,e[1]||(e[1]=[(0,t.Lk)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[(0,t.Lk)("circle",{cx:"12",cy:"12",r:"10"}),(0,t.Lk)("line",{x1:"12",y1:"8",x2:"12",y2:"16"}),(0,t.Lk)("line",{x1:"8",y1:"12",x2:"16",y2:"12"})],-1)])))]),(0,t.Lk)("div",L,[(0,t.Lk)("h3",m,(0,o.v_)(s.task.title),1),(0,t.Lk)("div",C,(0,o.v_)(s.task.points)+" $CEFIcoin",1)]),(0,t.Lk)("div",f,[s.loading?((0,t.uX)(),(0,t.CE)("div",E)):((0,t.uX)(),(0,t.CE)("svg",T,e[2]||(e[2]=[(0,t.Lk)("polyline",{points:"9 18 15 12 9 6"},null,-1)])))])],2)}var x=a(144),X=a(657),b=a(526);const W=(0,X.nY)("tasks",{state:()=>({tasks:[],loading:!1,error:null}),actions:{async fetchTasks(){this.loading=!0,this.error=null;try{const s=await b.A.get("/tasks");s.data.success?this.tasks=s.data.data:this.error=s.data.message||"Failed to load tasks"}catch(s){this.error=s.response?.data?.message||s.message||"Unknown error",console.error("Error fetching tasks:",s)}finally{this.loading=!1}},async completeTask(s){this.loading=!0,this.error=null;try{const e=await b.A.post("/tasks/complete",{task_id:s});return e.data.success?(await this.fetchTasks(),!0):(this.error=e.data.message||"Failed to complete task",!1)}catch(e){return this.error=e.response?.data?.message||e.message||"Unknown error",console.error("Error completing task:",e),!1}finally{this.loading=!1}},openTaskLink(s){const e=window.Telegram?.WebApp;e?(s.includes("t.me/")||s.includes("telegram.me/"))&&e.openTelegramLink?e.openTelegramLink(s):e.openLink(s):window.open(s,"_blank")}}}),F=(0,t.pM)({name:"TaskCard",props:{task:{type:Object,required:!0}},setup(s){const e=W(),a=(0,x.KR)(!1),t=async()=>{a.value=!0;try{e.openTaskLink(s.task.link),await e.completeTask(s.task._id)}catch(t){console.error("Error completing task:",t)}finally{a.value=!1}};return{openTask:t,loading:a}}});var I=a(241);const A=(0,I.A)(F,[["render",_],["__scopeId","data-v-58e4ad20"]]),j=A;var $=a(652);const B=(0,t.pM)({name:"TasksView",components:{TaskCard:j},setup(){const s=W(),e=(0,$.k)(),a=(0,t.EW)((()=>s.tasks)),n=(0,t.EW)((()=>s.loading)),o=(0,t.EW)((()=>s.error)),r=(0,t.EW)((()=>e.user)),l=async()=>{await s.fetchTasks()};return(0,t.sV)((()=>{l()})),{tasks:a,loading:n,error:o,user:r,fetchTasks:l}}}),K=(0,I.A)(B,[["render",v],["__scopeId","data-v-1b326350"]]),M=K}}]);
//# sourceMappingURL=477.ce878708.js.map