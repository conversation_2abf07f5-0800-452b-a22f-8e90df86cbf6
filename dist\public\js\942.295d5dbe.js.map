{"version": 3, "file": "js/942.295d5dbe.js", "mappings": "8HASM,SAAUA,EAAeC,GAC7B,OAAKA,EAEDA,EAAMC,MAAc,KACpBD,EAAME,QAAgB,OAEnB,KALY,IAMrB,CAOM,SAAUC,EAAWH,GACzB,IAAKA,EAAO,OAAO,KAEnB,MAAMI,EAASL,EAAeC,GAE9B,MAAe,OAAXI,EAAwBJ,EAAMC,OAAS,KAC5B,SAAXG,GAA0BJ,EAAME,SAE7B,IACT,CAOM,SAAUG,EAAgBC,GAC9B,MAAMC,EAAgC,CACpCP,MAAO,QACPQ,GAAI,SACJC,MAAO,QACP,YAAa,SACb,eAAgB,gBAGlB,OAAOF,EAAMD,IAASA,CACxB,CAOM,SAAUI,EAAkBV,GAChC,OAAKA,EAGDA,EAAMW,aAAaC,KACdZ,EAAMW,YAAYC,KAGpBZ,EAAMa,YAAc,KAPR,IAQrB,C,mGCzCO,MAAMC,GAAoBC,EAAAA,EAAAA,IAAY,YAAa,CACxDC,MAAOA,KAAA,CACLC,UAAW,GACXC,SAAS,EACTC,MAAO,KACPC,cAAe,IAGjBC,QAAS,CACP,oBAAMC,CAAeC,GAAe,GAElC,MAAMC,EAAMC,KAAKD,MACXE,EAAc,IAEpB,MAAKH,GACDI,KAAKV,UAAUW,OAAS,GACxBJ,EAAMG,KAAKP,cAAgBM,GAF/B,CAMAC,KAAKT,SAAU,EACfS,KAAKR,MAAQ,KAEb,IACE,MAAMU,QAAiBC,EAAAA,EAAIC,IAAI,cAE3BF,EAASG,KAAKC,SAChBN,KAAKV,UAAYY,EAASG,KAAKA,KAC/BL,KAAKP,cAAgBI,GAErBG,KAAKR,MAAQU,EAASG,KAAKE,SAAW,0BAE1C,CAAE,MAAOf,GACPQ,KAAKR,MAAQA,EAAMU,UAAUG,MAAME,SAAWf,EAAMe,SAAW,gBAC/DC,QAAQhB,MAAM,4BAA6BA,EAC7C,CAAE,QACAQ,KAAKT,SAAU,CACjB,CAnBA,CAoBF,EAEA,iBAAMkB,CAAYC,EAAkBjC,GAClCuB,KAAKT,SAAU,EACfS,KAAKR,MAAQ,KAEb,IACE,MAAMU,QAAiBC,EAAAA,EAAIQ,KAAK,aAAc,CAAED,WAAUjC,WAE1D,GAAIyB,EAASG,KAAKC,QAAS,CAEzB,IAAIM,EACJ,IACE,GAAe,OAAXnC,EAAiB,CAEnB,MAAMoC,QAAsBV,EAAAA,EAAIC,IAAI,iBAAiBM,eACrDE,EAAeC,EAAcR,IAC/B,KAAO,CACL,MAAMQ,QAAsBV,EAAAA,EAAIC,IAAI,iBAAiBM,iBACrDE,EAAeC,EAAcR,IAC/B,CAGAL,KAAKV,UAAUwB,QAAQ,CACrBC,IAAKb,EAASG,KAAKA,KAAKU,IACxBtC,OAAQA,EACRuC,QAASJ,GAEb,CAAE,MAAOK,GACPT,QAAQhB,MAAM,gCAAiCyB,GAE/CjB,KAAKV,UAAUwB,QAAQ,CACrBC,IAAKb,EAASG,KAAKA,KAAKU,IACxBtC,OAAQA,EACRuC,QAAS,MAEb,CAEA,OAAO,CACT,CAEE,OADAhB,KAAKR,MAAQU,EAASG,KAAKE,SAAW,0BAC/B,CAEX,CAAE,MAAOf,GAGP,OAFAQ,KAAKR,MAAQA,EAAMU,UAAUG,MAAME,SAAWf,EAAMe,SAAW,gBAC/DC,QAAQhB,MAAM,yBAA0BA,IACjC,CACT,CAAE,QACAQ,KAAKT,SAAU,CACjB,CACF,EAEA,oBAAM2B,CAAeC,GACnBnB,KAAKT,SAAU,EACfS,KAAKR,MAAQ,KAEb,IACE,MAAMU,QAAiBC,EAAAA,EAAIiB,OAAO,cAAcD,KAEhD,OAAIjB,EAASG,KAAKC,SAEhBN,KAAKV,UAAYU,KAAKV,UAAU+B,QAAOC,GAAYA,EAASP,MAAQI,KAC7D,IAEPnB,KAAKR,MAAQU,EAASG,KAAKE,SAAW,6BAC/B,EAEX,CAAE,MAAOf,GAGP,OAFAQ,KAAKR,MAAQA,EAAMU,UAAUG,MAAME,SAAWf,EAAMe,SAAW,gBAC/DC,QAAQhB,MAAM,2BAA4BA,IACnC,CACT,CAAE,QACAQ,KAAKT,SAAU,CACjB,CACF,EAEAgC,YAAAA,CAAab,EAAkBjC,GAC7B,OAAOuB,KAAKV,UAAUkC,MAAKF,KACpBA,EAASN,UAEC,OAAXvC,EACK6C,EAASN,QAAQ1C,QAAUoC,GAAYY,EAAS7C,SAAWA,EAC9C,SAAXA,IACF6C,EAASN,QAAQzC,UAAYmC,GAAYY,EAAS7C,SAAWA,KAK1E,EAEAgD,aAAAA,CAAcf,EAAkBjC,GAC9B,MAAM6C,EAAWtB,KAAKV,UAAUoC,MAAKJ,KAC9BA,EAASN,UAEC,OAAXvC,EACK6C,EAASN,QAAQ1C,QAAUoC,GAAYY,EAAS7C,SAAWA,EAC9C,SAAXA,IACF6C,EAASN,QAAQzC,UAAYmC,GAAYY,EAAS7C,SAAWA,MAMxE,OAAO6C,EAAWA,EAASP,IAAM,IACnC,I,oECpKJ,MAAMY,EAAa,CCDZC,MAAM,kBDEPC,EAAa,CCDVD,MAAM,aDETE,EAAa,CCJnBC,IAAA,EAM0BH,MAAM,qBDE1BI,EAAa,CCRnBD,IAAA,EAW6BH,MAAM,mBDC7BK,EAAa,CCZnBF,IAAA,EAgB8CH,MAAM,mBDA9CM,EAAa,CChBnBH,IAAA,EAwBkBH,MAAM,kBDJlBO,EAAa,CCpBnBJ,IAAA,EA+BsBH,MAAM,kBDPtBQ,ECxBN,YD0BM,SAAUC,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAC3CC,GAAuBD,EAAAA,EAAAA,IAAkB,aAE/C,OAAQE,EAAAA,EAAAA,OC7BRC,EAAAA,EAAAA,IAqCM,MArCNrB,EAqCM,EApCJsB,EAAAA,EAAAA,IAmCM,MAnCNpB,EAmCM,CDLJU,EAAO,KAAOA,EAAO,IC7BrBU,EAAAA,EAAAA,IAAoC,MAAhCrB,MAAM,cAAa,YAAQ,ID8B/BW,EAAO,KAAOA,EAAO,IC7BrBU,EAAAA,EAAAA,IAAkD,KAA/CrB,MAAM,oBAAmB,sBAAkB,IAEnCU,EAAA/C,UD6BNwD,EAAAA,EAAAA,OC7BLC,EAAAA,EAAAA,IAGM,MAHNlB,EAGMS,EAAA,KAAAA,EAAA,KAFJU,EAAAA,EAAAA,IAAyC,OAApCrB,MAAM,yBAAuB,UAClCqB,EAAAA,EAAAA,IAA2B,SAAxB,wBAAoB,OAGTX,EAAA9C,QD6BTuD,EAAAA,EAAAA,OC7BPC,EAAAA,EAAAA,IAGM,MAHNhB,EAGM,EAFJiB,EAAAA,EAAAA,IAAkB,UAAAC,EAAAA,EAAAA,IAAZZ,EAAA9C,OAAK,IACXyD,EAAAA,EAAAA,IAAuE,UAA9DE,QAAKZ,EAAA,KAAAA,EAAA,GDgCtB,IAAIa,IChCoBd,EAAA3C,gBAAA2C,EAAA3C,kBAAAyD,IAAgBxB,MAAM,gBAAe,gBAGlB,IAArBU,EAAAhD,UAAUW,SDkCjB8C,EAAAA,EAAAA,OClCTC,EAAAA,EAAAA,IAMM,MANNf,EAMM,CD6BIM,EAAO,KAAOA,EAAO,IClC7BU,EAAAA,EAAAA,IAEM,OAFDI,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,QAAQ5B,MAAM,cD2CzJ,EC1CTqB,EAAAA,EAAAA,IAAmE,QAA7DQ,EAAE,wDD4CE,IACJlB,EAAO,KAAOA,EAAO,IC3C7BU,EAAAA,EAAAA,IAA8B,SAA3B,2BAAuB,KAC1BS,EAAAA,EAAAA,IAAgFd,EAAA,CAAnEe,GAAG,WAAW/B,MAAM,iBD8CtB,CCnEnBgC,SAAAC,EAAAA,EAAAA,KAqByD,IAAiBtB,EAAA,KAAAA,EAAA,KArB1EuB,EAAAA,EAAAA,IAqByD,yBArBzDC,EAAA,SD0EehB,EAAAA,EAAAA,OClDTC,EAAAA,EAAAA,IAYM,MAZNd,EAYM,GDuCKa,EAAAA,EAAAA,KAAW,IClDpBC,EAAAA,EAAAA,IAUMgB,EAAAA,GAAA,MAnCdC,EAAAA,EAAAA,IAyBgC3B,EAAAhD,WAAZgC,KDmDMyB,EAAAA,EAAAA,OCnDlBC,EAAAA,EAAAA,IAUM,OAV8BjB,IAAKT,EAASP,IAAKa,MAAM,iBDsDhD,CCpDHN,EAASN,UDsDF+B,EAAAA,EAAAA,OCvDfmB,EAAAA,EAAAA,IAIEpB,EAAA,CA9BZf,IAAA,EA4Ba1D,MAAOiD,EAASN,QAChBmD,mBAAmB,GDwDL,KAAM,EAAG,CAAC,aACZpB,EAAAA,EAAAA,OCvDfC,EAAAA,EAAAA,IAGM,MAHNb,EAGM,CDqDUI,EAAO,KAAOA,EAAO,ICvDnCU,EAAAA,EAAAA,IAA+C,SAA5C,4CAAwC,KAC3CA,EAAAA,EAAAA,IAA6F,UAApFE,QAAKiB,GAAE9B,EAAA+B,eAAe/C,EAASP,KAAMa,MAAM,iBAAgB,mBAAgB,EAjChGQ,WD8FoB,YAIpB,C,aEhGA,MAAMT,EAAa,CCAVC,MAAM,gBDCTC,ECHN,cDIMC,EAAa,CCJnBC,IAAA,EAIkBH,MAAM,sBDIlBI,EAAa,CCMRJ,MAAM,cDLXK,EAAa,CCTnBF,IAAA,EAeuDH,MAAM,gBDFvDM,EAAa,CCMVN,MAAM,cDLTO,EAAa,CCMTP,MAAM,eDLVQ,EAAa,CCMVR,MAAM,wBDLT0C,EAAa,CCMR1C,MAAM,iBDLX2C,ECjBN,SDmBM,SAAUlC,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,OAAQI,EAAAA,EAAAA,OCnBRC,EAAAA,EAAAA,IA6BM,OA7BDpB,MAAM,aAAcuB,QAAKZ,EAAA,KAAAA,EAAA,GDuBhC,IAAIa,ICvB8Bd,EAAAkC,iBAAAlC,EAAAkC,mBAAApB,KDwB7B,ECvBDH,EAAAA,EAAAA,IAgBM,MAhBNtB,EAgBM,CAfOW,EAAAmC,YDyBN1B,EAAAA,EAAAA,OCzBLC,EAAAA,EAAAA,IAAkF,OAHxFjB,IAAA,EAG6B2C,IAAKpC,EAAAmC,UAAYE,IAAKrC,EAAAjE,MAAMuG,SAAUhD,MAAM,gBD8B5D,KAAM,ECjCnBC,MDkCWkB,EAAAA,EAAAA,OC9BLC,EAAAA,EAAAA,IASM,MATNlB,EASMS,EAAA,KAAAA,EAAA,KAbZsC,EAAAA,EAAAA,IAAA,sjBAcM5B,EAAAA,EAAAA,IAA8C,MAA9CjB,GAA8CkB,EAAAA,EAAAA,IAAnBZ,EAAAjE,MAAMyG,MAAI,GAC1BxC,EAAAjE,MAAM0G,WAAazC,EAAAjE,MAAM2G,cDwB/BjC,EAAAA,EAAAA,OCxBLC,EAAAA,EAAAA,IAEM,MAFNf,GAEMiB,EAAAA,EAAAA,IADDZ,EAAAjE,MAAM0G,WAAazC,EAAAjE,MAAM2G,aAAW,KAhB/CC,EAAAA,EAAAA,IAAA,UAmBIhC,EAAAA,EAAAA,IAUM,MAVNf,EAUM,EATJe,EAAAA,EAAAA,IAAiD,KAAjDd,GAAiDe,EAAAA,EAAAA,IAAtBZ,EAAAjE,MAAMuG,UAAQ,IACzC3B,EAAAA,EAAAA,IAA6D,IAA7Db,GAA6Dc,EAAAA,EAAAA,IAA1BZ,EAAAjE,MAAM6G,eAAa,IACtDjC,EAAAA,EAAAA,IAMM,MANNqB,EAMM,EALJrB,EAAAA,EAAAA,IAIS,UAJDrB,MAAM,kBAAmBuB,QAAKZ,EAAA,KAAAA,EAAA,IAvB9C4C,EAAAA,EAAAA,KDkDA,IAAI/B,IC3BiDd,EAAA8C,gBAAA9C,EAAA8C,kBAAAhC,IAAc,YD4BxD,GACAL,EAAAA,EAAAA,OC5BDC,EAAAA,EAAAA,IAEM,OAFDK,MAAM,6BAA6BC,QAAQ,YAAaC,KAAMjB,EAAAf,aAAe,eAAiB,OAAQiC,OAAO,eAAe,eAAa,IAAI,iBAAe,QAAQ,kBAAgB,SDoCtLjB,EAAO,KAAOA,EAAO,GAAK,ECnC3BU,EAAAA,EAAAA,IAAmE,QAA7DQ,EAAE,qDAAmD,WDqCzD,EC9Ddc,WDmEA,C,wCC5BA,SAAec,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,YACNC,MAAO,CACLlH,MAAO,CACLM,KAAM6G,OACNC,UAAU,GAEZtB,kBAAmB,CACjBxF,KAAM+G,QACN9B,SAAS,IAGb+B,KAAAA,CAAMJ,GACJ,MAAMK,GAASC,EAAAA,EAAAA,MACTC,GAAiB3G,EAAAA,EAAAA,KACjBI,GAAUwG,EAAAA,EAAAA,KAAI,GAGdtH,GAASuH,EAAAA,EAAAA,KAAS,KAAM5H,EAAAA,EAAAA,IAAemH,EAAMlH,SAC7C4H,GAAUD,EAAAA,EAAAA,KAAS,KAAMxH,EAAAA,EAAAA,IAAW+G,EAAMlH,SAG1CkD,GAAeyE,EAAAA,EAAAA,KAAS,MAExBT,EAAMpB,sBAGL1F,EAAOyH,QAAUD,EAAQC,QACvBJ,EAAevE,aAAa0E,EAAQC,MAAOzH,EAAOyH,SAIrDzB,GAAYuB,EAAAA,EAAAA,KAAS,KAAMjH,EAAAA,EAAAA,IAAkBwG,EAAMlH,SAGnDmG,EAAkBA,KAClB/F,EAAOyH,OAASD,EAAQC,OAC1BN,EAAOO,KAAK,UAAU1H,EAAOyH,SAASD,EAAQC,QAChD,EAIId,EAAiBgB,UAGrB,GAFAC,EAAMC,kBAED7H,EAAOyH,OAAUD,EAAQC,MAA9B,CAEA3G,EAAQ2G,OAAQ,EAEhB,IAEE,GAAIX,EAAMpB,kBAAmB,CAC3B,MAAMoC,EAAaT,EAAerE,cAAcwE,EAAQC,MAAOzH,EAAOyH,OAClEK,SACIT,EAAe5E,eAAeqF,EAExC,MAEE,GAAIhF,EAAa2E,MAAO,CACtB,MAAMK,EAAaT,EAAerE,cAAcwE,EAAQC,MAAOzH,EAAOyH,OAClEK,SACIT,EAAe5E,eAAeqF,EAExC,YACQT,EAAerF,YAAYwF,EAAQC,MAAOzH,EAAOyH,MAG7D,CAAE,MAAO1G,GACPgB,QAAQhB,MAAM,2BAA4BA,EAC5C,CAAE,QACAD,EAAQ2G,OAAQ,CAClB,CA1B2C,CA0B3C,EAGF,MAAO,CACLzH,SACAwH,UACA1E,eACAkD,YACAD,kBACAY,iBACA7F,UAEJ,I,aCnHF,MAAMiH,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,IHqCA,GAAenB,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,gBACNmB,WAAY,CACVC,UAASA,GAEXf,KAAAA,GACE,MAAMG,GAAiB3G,EAAAA,EAAAA,KAGjBG,GAAY0G,EAAAA,EAAAA,KAAS,IAAMF,EAAexG,YAC1CC,GAAUyG,EAAAA,EAAAA,KAAS,IAAMF,EAAevG,UACxCC,GAAQwG,EAAAA,EAAAA,KAAS,IAAMF,EAAetG,QAGtCG,EAAiByG,gBACfN,EAAenG,gBAAgB,EAIjC0E,EAAiB+B,gBACfN,EAAe5E,eAAeC,EAAG,EAOzC,OAJAwF,EAAAA,EAAAA,KAAU,KACRhH,GAAgB,IAGX,CACLL,YACAC,UACAC,QACAG,iBACA0E,iBAEJ,IIzEI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAShC,GAAQ,CAAC,YAAY,qBAEzF,G", "sources": ["webpack://cinema-bot-frontend/./src/utils/movieUtils.ts", "webpack://cinema-bot-frontend/./src/store/bookmarks.ts", "webpack://cinema-bot-frontend/./src/views/BookmarksView.vue?61ae", "webpack://cinema-bot-frontend/./src/views/BookmarksView.vue", "webpack://cinema-bot-frontend/./src/components/MovieCard.vue?3b77", "webpack://cinema-bot-frontend/./src/components/MovieCard.vue", "webpack://cinema-bot-frontend/./src/components/MovieCard.vue?45f0", "webpack://cinema-bot-frontend/./src/views/BookmarksView.vue?d793"], "sourcesContent": ["// Импорт не используется, но оставлен для документации\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport { Movie } from '@/services/vibixService';\n\n/**\n * Определяет источник фильма (Кинопоиск или IMDB)\n * @param movie Объект фильма\n * @returns 'kp', 'imdb' или null, если источник не определен\n */\nexport function getMovieSource(movie: any | null): 'kp' | 'imdb' | null {\n  if (!movie) return null;\n\n  if (movie.kp_id) return 'kp';\n  if (movie.imdb_id) return 'imdb';\n\n  return null;\n}\n\n/**\n * Получает ID фильма в зависимости от источника\n * @param movie Объект фильма\n * @returns ID фильма или null, если ID не найден\n */\nexport function getMovieId(movie: any | null): string | null {\n  if (!movie) return null;\n\n  const source = getMovieSource(movie);\n\n  if (source === 'kp') return movie.kp_id || null;\n  if (source === 'imdb') return movie.imdb_id || null;\n\n  return null;\n}\n\n/**\n * Форматирует тип фильма для отображения\n * @param type Тип фильма\n * @returns Отформатированный тип фильма\n */\nexport function formatMovieType(type: string): string {\n  const types: Record<string, string> = {\n    movie: 'Фильм',\n    tv: 'Сериал',\n    anime: 'Аниме',\n    'tv-series': 'Сериал',\n    'anime-series': 'Аниме-сериал'\n  };\n\n  return types[type] || type;\n}\n\n/**\n * Получает URL постера фильма с приоритетом TMDB\n * @param movie Объект фильма\n * @returns URL постера или null, если постер отсутствует\n */\nexport function getMoviePosterUrl(movie: any | null): string | null {\n  if (!movie) return null;\n\n  // Приоритет: TMDB w500 > оригинальный poster_url > null\n  if (movie.poster_urls?.w500) {\n    return movie.poster_urls.w500;\n  }\n\n  return movie.poster_url || null;\n}\n", "import { defineStore } from 'pinia';\nimport api from '@/services/api';\n\ninterface MovieDetails {\n  id: string;\n  name_rus: string;\n  name_original: string;\n  iframe_url: string;\n  type: string;\n  year: number;\n  kp_id?: string;\n  imdb_id?: string;\n  kp_rating?: number;\n  imdb_rating?: number;\n  description_short?: string;\n  poster_url?: string;\n}\n\ninterface Bookmark {\n  _id: string;\n  source: 'kp' | 'imdb';\n  details: MovieDetails | null;\n}\n\nexport const useBookmarksStore = defineStore('bookmarks', {\n  state: () => ({\n    bookmarks: [] as Bookmark[],\n    loading: false,\n    error: null as string | null,\n    lastFetchTime: 0 // Время последней загрузки закладок\n  }),\n\n  actions: {\n    async fetchBookmarks(forceRefresh = false) {\n      // Если закладки уже загружены и прошло менее 5 минут с последней загрузки, не загружаем их снова\n      const now = Date.now();\n      const fiveMinutes = 5 * 60 * 1000; // 5 минут в миллисекундах\n\n      if (!forceRefresh &&\n          this.bookmarks.length > 0 &&\n          now - this.lastFetchTime < fiveMinutes) {\n        return;\n      }\n\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.get('/bookmarks');\n\n        if (response.data.success) {\n          this.bookmarks = response.data.data;\n          this.lastFetchTime = now;\n        } else {\n          this.error = response.data.message || 'Failed to load bookmarks';\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error fetching bookmarks:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async addBookmark(movie_id: string, source: 'kp' | 'imdb') {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.post('/bookmarks', { movie_id, source });\n\n        if (response.data.success) {\n          // Получаем детали фильма для добавления в state\n          let movieDetails;\n          try {\n            if (source === 'kp') {\n              // Используем существующий API для получения деталей фильма\n              const movieResponse = await api.get(`/vibix/movies/${movie_id}?source=kp`);\n              movieDetails = movieResponse.data;\n            } else {\n              const movieResponse = await api.get(`/vibix/movies/${movie_id}?source=imdb`);\n              movieDetails = movieResponse.data;\n            }\n\n            // Добавляем новую закладку в state\n            this.bookmarks.unshift({\n              _id: response.data.data._id,\n              source: source,\n              details: movieDetails\n            });\n          } catch (movieError) {\n            console.error('Error fetching movie details:', movieError);\n            // Если не удалось получить детали фильма, все равно добавляем закладку\n            this.bookmarks.unshift({\n              _id: response.data.data._id,\n              source: source,\n              details: null\n            });\n          }\n\n          return true;\n        } else {\n          this.error = response.data.message || 'Failed to add bookmark';\n          return false;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error adding bookmark:', error);\n        return false;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async deleteBookmark(id: string) {\n      this.loading = true;\n      this.error = null;\n\n      try {\n        const response = await api.delete(`/bookmarks/${id}`);\n\n        if (response.data.success) {\n          // Remove bookmark from state\n          this.bookmarks = this.bookmarks.filter(bookmark => bookmark._id !== id);\n          return true;\n        } else {\n          this.error = response.data.message || 'Failed to delete bookmark';\n          return false;\n        }\n      } catch (error: any) {\n        this.error = error.response?.data?.message || error.message || 'Unknown error';\n        console.error('Error deleting bookmark:', error);\n        return false;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    isBookmarked(movie_id: string, source: 'kp' | 'imdb'): boolean {\n      return this.bookmarks.some(bookmark => {\n        if (!bookmark.details) return false;\n\n        if (source === 'kp') {\n          return bookmark.details.kp_id === movie_id && bookmark.source === source;\n        } else if (source === 'imdb') {\n          return bookmark.details.imdb_id === movie_id && bookmark.source === source;\n        }\n\n        return false;\n      });\n    },\n\n    getBookmarkId(movie_id: string, source: 'kp' | 'imdb'): string | null {\n      const bookmark = this.bookmarks.find(bookmark => {\n        if (!bookmark.details) return false;\n\n        if (source === 'kp') {\n          return bookmark.details.kp_id === movie_id && bookmark.source === source;\n        } else if (source === 'imdb') {\n          return bookmark.details.imdb_id === movie_id && bookmark.source === source;\n        }\n\n        return false;\n      });\n\n      return bookmark ? bookmark._id : null;\n    }\n  }\n});\n", "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"bookmarks-view\" }\nconst _hoisted_2 = { class: \"container\" }\nconst _hoisted_3 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_4 = {\n  key: 1,\n  class: \"error-container\"\n}\nconst _hoisted_5 = {\n  key: 2,\n  class: \"empty-container\"\n}\nconst _hoisted_6 = {\n  key: 3,\n  class: \"bookmarks-list\"\n}\nconst _hoisted_7 = {\n  key: 1,\n  class: \"bookmark-error\"\n}\nconst _hoisted_8 = [\"onClick\"]\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_router_link = _resolveComponent(\"router-link\")!\n  const _component_MovieCard = _resolveComponent(\"MovieCard\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[6] || (_cache[6] = _createElementVNode(\"h1\", { class: \"page-title\" }, \"Закладки\", -1)),\n      _cache[7] || (_cache[7] = _createElementVNode(\"p\", { class: \"page-description\" }, \"Сохраненные фильмы\", -1)),\n      (_ctx.loading)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[1] || (_cache[1] = [\n            _createElementVNode(\"div\", { class: \"loading-spinner large\" }, null, -1),\n            _createElementVNode(\"p\", null, \"Загрузка закладок...\", -1)\n          ])))\n        : (_ctx.error)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n              _createElementVNode(\"p\", null, _toDisplayString(_ctx.error), 1),\n              _createElementVNode(\"button\", {\n                onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.fetchBookmarks && _ctx.fetchBookmarks(...args))),\n                class: \"retry-button\"\n              }, \"Повторить\")\n            ]))\n          : (_ctx.bookmarks.length === 0)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                _cache[3] || (_cache[3] = _createElementVNode(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  \"stroke-width\": \"2\",\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  class: \"empty-icon\"\n                }, [\n                  _createElementVNode(\"path\", { d: \"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\" })\n                ], -1)),\n                _cache[4] || (_cache[4] = _createElementVNode(\"p\", null, \"У вас пока нет закладок\", -1)),\n                _createVNode(_component_router_link, {\n                  to: \"/catalog\",\n                  class: \"browse-button\"\n                }, {\n                  default: _withCtx(() => _cache[2] || (_cache[2] = [\n                    _createTextVNode(\"Перейти в каталог\")\n                  ])),\n                  _: 1\n                })\n              ]))\n            : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n                (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.bookmarks, (bookmark) => {\n                  return (_openBlock(), _createElementBlock(\"div\", {\n                    key: bookmark._id,\n                    class: \"bookmark-item\"\n                  }, [\n                    (bookmark.details)\n                      ? (_openBlock(), _createBlock(_component_MovieCard, {\n                          key: 0,\n                          movie: bookmark.details,\n                          isInBookmarksView: true\n                        }, null, 8, [\"movie\"]))\n                      : (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [\n                          _cache[5] || (_cache[5] = _createElementVNode(\"p\", null, \"Не удалось загрузить информацию о фильме\", -1)),\n                          _createElementVNode(\"button\", {\n                            onClick: ($event: any) => (_ctx.removeBookmark(bookmark._id)),\n                            class: \"remove-button\"\n                          }, \"Удалить закладку\", 8, _hoisted_8)\n                        ]))\n                  ]))\n                }), 128))\n              ]))\n    ])\n  ]))\n}", "<template>\n  <div class=\"bookmarks-view\">\n    <div class=\"container\">\n      <h1 class=\"page-title\">Закладки</h1>\n      <p class=\"page-description\">Сохраненные фильмы</p>\n\n      <div v-if=\"loading\" class=\"loading-container\">\n        <div class=\"loading-spinner large\"></div>\n        <p>Загрузка закладок...</p>\n      </div>\n\n      <div v-else-if=\"error\" class=\"error-container\">\n        <p>{{ error }}</p>\n        <button @click=\"fetchBookmarks\" class=\"retry-button\">Повторить</button>\n      </div>\n\n      <div v-else-if=\"bookmarks.length === 0\" class=\"empty-container\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"empty-icon\">\n          <path d=\"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\"></path>\n        </svg>\n        <p>У вас пока нет закладок</p>\n        <router-link to=\"/catalog\" class=\"browse-button\">Перейти в каталог</router-link>\n      </div>\n\n      <div v-else class=\"bookmarks-list\">\n        <div v-for=\"bookmark in bookmarks\" :key=\"bookmark._id\" class=\"bookmark-item\">\n          <MovieCard\n            v-if=\"bookmark.details\"\n            :movie=\"bookmark.details\"\n            :isInBookmarksView=\"true\"\n          />\n          <div v-else class=\"bookmark-error\">\n            <p>Не удалось загрузить информацию о фильме</p>\n            <button @click=\"removeBookmark(bookmark._id)\" class=\"remove-button\">Удалить закладку</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, onMounted, computed } from 'vue';\nimport MovieCard from '@/components/MovieCard.vue';\nimport { useBookmarksStore } from '@/store/bookmarks';\n\nexport default defineComponent({\n  name: 'BookmarksView',\n  components: {\n    MovieCard\n  },\n  setup() {\n    const bookmarksStore = useBookmarksStore();\n\n    // Computed properties\n    const bookmarks = computed(() => bookmarksStore.bookmarks);\n    const loading = computed(() => bookmarksStore.loading);\n    const error = computed(() => bookmarksStore.error);\n\n    // Fetch bookmarks on mount\n    const fetchBookmarks = async () => {\n      await bookmarksStore.fetchBookmarks();\n    };\n\n    // Remove bookmark\n    const removeBookmark = async (id: string) => {\n      await bookmarksStore.deleteBookmark(id);\n    };\n\n    onMounted(() => {\n      fetchBookmarks();\n    });\n\n    return {\n      bookmarks,\n      loading,\n      error,\n      fetchBookmarks,\n      removeBookmark\n    };\n  }\n});\n</script>\n\n<style scoped>\n.bookmarks-view {\n  padding-bottom: 80px;\n}\n\n.page-title {\n  font-size: var(--font-size-xl);\n  font-weight: bold;\n  margin-bottom: var(--spacing-xs);\n  text-align: center;\n}\n\n.page-description {\n  color: var(--tg-theme-hint-color);\n  text-align: center;\n  margin-bottom: var(--spacing-lg);\n}\n\n.loading-container, .error-container, .empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-xl) 0;\n  color: var(--tg-theme-hint-color);\n}\n\n.loading-spinner {\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  border: 2px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top-color: var(--tg-theme-button-color);\n  animation: spin 1s ease-in-out infinite;\n  margin-bottom: var(--spacing-md);\n}\n\n.loading-spinner.large {\n  width: 48px;\n  height: 48px;\n  border-width: 3px;\n}\n\n.empty-icon {\n  width: 48px;\n  height: 48px;\n  color: var(--tg-theme-hint-color);\n  margin-bottom: var(--spacing-md);\n}\n\n.retry-button, .browse-button {\n  margin-top: var(--spacing-md);\n  background-color: var(--tg-theme-button-color);\n  color: var(--tg-theme-button-text-color);\n  border: none;\n  border-radius: var(--border-radius);\n  padding: var(--spacing-sm) var(--spacing-md);\n  font-size: var(--font-size-md);\n  cursor: pointer;\n  text-decoration: none;\n  display: inline-block;\n}\n\n.bookmark-error {\n  background-color: var(--tg-theme-secondary-bg-color);\n  border-radius: var(--border-radius);\n  padding: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n  text-align: center;\n}\n\n.remove-button {\n  margin-top: var(--spacing-sm);\n  background-color: var(--tg-theme-destructive-text-color, #ff3b30);\n  color: white;\n  border: none;\n  border-radius: var(--border-radius);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  font-size: var(--font-size-sm);\n  cursor: pointer;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n</style>\n", "import { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"movie-poster\" }\nconst _hoisted_2 = [\"src\", \"alt\"]\nconst _hoisted_3 = {\n  key: 1,\n  class: \"poster-placeholder\"\n}\nconst _hoisted_4 = { class: \"movie-year\" }\nconst _hoisted_5 = {\n  key: 2,\n  class: \"movie-rating\"\n}\nconst _hoisted_6 = { class: \"movie-info\" }\nconst _hoisted_7 = { class: \"movie-title\" }\nconst _hoisted_8 = { class: \"movie-original-title\" }\nconst _hoisted_9 = { class: \"movie-actions\" }\nconst _hoisted_10 = [\"fill\"]\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  return (_openBlock(), _createElementBlock(\"div\", {\n    class: \"movie-card\",\n    onClick: _cache[1] || (_cache[1] = \n//@ts-ignore\n(...args) => (_ctx.navigateToMovie && _ctx.navigateToMovie(...args)))\n  }, [\n    _createElementVNode(\"div\", _hoisted_1, [\n      (_ctx.posterUrl)\n        ? (_openBlock(), _createElementBlock(\"img\", {\n            key: 0,\n            src: _ctx.posterUrl,\n            alt: _ctx.movie.name_rus,\n            class: \"poster-image\"\n          }, null, 8, _hoisted_2))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[2] || (_cache[2] = [\n            _createStaticVNode(\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"1\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" data-v-2f2b267e><rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"20\\\" rx=\\\"2.18\\\" ry=\\\"2.18\\\" data-v-2f2b267e></rect><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\" data-v-2f2b267e></circle><line x1=\\\"8\\\" y1=\\\"4\\\" x2=\\\"16\\\" y2=\\\"4\\\" data-v-2f2b267e></line><line x1=\\\"4\\\" y1=\\\"8\\\" x2=\\\"4\\\" y2=\\\"16\\\" data-v-2f2b267e></line><line x1=\\\"20\\\" y1=\\\"8\\\" x2=\\\"20\\\" y2=\\\"16\\\" data-v-2f2b267e></line><line x1=\\\"8\\\" y1=\\\"20\\\" x2=\\\"16\\\" y2=\\\"20\\\" data-v-2f2b267e></line></svg>\", 1)\n          ]))),\n      _createElementVNode(\"div\", _hoisted_4, _toDisplayString(_ctx.movie.year), 1),\n      (_ctx.movie.kp_rating || _ctx.movie.imdb_rating)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _toDisplayString(_ctx.movie.kp_rating || _ctx.movie.imdb_rating), 1))\n        : _createCommentVNode(\"\", true)\n    ]),\n    _createElementVNode(\"div\", _hoisted_6, [\n      _createElementVNode(\"h3\", _hoisted_7, _toDisplayString(_ctx.movie.name_rus), 1),\n      _createElementVNode(\"p\", _hoisted_8, _toDisplayString(_ctx.movie.name_original), 1),\n      _createElementVNode(\"div\", _hoisted_9, [\n        _createElementVNode(\"button\", {\n          class: \"bookmark-button\",\n          onClick: _cache[0] || (_cache[0] = _withModifiers(\n//@ts-ignore\n(...args) => (_ctx.toggleBookmark && _ctx.toggleBookmark(...args)), [\"stop\"]))\n        }, [\n          (_openBlock(), _createElementBlock(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: _ctx.isBookmarked ? 'currentColor' : 'none',\n            stroke: \"currentColor\",\n            \"stroke-width\": \"2\",\n            \"stroke-linecap\": \"round\",\n            \"stroke-linejoin\": \"round\"\n          }, _cache[3] || (_cache[3] = [\n            _createElementVNode(\"path\", { d: \"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\" }, null, -1)\n          ]), 8, _hoisted_10))\n        ])\n      ])\n    ])\n  ]))\n}", "<template>\n  <div class=\"movie-card\" @click=\"navigateToMovie\">\n    <div class=\"movie-poster\">\n      <img v-if=\"posterUrl\" :src=\"posterUrl\" :alt=\"movie.name_rus\" class=\"poster-image\">\n      <div v-else class=\"poster-placeholder\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" rx=\"2.18\" ry=\"2.18\"></rect>\n          <circle cx=\"12\" cy=\"12\" r=\"4\"></circle>\n          <line x1=\"8\" y1=\"4\" x2=\"16\" y2=\"4\"></line>\n          <line x1=\"4\" y1=\"8\" x2=\"4\" y2=\"16\"></line>\n          <line x1=\"20\" y1=\"8\" x2=\"20\" y2=\"16\"></line>\n          <line x1=\"8\" y1=\"20\" x2=\"16\" y2=\"20\"></line>\n        </svg>\n      </div>\n      <div class=\"movie-year\">{{ movie.year }}</div>\n      <div v-if=\"movie.kp_rating || movie.imdb_rating\" class=\"movie-rating\">\n        {{ movie.kp_rating || movie.imdb_rating }}\n      </div>\n    </div>\n    <div class=\"movie-info\">\n      <h3 class=\"movie-title\">{{ movie.name_rus }}</h3>\n      <p class=\"movie-original-title\">{{ movie.name_original }}</p>\n      <div class=\"movie-actions\">\n        <button class=\"bookmark-button\" @click.stop=\"toggleBookmark\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" :fill=\"isBookmarked ? 'currentColor' : 'none'\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <path d=\"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\"></path>\n          </svg>\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, computed, ref } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { useBookmarksStore } from '@/store/bookmarks';\nimport { getMovieSource, getMovieId, getMoviePosterUrl } from '@/utils/movieUtils';\n\nexport default defineComponent({\n  name: 'MovieCard',\n  props: {\n    movie: {\n      type: Object,\n      required: true\n    },\n    isInBookmarksView: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup(props) {\n    const router = useRouter();\n    const bookmarksStore = useBookmarksStore();\n    const loading = ref(false);\n\n    // Determine source and ID for the movie using utility functions\n    const source = computed(() => getMovieSource(props.movie));\n    const movieId = computed(() => getMovieId(props.movie));\n\n    // Check if movie is bookmarked\n    const isBookmarked = computed(() => {\n      // Если мы на странице закладок, то фильм точно в закладках\n      if (props.isInBookmarksView) return true;\n\n      // Иначе проверяем через store\n      if (!source.value || !movieId.value) return false;\n      return bookmarksStore.isBookmarked(movieId.value, source.value);\n    });\n\n    // Generate poster URL from movie data using utility function\n    const posterUrl = computed(() => getMoviePosterUrl(props.movie));\n\n    // Navigate to movie details\n    const navigateToMovie = () => {\n      if (source.value && movieId.value) {\n        router.push(`/movie/${source.value}/${movieId.value}`);\n      }\n    };\n\n    // Toggle bookmark\n    const toggleBookmark = async (event: Event) => {\n      event.stopPropagation();\n\n      if (!source.value || !movieId.value) return;\n\n      loading.value = true;\n\n      try {\n        // Если мы на странице закладок, то можем только удалить закладку\n        if (props.isInBookmarksView) {\n          const bookmarkId = bookmarksStore.getBookmarkId(movieId.value, source.value);\n          if (bookmarkId) {\n            await bookmarksStore.deleteBookmark(bookmarkId);\n          }\n        } else {\n          // На других страницах можем как добавлять, так и удалять\n          if (isBookmarked.value) {\n            const bookmarkId = bookmarksStore.getBookmarkId(movieId.value, source.value);\n            if (bookmarkId) {\n              await bookmarksStore.deleteBookmark(bookmarkId);\n            }\n          } else {\n            await bookmarksStore.addBookmark(movieId.value, source.value);\n          }\n        }\n      } catch (error) {\n        console.error('Error toggling bookmark:', error);\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    return {\n      source,\n      movieId,\n      isBookmarked,\n      posterUrl,\n      navigateToMovie,\n      toggleBookmark,\n      loading\n    };\n  }\n});\n</script>\n\n<style scoped>\n.movie-card {\n  display: flex;\n  background-color: var(--tg-theme-secondary-bg-color);\n  border-radius: var(--border-radius);\n  overflow: hidden;\n  margin-bottom: var(--spacing-md);\n  box-shadow: var(--shadow-sm);\n  cursor: pointer;\n  transition: transform 0.2s, box-shadow 0.2s;\n}\n\n.movie-card:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-md);\n}\n\n.movie-poster {\n  position: relative;\n  width: 100px;\n  min-width: 100px;\n  height: 150px;\n  background-color: rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.poster-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.poster-placeholder {\n  width: 50px;\n  height: 50px;\n  color: var(--tg-theme-hint-color);\n}\n\n.movie-year {\n  position: absolute;\n  top: 8px;\n  left: 8px;\n  background-color: rgba(0, 0, 0, 0.7);\n  color: white;\n  font-size: 12px;\n  padding: 2px 6px;\n  border-radius: 4px;\n}\n\n.movie-rating {\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  background-color: rgba(255, 193, 7, 0.9);\n  color: black;\n  font-size: 12px;\n  font-weight: bold;\n  padding: 2px 6px;\n  border-radius: 4px;\n}\n\n.movie-info {\n  flex: 1;\n  padding: var(--spacing-sm);\n  display: flex;\n  flex-direction: column;\n}\n\n.movie-title {\n  font-size: var(--font-size-md);\n  font-weight: bold;\n  margin: 0 0 4px 0;\n}\n\n.movie-original-title {\n  font-size: var(--font-size-sm);\n  color: var(--tg-theme-hint-color);\n  margin: 0 0 auto 0;\n}\n\n.movie-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: var(--spacing-sm);\n}\n\n.bookmark-button {\n  background: none;\n  border: none;\n  color: var(--tg-theme-button-color);\n  width: 24px;\n  height: 24px;\n  padding: 0;\n  cursor: pointer;\n}\n\n.bookmark-button svg {\n  width: 100%;\n  height: 100%;\n}\n</style>\n", "import { render } from \"./MovieCard.vue?vue&type=template&id=2f2b267e&scoped=true&ts=true\"\nimport script from \"./MovieCard.vue?vue&type=script&lang=ts\"\nexport * from \"./MovieCard.vue?vue&type=script&lang=ts\"\n\nimport \"./MovieCard.vue?vue&type=style&index=0&id=2f2b267e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-2f2b267e\"]])\n\nexport default __exports__", "import { render } from \"./BookmarksView.vue?vue&type=template&id=5fc778cc&scoped=true&ts=true\"\nimport script from \"./BookmarksView.vue?vue&type=script&lang=ts\"\nexport * from \"./BookmarksView.vue?vue&type=script&lang=ts\"\n\nimport \"./BookmarksView.vue?vue&type=style&index=0&id=5fc778cc&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-5fc778cc\"]])\n\nexport default __exports__"], "names": ["getMovieSource", "movie", "kp_id", "imdb_id", "getMovieId", "source", "formatMovieType", "type", "types", "tv", "anime", "getMoviePosterUrl", "poster_urls", "w500", "poster_url", "useBookmarksStore", "defineStore", "state", "bookmarks", "loading", "error", "lastFetchTime", "actions", "fetchBookmarks", "forceRefresh", "now", "Date", "fiveMinutes", "this", "length", "response", "api", "get", "data", "success", "message", "console", "addBookmark", "movie_id", "post", "movieDetails", "movieResponse", "unshift", "_id", "details", "movieError", "deleteBookmark", "id", "delete", "filter", "bookmark", "isBookmarked", "some", "getBookmarkId", "find", "_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "key", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_router_link", "_resolveComponent", "_component_MovieCard", "_openBlock", "_createElementBlock", "_createElementVNode", "_toDisplayString", "onClick", "args", "xmlns", "viewBox", "fill", "stroke", "d", "_createVNode", "to", "default", "_withCtx", "_createTextVNode", "_", "_Fragment", "_renderList", "_createBlock", "isInBookmarksView", "$event", "removeBookmark", "_hoisted_9", "_hoisted_10", "navigateToMovie", "posterUrl", "src", "alt", "name_rus", "_createStaticVNode", "year", "kp_rating", "imdb_rating", "_createCommentVNode", "name_original", "_withModifiers", "toggleBookmark", "defineComponent", "name", "props", "Object", "required", "Boolean", "setup", "router", "useRouter", "bookmarksStore", "ref", "computed", "movieId", "value", "push", "async", "event", "stopPropagation", "bookmarkId", "__exports__", "components", "MovieCard", "onMounted"], "sourceRoot": ""}